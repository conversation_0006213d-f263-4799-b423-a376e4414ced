package com.ruoyi.guaranty_info.service;

import java.util.List;
import com.ruoyi.guaranty_info.domain.GuarantyInfo;

/**
 * 抵押登记Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface IGuarantyInfoService 
{
    /**
     * 查询抵押登记
     * 
     * @param id 抵押登记主键
     * @return 抵押登记
     */
    public GuarantyInfo selectGuarantyInfoById(String id);

    /**
     * 查询抵押登记列表
     * 
     * @param guarantyInfo 抵押登记
     * @return 抵押登记集合
     */
    public List<GuarantyInfo> selectGuarantyInfoList(GuarantyInfo guarantyInfo);

    /**
     * 新增抵押登记
     * 
     * @param guarantyInfo 抵押登记
     * @return 结果
     */
    public int insertGuarantyInfo(GuarantyInfo guarantyInfo);

    /**
     * 修改抵押登记
     * 
     * @param guarantyInfo 抵押登记
     * @return 结果
     */
    public int updateGuarantyInfo(GuarantyInfo guarantyInfo);

    /**
     * 批量删除抵押登记
     * 
     * @param ids 需要删除的抵押登记主键集合
     * @return 结果
     */
    public int deleteGuarantyInfoByIds(String[] ids);

    /**
     * 删除抵押登记信息
     * 
     * @param id 抵押登记主键
     * @return 结果
     */
    public int deleteGuarantyInfoById(String id);
}
