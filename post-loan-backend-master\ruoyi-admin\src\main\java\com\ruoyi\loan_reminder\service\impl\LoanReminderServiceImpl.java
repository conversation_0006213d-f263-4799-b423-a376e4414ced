package com.ruoyi.loan_reminder.service.impl;

import java.util.List;
import com.ruoyi.account_loan.service.IAccountLoanService;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.loan_reminder.mapper.LoanReminderMapper;
import com.ruoyi.loan_reminder.domain.LoanReminder;
import com.ruoyi.loan_reminder.service.ILoanReminderService;

/**
 * 催记Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
public class LoanReminderServiceImpl implements ILoanReminderService {
    @Autowired
    private LoanReminderMapper loanReminderMapper;

    @Autowired
    private IAccountLoanService accountLoanService;

    /**
     * 查询催记
     * 
     * @param id 催记主键
     * @return 催记
     */
    @Override
    public LoanReminder selectLoanReminderById(String id) {
        return loanReminderMapper.selectLoanReminderById(id);
    }

    /**
     * 查询催记列表
     * 
     * @param loanReminder 催记
     * @return 催记
     */
    @Override
    public List<LoanReminder> selectLoanReminderList(LoanReminder loanReminder) {
        return loanReminderMapper.selectLoanReminderList(loanReminder);
    }

    /**
     * 查询催记详情
     * 
     * @param loanReminder 催记
     * @return 催记详情
     */
    @Override
    public LoanReminder selectLoanReminderListDetail(LoanReminder loanReminder) {
        return loanReminderMapper.selectLoanReminderListDetail(loanReminder);
    }

    /**
     * 新增催记
     * 
     * @param loanReminder 催记
     * @return 结果
     */
    @Override
    public int insertLoanReminder(LoanReminder loanReminder) {
        loanReminder.setCreateTime(DateUtils.getNowDate());
        return loanReminderMapper.insertLoanReminder(loanReminder);
    }

    /**
     * 修改催记
     * 
     * @param loanReminder 催记
     * @return 结果
     */
    @Override
    public int updateLoanReminder(LoanReminder loanReminder) {
        loanReminder.setUpdateTime(DateUtils.getNowDate());
        return loanReminderMapper.updateLoanReminder(loanReminder);
    }

    /**
     * 批量删除催记
     * 
     * @param ids 需要删除的催记主键
     * @return 结果
     */
    @Override
    public int deleteLoanReminderByIds(String[] ids) {
        return loanReminderMapper.deleteLoanReminderByIds(ids);
    }

    /**
     * 删除催记信息
     * 
     * @param id 催记主键
     * @return 结果
     */
    @Override
    public int deleteLoanReminderById(String id) {
        return loanReminderMapper.deleteLoanReminderById(id);
    }

    /**
     * 审核催记
     * 
     * @param loanReminder 催记审核信息
     * @return 结果
     */
    @Override
    public int approveLoanReminder(LoanReminder loanReminder) {
        loanReminder.setUpdateTime(DateUtils.getNowDate());
        // 只更新与审核相关的字段
        return loanReminderMapper.updateLoanReminderApproval(loanReminder);
    }

    /**
     * 获取贷款的贷后还款状态
     * 
     * @param loanId 贷款ID
     * @return 贷后还款状态
     */
    @Override
    public Integer getAccountLoanRepaymentStatus(Long loanId) {
        return accountLoanService.getRepaymentStatusByLoanId(loanId);
    }

    /**
     * 更新贷款的贷后还款状态
     * 
     * @param loanId          贷款ID
     * @param repaymentStatus 贷后还款状态
     * @return 结果
     */
    @Override
    public int updateAccountLoanRepaymentStatus(Long loanId, Integer repaymentStatus) {
        return accountLoanService.updateRepaymentStatus(loanId, repaymentStatus);
    }

    @Override
    public LoanReminder selectLatestLoanReminderByLitigationId(Long litigationId) {
        return loanReminderMapper.selectLatestLoanReminderByLitigationId(litigationId);
    }

    /**
     * 获取产品列表
     * @return 产品列表
     */
    @Override
    public List<java.util.Map<String, Object>> getProductList() {
        return loanReminderMapper.selectProductList();
    }
}
