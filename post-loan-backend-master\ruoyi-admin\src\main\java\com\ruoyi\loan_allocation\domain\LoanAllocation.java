package com.ruoyi.loan_allocation.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 流程跟催员分配对象 loan_allocation
 * 
 * <AUTHOR>
 * @date 2025-06-21
 */
public class LoanAllocation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private String id;

    /** 流程id */
    @Excel(name = "流程id")
    private Long loanId;

    /** 分配人id */
    @Excel(name = "分配人id")
    private Long assignPerson;

    /** 跟催员id */
    @Excel(name = "跟催员id")
    private String followUp;

    /** 跟催员身份 */
    @Excel(name = "跟催员身份")
    private String followRole;

    /** 跟催状态，1-跟催中，2-结束 */
    @Excel(name = "跟催状态，1-跟催中，2-结束")
    private Long status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setLoanId(Long loanId) 
    {
        this.loanId = loanId;
    }

    public Long getLoanId() 
    {
        return loanId;
    }

    public void setAssignPerson(Long assignPerson) 
    {
        this.assignPerson = assignPerson;
    }

    public Long getAssignPerson() 
    {
        return assignPerson;
    }

    public void setFollowUp(String followUp)
    {
        this.followUp = followUp;
    }

    public String getFollowUp()
    {
        return followUp;
    }

    public void setFollowRole(String followRole) 
    {
        this.followRole = followRole;
    }

    public String getFollowRole() 
    {
        return followRole;
    }

    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("loanId", getLoanId())
            .append("assignPerson", getAssignPerson())
            .append("followUp", getFollowUp())
            .append("followRole", getFollowRole())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
