package com.ruoyi.loan_list.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.loan_list.domain.LoanList;
import com.ruoyi.loan_list.service.ILoanListService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.loan_list.domain.BatchUpdateUrgeUserRequest;
import com.ruoyi.loan_list.domain.BatchAssignPetitionUserRequest;

/**
 * 贷后逾期记录Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/loan_list/loan_list")
public class LoanListController extends BaseController {
    @Autowired
    private ILoanListService loanListService;

    /**
     * 查询贷后逾期记录列表
     */
    @PreAuthorize("@ss.hasPermi('loan_list:loan_list:list')")
    @GetMapping("/list")
    public TableDataInfo list(LoanList loanList) {
        startPage();
        loanList.setStatus("1");
        List<LoanList> list = loanListService.selectLoanListList(loanList);
        return getDataTable(list);
    }

    /**
     * 查询贷后逾期跟踪列表
     */
    @PreAuthorize("@ss.hasPermi('loan_list:loan_list:list')")
    @GetMapping("/loan_list")
    public TableDataInfo loan_list(LoanList loanList) {
        startPage();
        loanList.setStatus("1");
        List<LoanList> list = loanListService.selectLoanListList(loanList);
        return getDataTable(list);
    }

    /**
     * 导出贷后逾期记录列表
     */
    @PreAuthorize("@ss.hasPermi('loan_list:loan_list:export')")
    @Log(title = "贷后逾期记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LoanList loanList) {
        List<LoanList> list = loanListService.selectLoanListList(loanList);
        ExcelUtil<LoanList> util = new ExcelUtil<LoanList>(LoanList.class);
        util.exportExcel(response, list, "贷后逾期记录数据");
    }

    /**
     * 获取贷后逾期记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('loan_list:loan_list:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(loanListService.selectLoanListById(id));
    }

    /**
     * 新增贷后逾期记录
     */
    @PreAuthorize("@ss.hasPermi('loan_list:loan_list:add')")
    @Log(title = "贷后逾期记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LoanList loanList) {
        return toAjax(loanListService.insertLoanList(loanList));
    }

    /**
     * 修改贷后逾期记录
     */
    @PreAuthorize("@ss.hasPermi('loan_list:loan_list:edit')")
    @Log(title = "贷后逾期记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LoanList loanList) {
        return toAjax(loanListService.updateLoanList(loanList));
    }

    /**
     * 删除贷后逾期记录
     */
    @PreAuthorize("@ss.hasPermi('loan_list:loan_list:remove')")
    @Log(title = "贷后逾期记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(loanListService.deleteLoanListByIds(ids));
    }

    /**
     * 批量更新催收人
     */
    @PreAuthorize("@ss.hasPermi('loan_list:loan_list:edit')")
    @Log(title = "贷后逾期记录", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUpdateUrgeUser")
    public AjaxResult batchUpdateUrgeUser(@RequestBody BatchUpdateUrgeUserRequest request) {
        int rows = loanListService.batchUpdateUrgeUser(request.getIds(), request.getUrgeUser());
        return toAjax(rows);
    }

    @PreAuthorize("@ss.hasPermi('loan_list:loan_list:edit')")
    @Log(title = "贷后逾期记录", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAssignPetitionUser")
    public AjaxResult batchAssignPetitionUser(@RequestBody BatchAssignPetitionUserRequest request) {
        int rows = loanListService.batchAssignPetitionUser(request.getIds(), request.getPetitionUser());
        if (rows == 0) {
            return AjaxResult.error("只有在上访指派状态为未指派或已撤销时才可指派，存在已指派的数据，操作已取消");
        }
        return toAjax(rows);
    }

    @PreAuthorize("@ss.hasPermi('loan_list:loan_list:edit')")
    @Log(title = "贷后逾期记录", businessType = BusinessType.UPDATE)
    @PutMapping("/revokePetitionUser/{id}")
    public AjaxResult revokePetitionUser(@PathVariable String id) {
        int rows = loanListService.revokePetitionUser(id);
        return toAjax(rows);
    }
}
