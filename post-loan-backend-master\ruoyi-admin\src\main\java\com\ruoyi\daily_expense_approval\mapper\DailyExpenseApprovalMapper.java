package com.ruoyi.daily_expense_approval.mapper;

import java.util.List;
import com.ruoyi.daily_expense_approval.domain.DailyExpenseApproval;

/**
 * 日常花费审批Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface DailyExpenseApprovalMapper 
{
    /**
     * 查询日常花费审批
     * 
     * @param id 日常花费审批主键
     * @return 日常花费审批
     */
    public DailyExpenseApproval selectDailyExpenseApprovalById(Long id);

    /**
     * 查询日常花费审批列表
     * 
     * @param dailyExpenseApproval 日常花费审批
     * @return 日常花费审批集合
     */
    public List<DailyExpenseApproval> selectDailyExpenseApprovalList(DailyExpenseApproval dailyExpenseApproval);

    /**
     * 新增日常花费审批
     * 
     * @param dailyExpenseApproval 日常花费审批
     * @return 结果
     */
    public int insertDailyExpenseApproval(DailyExpenseApproval dailyExpenseApproval);

    /**
     * 修改日常花费审批
     * 
     * @param dailyExpenseApproval 日常花费审批
     * @return 结果
     */
    public int updateDailyExpenseApproval(DailyExpenseApproval dailyExpenseApproval);

    /**
     * 删除日常花费审批
     * 
     * @param id 日常花费审批主键
     * @return 结果
     */
    public int deleteDailyExpenseApprovalById(Long id);

    /**
     * 批量删除日常花费审批
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDailyExpenseApprovalByIds(Long[] ids);
}
