package com.ruoyi.loan_reminder.service;

import java.util.List;
import com.ruoyi.loan_reminder.domain.LoanReminder;

/**
 * 催记Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface ILoanReminderService {
    /**
     * 查询催记
     * 
     * @param id 催记主键
     * @return 催记
     */
    public LoanReminder selectLoanReminderById(String id);

    /**
     * 查询催记列表
     * 
     * @param loanReminder 催记
     * @return 催记集合
     */
    public List<LoanReminder> selectLoanReminderList(LoanReminder loanReminder);

    /**
     * 查询催记详情
     * 
     * @param loanReminder 催记
     * @return 催记详情
     */
    public LoanReminder selectLoanReminderListDetail(LoanReminder loanReminder);

    /**
     * 新增催记
     * 
     * @param loanReminder 催记
     * @return 结果
     */
    public int insertLoanReminder(LoanReminder loanReminder);

    /**
     * 修改催记
     * 
     * @param loanReminder 催记
     * @return 结果
     */
    public int updateLoanReminder(LoanReminder loanReminder);

    /**
     * 批量删除催记
     * 
     * @param ids 需要删除的催记主键集合
     * @return 结果
     */
    public int deleteLoanReminderByIds(String[] ids);

    /**
     * 删除催记信息
     * 
     * @param id 催记主键
     * @return 结果
     */
    public int deleteLoanReminderById(String id);

    /**
     * 审核催记
     * 
     * @param loanReminder 催记审核信息
     * @return 结果
     */
    public int approveLoanReminder(LoanReminder loanReminder);

    /**
     * 获取贷款的贷后还款状态
     * 
     * @param loanId 贷款ID
     * @return 贷后还款状态
     */
    public Integer getAccountLoanRepaymentStatus(Long loanId);

    /**
     * 更新贷款的贷后还款状态
     * 
     * @param loanId          贷款ID
     * @param repaymentStatus 贷后还款状态
     * @return 结果
     */
    public int updateAccountLoanRepaymentStatus(Long loanId, Integer repaymentStatus);

    /**
     * 查询某法诉案件的最新一条催记记录
     * @param litigationId 法诉案件ID
     * @return 最新的催记
     */
    public LoanReminder selectLatestLoanReminderByLitigationId(Long litigationId);

    /**
     * 获取产品列表
     * @return 产品列表
     */
    public List<java.util.Map<String, Object>> getProductList();
}
