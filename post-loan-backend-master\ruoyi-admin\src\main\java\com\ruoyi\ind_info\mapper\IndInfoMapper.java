package com.ruoyi.ind_info.mapper;

import java.util.List;
import com.ruoyi.ind_info.domain.IndInfo;

/**
 * 个人客户信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IndInfoMapper 
{
    /**
     * 查询个人客户信息
     * 
     * @param id 个人客户信息主键
     * @return 个人客户信息
     */
    public IndInfo selectIndInfoById(String id);

    /**
     * 查询个人客户信息列表
     * 
     * @param indInfo 个人客户信息
     * @return 个人客户信息集合
     */
    public List<IndInfo> selectIndInfoList(IndInfo indInfo);

    /**
     * 新增个人客户信息
     * 
     * @param indInfo 个人客户信息
     * @return 结果
     */
    public int insertIndInfo(IndInfo indInfo);

    /**
     * 修改个人客户信息
     * 
     * @param indInfo 个人客户信息
     * @return 结果
     */
    public int updateIndInfo(IndInfo indInfo);

    /**
     * 删除个人客户信息
     * 
     * @param id 个人客户信息主键
     * @return 结果
     */
    public int deleteIndInfoById(String id);

    /**
     * 批量删除个人客户信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIndInfoByIds(String[] ids);
}
