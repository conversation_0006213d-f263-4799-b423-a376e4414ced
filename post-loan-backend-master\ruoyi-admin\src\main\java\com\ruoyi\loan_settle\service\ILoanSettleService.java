package com.ruoyi.loan_settle.service;

import java.util.List;
import com.ruoyi.loan_settle.domain.LoanSettle;
import com.ruoyi.common.core.domain.AjaxResult;

/**
 * 流程结清Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface ILoanSettleService {
    /**
     * 查询流程结清
     * 
     * @param id 流程结清主键
     * @return 流程结清
     */
    public LoanSettle selectLoanSettleById(String id);

    /**
     * 查询流程结清列表
     * 
     * @param loanSettle 流程结清
     * @return 流程结清集合
     */
    public List<LoanSettle> selectLoanSettleList(LoanSettle loanSettle);

    public LoanSettle selectLoanSettleListDetail(LoanSettle loanSettle);

    /**
     * 新增流程结清
     * 
     * @param loanSettle 流程结清
     * @return 结果
     */
    public int insertLoanSettle(LoanSettle loanSettle);

    /**
     * 修改流程结清
     * 
     * @param loanSettle 流程结清
     * @return 结果
     */
    public int updateLoanSettle(LoanSettle loanSettle);

    /**
     * 批量删除流程结清
     * 
     * @param ids 需要删除的流程结清主键集合
     * @return 结果
     */
    public int deleteLoanSettleByIds(String[] ids);

    /**
     * 删除流程结清信息
     * 
     * @param id 流程结清主键
     * @return 结果
     */
    public int deleteLoanSettleById(String id);

    /**
     * 代偿结清功能（支持全额/减免）
     * @param loanId 贷款ID
     * @param status 结清类型 1-全额 2-减免
     * @return 结果
     */
    AjaxResult processCompensationSettlement(Long loanId, Integer status);

    /**
     * 判断该贷款是否有正在处理的结清流程（非拒绝状态）
     * @param loanId 贷款ID
     * @return true-有正在处理的结清，false-可以发起结清
     */
    boolean hasProcessingSettle(Long loanId);

    /**
     * 查询该贷款下所有未被拒绝的结清流程
     * @param loanId 贷款ID
     * @return 未被拒绝的结清流程列表
     */
    List<LoanSettle> selectProcessingSettlesByLoanId(Long loanId);
}
