<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.JJIndCarInfoMapper">
    
    <resultMap type="JJIndCarInfo" id="IndCarInfoResult">
        <result property="id"    column="id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="customerId"    column="customer_id"    />
        <result property="plateProvince"    column="plate_province"    />
        <result property="plateCity"    column="plate_city"    />
        <result property="plateAddress"    column="plate_address"    />
        <result property="mileage"    column="mileage"    />
        <result property="color"    column="color"    />
        <result property="nature"    column="nature"    />
        <result property="brandType"    column="brand_type"    />
        <result property="energy"    column="energy"    />
        <result property="regIssueDate"    column="reg_issue_date"    />
        <result property="registrationDate"    column="registration_date"    />
        <result property="registrateFlag"    column="registrate_flag"    />
        <result property="registrateReason"    column="registrate_reason"    />
        <result property="brandId"    column="brand_id"    />
        <result property="brandName"    column="brand_name"    />
        <result property="seriesId"    column="series_id"    />
        <result property="seriesName"    column="series_name"    />
        <result property="modelId"    column="model_id"    />
        <result property="modelName"    column="model_name"    />
        <result property="cityId"    column="city_id"    />
        <result property="cityName"    column="city_name"    />
        <result property="mileAge"    column="mile_age"    />
        <result property="regDate"    column="reg_date"    />
        <result property="modelPrice"    column="model_price"    />
        <result property="dealerPrice"    column="dealer_price"    />
        <result property="highDealerPrice"    column="high_dealer_price"    />
        <result property="individualPrice"    column="individual_price"    />
        <result property="dealerBuyPrice"    column="dealer_buy_price"    />
        <result property="individualLowSoldPrice"    column="individual_low_sold_price"    />
        <result property="dealerLowBuyPrice"    column="dealer_low_buy_price"    />
        <result property="dealerHighSoldPrice"    column="dealer_high_sold_price"    />
        <result property="dealerLowSoldPrice"    column="dealer_low_sold_price"    />
        <result property="url"    column="url"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="manProvince"    column="man_province"    />
        <result property="manCity"    column="man_city"    />
        <result property="manBorough"    column="man_borough"    />
        <result property="manAddress"    column="man_address"    />
        <result property="manDetailAddress"    column="man_detail_address"    />
        <result property="carProvince"    column="car_province"    />
        <result property="carCity"    column="car_city"    />
        <result property="carBorough"    column="car_borough"    />
        <result property="carAddress"    column="car_address"    />
        <result property="carDetailAddress"    column="car_detail_address"    />
        <result property="carLandType"    column="car_land_type"    />
        <result property="carStatus"    column="car_status"    />
        <result property="gpsStatus"    column="gps_status"    />
        <result property="carColor"    column="car_color"    />
        <result property="modelYear"    column="model_year"    />
        <result property="modelLiter"    column="model_liter"    />
        <result property="modelGear"    column="model_gear"    />
        <result property="modelEmissionStandard"    column="model_emission_standard"    />
        <result property="minRegYear"    column="min_reg_year"    />
        <result property="maxRegYear"    column="max_reg_year"    />
        <result property="seriesGroupName"    column="series_group_name"    />
        <result property="reportUrl"    column="report_url"    />
        <result property="vehicleType"    column="vehicle_type"    />
        <result property="seriesType"    column="series_type"    />
        <result property="carState"    column="car_state"    />
        <result property="jzgOrderNo"    column="jzg_order_no"    />
        <result property="jzgAssessmentPriceSold"    column="jzg_assessment_price_sold"    />
        <result property="jzgAssessmentPriceBuy"    column="jzg_assessment_price_buy"    />
        <result property="jzgManufacturerPrice"    column="jzg_manufacturer_price"    />
        <result property="jzgAssessmentReportPdf"    column="jzg_assessment_report_pdf"    />
        <result property="jzgStatus"    column="jzg_status"    />
        <result property="jzgStatusDes"    column="jzg_status_des"    />
        <result property="departureDate"    column="departure_date"    />
        <result property="transferCount"    column="transfer_count"    />
        <result property="insuranceCondition"    column="insurance_condition"    />
        <result property="registrationNo"    column="registration_no"    />
        <result property="orderNo"    column="order_no"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="orderReportUrl"    column="order_report_url"    />
        <result property="orderReportWeb"    column="order_report_web"    />
        <result property="beforePlateNo"    column="before_plate_no"    />
        <result property="manufacturer"    column="manufacturer"    />
        <result property="seatNumber"    column="seat_number"    />
        <result property="wxBrandCode"    column="wx_brand_code"    />
        <result property="wxBrandName"    column="wx_brand_name"    />
        <result property="wxSeriesCode"    column="wx_series_code"    />
        <result property="wxSeriesName"    column="wx_series_name"    />
        <result property="wxModelCode"    column="wx_model_code"    />
        <result property="wxModelName"    column="wx_model_name"    />
        <result property="wxGuidancePrice"    column="wx_guidance_price"    />
        <result property="wxAssessmentPrice"    column="wx_assessment_price"    />
        <result property="wxReportOrderNo"    column="wx_report_order_no"    />
        <result property="wxReportUrl"    column="wx_report_url"    />
        <result property="isAccident"    column="is_accident"    />
        <result property="avpId"    column="avp_id"    />
    </resultMap>

    <sql id="selectIndCarInfoVo">
        select * from ind_car_info
    </sql>

    <select id="selectIndCarInfoList" parameterType="JJIndCarInfo" resultMap="IndCarInfoResult">
        <include refid="selectIndCarInfoVo"/>
        <where>
            DATE(create_date) = CURDATE() or DATE(update_date) = CURDATE()
        </where>
    </select>
    
    <select id="selectIndCarInfoById" parameterType="String" resultMap="IndCarInfoResult">
        <include refid="selectIndCarInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertIndCarInfo" parameterType="JJIndCarInfo">
        insert into ind_car_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applyNo != null">apply_no,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="plateProvince != null">plate_province,</if>
            <if test="plateCity != null">plate_city,</if>
            <if test="plateAddress != null">plate_address,</if>
            <if test="mileage != null">mileage,</if>
            <if test="color != null">color,</if>
            <if test="nature != null">nature,</if>
            <if test="brandType != null">brand_type,</if>
            <if test="energy != null">energy,</if>
            <if test="regIssueDate != null">reg_issue_date,</if>
            <if test="registrationDate != null">registration_date,</if>
            <if test="registrateFlag != null">registrate_flag,</if>
            <if test="registrateReason != null">registrate_reason,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="brandName != null">brand_name,</if>
            <if test="seriesId != null">series_id,</if>
            <if test="seriesName != null">series_name,</if>
            <if test="modelId != null">model_id,</if>
            <if test="modelName != null">model_name,</if>
            <if test="cityId != null">city_id,</if>
            <if test="cityName != null">city_name,</if>
            <if test="mileAge != null">mile_age,</if>
            <if test="regDate != null">reg_date,</if>
            <if test="modelPrice != null">model_price,</if>
            <if test="dealerPrice != null">dealer_price,</if>
            <if test="highDealerPrice != null">high_dealer_price,</if>
            <if test="individualPrice != null">individual_price,</if>
            <if test="dealerBuyPrice != null">dealer_buy_price,</if>
            <if test="individualLowSoldPrice != null">individual_low_sold_price,</if>
            <if test="dealerLowBuyPrice != null">dealer_low_buy_price,</if>
            <if test="dealerHighSoldPrice != null">dealer_high_sold_price,</if>
            <if test="dealerLowSoldPrice != null">dealer_low_sold_price,</if>
            <if test="url != null">url,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="manProvince != null">man_province,</if>
            <if test="manCity != null">man_city,</if>
            <if test="manBorough != null">man_borough,</if>
            <if test="manAddress != null">man_address,</if>
            <if test="manDetailAddress != null">man_detail_address,</if>
            <if test="carProvince != null">car_province,</if>
            <if test="carCity != null">car_city,</if>
            <if test="carBorough != null">car_borough,</if>
            <if test="carAddress != null">car_address,</if>
            <if test="carDetailAddress != null">car_detail_address,</if>
            <if test="carLandType != null">car_land_type,</if>
            <if test="carStatus != null">car_status,</if>
            <if test="gpsStatus != null">gps_status,</if>
            <if test="carColor != null">car_color,</if>
            <if test="modelYear != null">model_year,</if>
            <if test="modelLiter != null">model_liter,</if>
            <if test="modelGear != null">model_gear,</if>
            <if test="modelEmissionStandard != null">model_emission_standard,</if>
            <if test="minRegYear != null">min_reg_year,</if>
            <if test="maxRegYear != null">max_reg_year,</if>
            <if test="seriesGroupName != null">series_group_name,</if>
            <if test="reportUrl != null">report_url,</if>
            <if test="vehicleType != null">vehicle_type,</if>
            <if test="seriesType != null">series_type,</if>
            <if test="carState != null">car_state,</if>
            <if test="jzgOrderNo != null">jzg_order_no,</if>
            <if test="jzgAssessmentPriceSold != null">jzg_assessment_price_sold,</if>
            <if test="jzgAssessmentPriceBuy != null">jzg_assessment_price_buy,</if>
            <if test="jzgManufacturerPrice != null">jzg_manufacturer_price,</if>
            <if test="jzgAssessmentReportPdf != null">jzg_assessment_report_pdf,</if>
            <if test="jzgStatus != null">jzg_status,</if>
            <if test="jzgStatusDes != null">jzg_status_des,</if>
            <if test="departureDate != null">departure_date,</if>
            <if test="transferCount != null">transfer_count,</if>
            <if test="insuranceCondition != null">insurance_condition,</if>
            <if test="registrationNo != null">registration_no,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="orderReportUrl != null">order_report_url,</if>
            <if test="orderReportWeb != null">order_report_web,</if>
            <if test="beforePlateNo != null">before_plate_no,</if>
            <if test="manufacturer != null">manufacturer,</if>
            <if test="seatNumber != null">seat_number,</if>
            <if test="wxBrandCode != null">wx_brand_code,</if>
            <if test="wxBrandName != null">wx_brand_name,</if>
            <if test="wxSeriesCode != null">wx_series_code,</if>
            <if test="wxSeriesName != null">wx_series_name,</if>
            <if test="wxModelCode != null">wx_model_code,</if>
            <if test="wxModelName != null">wx_model_name,</if>
            <if test="wxGuidancePrice != null">wx_guidance_price,</if>
            <if test="wxAssessmentPrice != null">wx_assessment_price,</if>
            <if test="wxReportOrderNo != null">wx_report_order_no,</if>
            <if test="wxReportUrl != null">wx_report_url,</if>
            <if test="isAccident != null">is_accident,</if>
            <if test="avpId != null">avp_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="plateProvince != null">#{plateProvince},</if>
            <if test="plateCity != null">#{plateCity},</if>
            <if test="plateAddress != null">#{plateAddress},</if>
            <if test="mileage != null">#{mileage},</if>
            <if test="color != null">#{color},</if>
            <if test="nature != null">#{nature},</if>
            <if test="brandType != null">#{brandType},</if>
            <if test="energy != null">#{energy},</if>
            <if test="regIssueDate != null">#{regIssueDate},</if>
            <if test="registrationDate != null">#{registrationDate},</if>
            <if test="registrateFlag != null">#{registrateFlag},</if>
            <if test="registrateReason != null">#{registrateReason},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="brandName != null">#{brandName},</if>
            <if test="seriesId != null">#{seriesId},</if>
            <if test="seriesName != null">#{seriesName},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="modelName != null">#{modelName},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="cityName != null">#{cityName},</if>
            <if test="mileAge != null">#{mileAge},</if>
            <if test="regDate != null">#{regDate},</if>
            <if test="modelPrice != null">#{modelPrice},</if>
            <if test="dealerPrice != null">#{dealerPrice},</if>
            <if test="highDealerPrice != null">#{highDealerPrice},</if>
            <if test="individualPrice != null">#{individualPrice},</if>
            <if test="dealerBuyPrice != null">#{dealerBuyPrice},</if>
            <if test="individualLowSoldPrice != null">#{individualLowSoldPrice},</if>
            <if test="dealerLowBuyPrice != null">#{dealerLowBuyPrice},</if>
            <if test="dealerHighSoldPrice != null">#{dealerHighSoldPrice},</if>
            <if test="dealerLowSoldPrice != null">#{dealerLowSoldPrice},</if>
            <if test="url != null">#{url},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="manProvince != null">#{manProvince},</if>
            <if test="manCity != null">#{manCity},</if>
            <if test="manBorough != null">#{manBorough},</if>
            <if test="manAddress != null">#{manAddress},</if>
            <if test="manDetailAddress != null">#{manDetailAddress},</if>
            <if test="carProvince != null">#{carProvince},</if>
            <if test="carCity != null">#{carCity},</if>
            <if test="carBorough != null">#{carBorough},</if>
            <if test="carAddress != null">#{carAddress},</if>
            <if test="carDetailAddress != null">#{carDetailAddress},</if>
            <if test="carLandType != null">#{carLandType},</if>
            <if test="carStatus != null">#{carStatus},</if>
            <if test="gpsStatus != null">#{gpsStatus},</if>
            <if test="carColor != null">#{carColor},</if>
            <if test="modelYear != null">#{modelYear},</if>
            <if test="modelLiter != null">#{modelLiter},</if>
            <if test="modelGear != null">#{modelGear},</if>
            <if test="modelEmissionStandard != null">#{modelEmissionStandard},</if>
            <if test="minRegYear != null">#{minRegYear},</if>
            <if test="maxRegYear != null">#{maxRegYear},</if>
            <if test="seriesGroupName != null">#{seriesGroupName},</if>
            <if test="reportUrl != null">#{reportUrl},</if>
            <if test="vehicleType != null">#{vehicleType},</if>
            <if test="seriesType != null">#{seriesType},</if>
            <if test="carState != null">#{carState},</if>
            <if test="jzgOrderNo != null">#{jzgOrderNo},</if>
            <if test="jzgAssessmentPriceSold != null">#{jzgAssessmentPriceSold},</if>
            <if test="jzgAssessmentPriceBuy != null">#{jzgAssessmentPriceBuy},</if>
            <if test="jzgManufacturerPrice != null">#{jzgManufacturerPrice},</if>
            <if test="jzgAssessmentReportPdf != null">#{jzgAssessmentReportPdf},</if>
            <if test="jzgStatus != null">#{jzgStatus},</if>
            <if test="jzgStatusDes != null">#{jzgStatusDes},</if>
            <if test="departureDate != null">#{departureDate},</if>
            <if test="transferCount != null">#{transferCount},</if>
            <if test="insuranceCondition != null">#{insuranceCondition},</if>
            <if test="registrationNo != null">#{registrationNo},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="orderReportUrl != null">#{orderReportUrl},</if>
            <if test="orderReportWeb != null">#{orderReportWeb},</if>
            <if test="beforePlateNo != null">#{beforePlateNo},</if>
            <if test="manufacturer != null">#{manufacturer},</if>
            <if test="seatNumber != null">#{seatNumber},</if>
            <if test="wxBrandCode != null">#{wxBrandCode},</if>
            <if test="wxBrandName != null">#{wxBrandName},</if>
            <if test="wxSeriesCode != null">#{wxSeriesCode},</if>
            <if test="wxSeriesName != null">#{wxSeriesName},</if>
            <if test="wxModelCode != null">#{wxModelCode},</if>
            <if test="wxModelName != null">#{wxModelName},</if>
            <if test="wxGuidancePrice != null">#{wxGuidancePrice},</if>
            <if test="wxAssessmentPrice != null">#{wxAssessmentPrice},</if>
            <if test="wxReportOrderNo != null">#{wxReportOrderNo},</if>
            <if test="wxReportUrl != null">#{wxReportUrl},</if>
            <if test="isAccident != null">#{isAccident},</if>
            <if test="avpId != null">#{avpId},</if>
         </trim>
    </insert>

    <update id="updateIndCarInfo" parameterType="JJIndCarInfo">
        update ind_car_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null">apply_no = #{applyNo},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="plateProvince != null">plate_province = #{plateProvince},</if>
            <if test="plateCity != null">plate_city = #{plateCity},</if>
            <if test="plateAddress != null">plate_address = #{plateAddress},</if>
            <if test="mileage != null">mileage = #{mileage},</if>
            <if test="color != null">color = #{color},</if>
            <if test="nature != null">nature = #{nature},</if>
            <if test="brandType != null">brand_type = #{brandType},</if>
            <if test="energy != null">energy = #{energy},</if>
            <if test="regIssueDate != null">reg_issue_date = #{regIssueDate},</if>
            <if test="registrationDate != null">registration_date = #{registrationDate},</if>
            <if test="registrateFlag != null">registrate_flag = #{registrateFlag},</if>
            <if test="registrateReason != null">registrate_reason = #{registrateReason},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="brandName != null">brand_name = #{brandName},</if>
            <if test="seriesId != null">series_id = #{seriesId},</if>
            <if test="seriesName != null">series_name = #{seriesName},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="modelName != null">model_name = #{modelName},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="mileAge != null">mile_age = #{mileAge},</if>
            <if test="regDate != null">reg_date = #{regDate},</if>
            <if test="modelPrice != null">model_price = #{modelPrice},</if>
            <if test="dealerPrice != null">dealer_price = #{dealerPrice},</if>
            <if test="highDealerPrice != null">high_dealer_price = #{highDealerPrice},</if>
            <if test="individualPrice != null">individual_price = #{individualPrice},</if>
            <if test="dealerBuyPrice != null">dealer_buy_price = #{dealerBuyPrice},</if>
            <if test="individualLowSoldPrice != null">individual_low_sold_price = #{individualLowSoldPrice},</if>
            <if test="dealerLowBuyPrice != null">dealer_low_buy_price = #{dealerLowBuyPrice},</if>
            <if test="dealerHighSoldPrice != null">dealer_high_sold_price = #{dealerHighSoldPrice},</if>
            <if test="dealerLowSoldPrice != null">dealer_low_sold_price = #{dealerLowSoldPrice},</if>
            <if test="url != null">url = #{url},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="manProvince != null">man_province = #{manProvince},</if>
            <if test="manCity != null">man_city = #{manCity},</if>
            <if test="manBorough != null">man_borough = #{manBorough},</if>
            <if test="manAddress != null">man_address = #{manAddress},</if>
            <if test="manDetailAddress != null">man_detail_address = #{manDetailAddress},</if>
            <if test="carProvince != null">car_province = #{carProvince},</if>
            <if test="carCity != null">car_city = #{carCity},</if>
            <if test="carBorough != null">car_borough = #{carBorough},</if>
            <if test="carAddress != null">car_address = #{carAddress},</if>
            <if test="carDetailAddress != null">car_detail_address = #{carDetailAddress},</if>
            <if test="carLandType != null">car_land_type = #{carLandType},</if>
            <if test="carStatus != null">car_status = #{carStatus},</if>
            <if test="gpsStatus != null">gps_status = #{gpsStatus},</if>
            <if test="carColor != null">car_color = #{carColor},</if>
            <if test="modelYear != null">model_year = #{modelYear},</if>
            <if test="modelLiter != null">model_liter = #{modelLiter},</if>
            <if test="modelGear != null">model_gear = #{modelGear},</if>
            <if test="modelEmissionStandard != null">model_emission_standard = #{modelEmissionStandard},</if>
            <if test="minRegYear != null">min_reg_year = #{minRegYear},</if>
            <if test="maxRegYear != null">max_reg_year = #{maxRegYear},</if>
            <if test="seriesGroupName != null">series_group_name = #{seriesGroupName},</if>
            <if test="reportUrl != null">report_url = #{reportUrl},</if>
            <if test="vehicleType != null">vehicle_type = #{vehicleType},</if>
            <if test="seriesType != null">series_type = #{seriesType},</if>
            <if test="carState != null">car_state = #{carState},</if>
            <if test="jzgOrderNo != null">jzg_order_no = #{jzgOrderNo},</if>
            <if test="jzgAssessmentPriceSold != null">jzg_assessment_price_sold = #{jzgAssessmentPriceSold},</if>
            <if test="jzgAssessmentPriceBuy != null">jzg_assessment_price_buy = #{jzgAssessmentPriceBuy},</if>
            <if test="jzgManufacturerPrice != null">jzg_manufacturer_price = #{jzgManufacturerPrice},</if>
            <if test="jzgAssessmentReportPdf != null">jzg_assessment_report_pdf = #{jzgAssessmentReportPdf},</if>
            <if test="jzgStatus != null">jzg_status = #{jzgStatus},</if>
            <if test="jzgStatusDes != null">jzg_status_des = #{jzgStatusDes},</if>
            <if test="departureDate != null">departure_date = #{departureDate},</if>
            <if test="transferCount != null">transfer_count = #{transferCount},</if>
            <if test="insuranceCondition != null">insurance_condition = #{insuranceCondition},</if>
            <if test="registrationNo != null">registration_no = #{registrationNo},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="orderReportUrl != null">order_report_url = #{orderReportUrl},</if>
            <if test="orderReportWeb != null">order_report_web = #{orderReportWeb},</if>
            <if test="beforePlateNo != null">before_plate_no = #{beforePlateNo},</if>
            <if test="manufacturer != null">manufacturer = #{manufacturer},</if>
            <if test="seatNumber != null">seat_number = #{seatNumber},</if>
            <if test="wxBrandCode != null">wx_brand_code = #{wxBrandCode},</if>
            <if test="wxBrandName != null">wx_brand_name = #{wxBrandName},</if>
            <if test="wxSeriesCode != null">wx_series_code = #{wxSeriesCode},</if>
            <if test="wxSeriesName != null">wx_series_name = #{wxSeriesName},</if>
            <if test="wxModelCode != null">wx_model_code = #{wxModelCode},</if>
            <if test="wxModelName != null">wx_model_name = #{wxModelName},</if>
            <if test="wxGuidancePrice != null">wx_guidance_price = #{wxGuidancePrice},</if>
            <if test="wxAssessmentPrice != null">wx_assessment_price = #{wxAssessmentPrice},</if>
            <if test="wxReportOrderNo != null">wx_report_order_no = #{wxReportOrderNo},</if>
            <if test="wxReportUrl != null">wx_report_url = #{wxReportUrl},</if>
            <if test="isAccident != null">is_accident = #{isAccident},</if>
            <if test="avpId != null">avp_id = #{avpId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIndCarInfoById" parameterType="String">
        delete from ind_car_info where id = #{id}
    </delete>

    <delete id="deleteIndCarInfoByIds" parameterType="String">
        delete from ind_car_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>