package com.ruoyi.litigation_case.mapper;

import java.util.List;
import com.ruoyi.litigation_case.domain.LitigationCase;

/**
 * 法诉案件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface LitigationCaseMapper 
{
    /**
     * 查询法诉案件
     * 
     * @param id 法诉案件主键
     * @return 法诉案件
     */
    public LitigationCase selectLitigationCaseById(Long id);

    /**
     * 查询法诉案件列表
     * 
     * @param litigationCase 法诉案件
     * @return 法诉案件集合
     */
    public List<LitigationCase> selectLitigationCaseList(LitigationCase litigationCase);

    /**
     * 新增法诉案件
     * 
     * @param litigationCase 法诉案件
     * @return 结果
     */
    public int insertLitigationCase(LitigationCase litigationCase);

    /**
     * 修改法诉案件
     * 
     * @param litigationCase 法诉案件
     * @return 结果
     */
    public int updateLitigationCase(LitigationCase litigationCase);

    /**
     * 删除法诉案件
     * 
     * @param id 法诉案件主键
     * @return 结果
     */
    public int deleteLitigationCaseById(Long id);

    /**
     * 批量删除法诉案件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLitigationCaseByIds(Long[] ids);
}
