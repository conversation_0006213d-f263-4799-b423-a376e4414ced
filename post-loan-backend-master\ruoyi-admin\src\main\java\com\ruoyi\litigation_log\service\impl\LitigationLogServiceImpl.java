package com.ruoyi.litigation_log.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.litigation_log.mapper.LitigationLogMapper;
import com.ruoyi.litigation_log.domain.LitigationLog;
import com.ruoyi.litigation_log.service.ILitigationLogService;

/**
 * 法诉日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class LitigationLogServiceImpl implements ILitigationLogService 
{
    @Autowired
    private LitigationLogMapper litigationLogMapper;

    /**
     * 查询法诉日志
     * 
     * @param id 法诉日志主键
     * @return 法诉日志
     */
    @Override
    public LitigationLog selectLitigationLogById(Long id)
    {
        return litigationLogMapper.selectLitigationLogById(id);
    }

    /**
     * 查询法诉日志列表
     * 
     * @param litigationLog 法诉日志
     * @return 法诉日志
     */
    @Override
    public List<LitigationLog> selectLitigationLogList(LitigationLog litigationLog)
    {
        return litigationLogMapper.selectLitigationLogList(litigationLog);
    }

    /**
     * 新增法诉日志
     * 
     * @param litigationLog 法诉日志
     * @return 结果
     */
    @Override
    public int insertLitigationLog(LitigationLog litigationLog)
    {
        litigationLog.setCreateBy(SecurityUtils.getUsername());
        litigationLog.setCreateTime(DateUtils.getNowDate());
        return litigationLogMapper.insertLitigationLog(litigationLog);
    }

    /**
     * 修改法诉日志
     * 
     * @param litigationLog 法诉日志
     * @return 结果
     */
    @Override
    public int updateLitigationLog(LitigationLog litigationLog)
    {
        litigationLog.setUpdateTime(DateUtils.getNowDate());
        return litigationLogMapper.updateLitigationLog(litigationLog);
    }

    /**
     * 批量删除法诉日志
     * 
     * @param ids 需要删除的法诉日志主键
     * @return 结果
     */
    @Override
    public int deleteLitigationLogByIds(Long[] ids)
    {
        return litigationLogMapper.deleteLitigationLogByIds(ids);
    }

    /**
     * 删除法诉日志信息
     * 
     * @param id 法诉日志主键
     * @return 结果
     */
    @Override
    public int deleteLitigationLogById(Long id)
    {
        return litigationLogMapper.deleteLitigationLogById(id);
    }

    @Override
    public LitigationLog selectLatestLitigationLogByLitigationId(Long litigationId) {
        return litigationLogMapper.selectLatestLitigationLogByLitigationId(litigationId);
    }
}
