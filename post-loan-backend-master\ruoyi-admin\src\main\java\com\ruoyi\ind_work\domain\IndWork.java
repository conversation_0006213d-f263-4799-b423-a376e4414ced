package com.ruoyi.ind_work.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 个人客户工作信息对象 ind_work
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
public class IndWork extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 客户编号 */
    @Excel(name = "客户编号")
    private String customerId;

    /** 部门 */
    @Excel(name = "部门")
    private String department;

    /** 职业类型 */
    @Excel(name = "职业类型")
    private String occupationType;

    /** 职位类型 */
    @Excel(name = "职位类型")
    private String headshipType;

    /** 在职状态 */
    @Excel(name = "在职状态")
    private String workStatus;

    /** 单位名称 */
    @Excel(name = "单位名称")
    private String workCorp;

    /** 单位性质 */
    @Excel(name = "单位性质")
    private String companyType;

    /** 单位所在-省份 */
    @Excel(name = "单位所在-省份")
    private String province;

    /** 单位所在-城市 */
    @Excel(name = "单位所在-城市")
    private String city;

    /** 单位所在-县/区 */
    @Excel(name = "单位所在-县/区")
    private String borough;

    /** 单位所在-地址 */
    @Excel(name = "单位所在-地址")
    private String address;

    /** 单位所在-详细地址 */
    @Excel(name = "单位所在-详细地址")
    private String addressDetail;

    /** 公司成立年限 */
    @Excel(name = "公司成立年限")
    private Long establishYear;

    /** 工作年限 */
    @Excel(name = "工作年限")
    private Long workYear;

    /** 是否有企业 */
    @Excel(name = "是否有企业")
    private String businessRegister;

    /** 占股情况 */
    @Excel(name = "占股情况")
    private String dutyType;

    /** 单位邮编 */
    @Excel(name = "单位邮编")
    private String workZipCode;

    /** 创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 修改日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 人事部联系人 */
    @Excel(name = "人事部联系人")
    private String hrName;

    /** 人事部联系人手机号 */
    @Excel(name = "人事部联系人手机号")
    private String hrMobilePhone;

    /** 所在行业 */
    @Excel(name = "所在行业")
    private String industryOwned;

    /** 工作岗位 */
    @Excel(name = "工作岗位")
    private String workTrade;

    /** 删除标记 */
    private String delFlag;

    /** 省份(银行) */
    @Excel(name = "省份(银行)")
    private String yhProvince;

    /** 城市(银行) */
    @Excel(name = "城市(银行)")
    private String yhCity;

    /** 县区(银行) */
    @Excel(name = "县区(银行)")
    private String yhBorough;

    /** 地址(银行) */
    @Excel(name = "地址(银行)")
    private String yhAddress;

    /** 详细地址(银行) */
    @Excel(name = "详细地址(银行)")
    private String yhAddressDetail;

    /** 公司名称(银行) */
    @Excel(name = "公司名称(银行)")
    private String yhWorkCorp;

    /** 职称(皖新) */
    @Excel(name = "职称(皖新)")
    private String techTitle;

    /** 企业社会信用代码(蓝海) */
    @Excel(name = "企业社会信用代码(蓝海)")
    private String businessCode;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setCustomerId(String customerId) 
    {
        this.customerId = customerId;
    }

    public String getCustomerId() 
    {
        return customerId;
    }

    public void setDepartment(String department) 
    {
        this.department = department;
    }

    public String getDepartment() 
    {
        return department;
    }

    public void setOccupationType(String occupationType) 
    {
        this.occupationType = occupationType;
    }

    public String getOccupationType() 
    {
        return occupationType;
    }

    public void setHeadshipType(String headshipType) 
    {
        this.headshipType = headshipType;
    }

    public String getHeadshipType() 
    {
        return headshipType;
    }

    public void setWorkStatus(String workStatus) 
    {
        this.workStatus = workStatus;
    }

    public String getWorkStatus() 
    {
        return workStatus;
    }

    public void setWorkCorp(String workCorp) 
    {
        this.workCorp = workCorp;
    }

    public String getWorkCorp() 
    {
        return workCorp;
    }

    public void setCompanyType(String companyType) 
    {
        this.companyType = companyType;
    }

    public String getCompanyType() 
    {
        return companyType;
    }

    public void setProvince(String province) 
    {
        this.province = province;
    }

    public String getProvince() 
    {
        return province;
    }

    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }

    public void setBorough(String borough) 
    {
        this.borough = borough;
    }

    public String getBorough() 
    {
        return borough;
    }

    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }

    public void setAddressDetail(String addressDetail) 
    {
        this.addressDetail = addressDetail;
    }

    public String getAddressDetail() 
    {
        return addressDetail;
    }

    public void setEstablishYear(Long establishYear) 
    {
        this.establishYear = establishYear;
    }

    public Long getEstablishYear() 
    {
        return establishYear;
    }

    public void setWorkYear(Long workYear) 
    {
        this.workYear = workYear;
    }

    public Long getWorkYear() 
    {
        return workYear;
    }

    public void setBusinessRegister(String businessRegister) 
    {
        this.businessRegister = businessRegister;
    }

    public String getBusinessRegister() 
    {
        return businessRegister;
    }

    public void setDutyType(String dutyType) 
    {
        this.dutyType = dutyType;
    }

    public String getDutyType() 
    {
        return dutyType;
    }

    public void setWorkZipCode(String workZipCode) 
    {
        this.workZipCode = workZipCode;
    }

    public String getWorkZipCode() 
    {
        return workZipCode;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    public void setHrName(String hrName) 
    {
        this.hrName = hrName;
    }

    public String getHrName() 
    {
        return hrName;
    }

    public void setHrMobilePhone(String hrMobilePhone) 
    {
        this.hrMobilePhone = hrMobilePhone;
    }

    public String getHrMobilePhone() 
    {
        return hrMobilePhone;
    }

    public void setIndustryOwned(String industryOwned) 
    {
        this.industryOwned = industryOwned;
    }

    public String getIndustryOwned() 
    {
        return industryOwned;
    }

    public void setWorkTrade(String workTrade) 
    {
        this.workTrade = workTrade;
    }

    public String getWorkTrade() 
    {
        return workTrade;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setYhProvince(String yhProvince) 
    {
        this.yhProvince = yhProvince;
    }

    public String getYhProvince() 
    {
        return yhProvince;
    }

    public void setYhCity(String yhCity) 
    {
        this.yhCity = yhCity;
    }

    public String getYhCity() 
    {
        return yhCity;
    }

    public void setYhBorough(String yhBorough) 
    {
        this.yhBorough = yhBorough;
    }

    public String getYhBorough() 
    {
        return yhBorough;
    }

    public void setYhAddress(String yhAddress) 
    {
        this.yhAddress = yhAddress;
    }

    public String getYhAddress() 
    {
        return yhAddress;
    }

    public void setYhAddressDetail(String yhAddressDetail) 
    {
        this.yhAddressDetail = yhAddressDetail;
    }

    public String getYhAddressDetail() 
    {
        return yhAddressDetail;
    }

    public void setYhWorkCorp(String yhWorkCorp) 
    {
        this.yhWorkCorp = yhWorkCorp;
    }

    public String getYhWorkCorp() 
    {
        return yhWorkCorp;
    }

    public void setTechTitle(String techTitle) 
    {
        this.techTitle = techTitle;
    }

    public String getTechTitle() 
    {
        return techTitle;
    }

    public void setBusinessCode(String businessCode) 
    {
        this.businessCode = businessCode;
    }

    public String getBusinessCode() 
    {
        return businessCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("customerId", getCustomerId())
            .append("department", getDepartment())
            .append("occupationType", getOccupationType())
            .append("headshipType", getHeadshipType())
            .append("workStatus", getWorkStatus())
            .append("workCorp", getWorkCorp())
            .append("companyType", getCompanyType())
            .append("province", getProvince())
            .append("city", getCity())
            .append("borough", getBorough())
            .append("address", getAddress())
            .append("addressDetail", getAddressDetail())
            .append("establishYear", getEstablishYear())
            .append("workYear", getWorkYear())
            .append("businessRegister", getBusinessRegister())
            .append("dutyType", getDutyType())
            .append("workZipCode", getWorkZipCode())
            .append("createBy", getCreateBy())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .append("hrName", getHrName())
            .append("hrMobilePhone", getHrMobilePhone())
            .append("industryOwned", getIndustryOwned())
            .append("workTrade", getWorkTrade())
            .append("remark", getRemark())
            .append("delFlag", getDelFlag())
            .append("yhProvince", getYhProvince())
            .append("yhCity", getYhCity())
            .append("yhBorough", getYhBorough())
            .append("yhAddress", getYhAddress())
            .append("yhAddressDetail", getYhAddressDetail())
            .append("yhWorkCorp", getYhWorkCorp())
            .append("techTitle", getTechTitle())
            .append("businessCode", getBusinessCode())
            .toString();
    }
}
