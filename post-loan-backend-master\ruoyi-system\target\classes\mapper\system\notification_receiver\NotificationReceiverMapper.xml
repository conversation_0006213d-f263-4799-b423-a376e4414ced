<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.notification_receiver.mapper.NotificationReceiverMapper">
    
    <resultMap type="NotificationReceiver" id="NotificationReceiverResult">
        <result property="id"    column="id"    />
        <result property="notificationId"    column="notification_id"    />
        <result property="userId"    column="user_id"    />
        <result property="status"    column="status"    />
        <result property="readTime"    column="read_time"    />
        <result property="receiveType"    column="receive_type"    />
        <result property="roleType"    column="role_type"    />
    </resultMap>

    <sql id="selectNotificationReceiverVo">
        select id, notification_id, user_id, status, read_time, receive_type, role_type from notification_receiver
    </sql>

    <select id="selectNotificationReceiverList" parameterType="NotificationReceiver" resultMap="NotificationReceiverResult">
        <include refid="selectNotificationReceiverVo"/>
        <where>  
            <if test="notificationId != null "> and notification_id = #{notificationId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="readTime != null "> and read_time = #{readTime}</if>
        </where>
    </select>
    
    <select id="selectNotificationReceiverById" parameterType="Long" resultMap="NotificationReceiverResult">
        <include refid="selectNotificationReceiverVo"/>
        where id = #{id}
    </select>

    <insert id="insertNotificationReceiver" parameterType="NotificationReceiver" useGeneratedKeys="true" keyProperty="id">
        insert into notification_receiver
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="notificationId != null">notification_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="status != null">status,</if>
            <if test="readTime != null">read_time,</if>
            <if test="receiveType != null">receive_type,</if>
            <if test="roleType != null">role_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="notificationId != null">#{notificationId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="status != null">#{status},</if>
            <if test="readTime != null">#{readTime},</if>
            <if test="receiveType != null">#{receiveType},</if>
            <if test="roleType != null">#{roleType},</if>
         </trim>
    </insert>

    <update id="updateNotificationReceiver" parameterType="NotificationReceiver">
        update notification_receiver
        <trim prefix="SET" suffixOverrides=",">
            <if test="notificationId != null">notification_id = #{notificationId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="readTime != null">read_time = #{readTime},</if>
            <if test="receiveType != null">receive_type = #{receiveType},</if>
            <if test="roleType != null">role_type = #{roleType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNotificationReceiverById" parameterType="Long">
        delete from notification_receiver where id = #{id}
    </delete>

    <delete id="deleteNotificationReceiverByIds" parameterType="String">
        delete from notification_receiver where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>