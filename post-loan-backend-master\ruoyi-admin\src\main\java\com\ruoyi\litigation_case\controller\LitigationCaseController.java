package com.ruoyi.litigation_case.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.litigation_case.domain.LitigationCase;
import com.ruoyi.litigation_case.service.ILitigationCaseService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 法诉案件Controller
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/litigation_case/litigation_case")
public class LitigationCaseController extends BaseController {
    @Autowired
    private ILitigationCaseService litigationCaseService;

    @Autowired
    private com.ruoyi.car_order.service.ICarOrderService carOrderService;

    /**
     * 查询法诉案件列表
     */
    @PreAuthorize("@ss.hasPermi('litigation_case:litigation_case:list')")
    @GetMapping("/list")
    public TableDataInfo list(LitigationCase litigationCase) {
        startPage();
        List<LitigationCase> list = litigationCaseService.selectLitigationCaseList(litigationCase);
        return getDataTable(list);
    }

    /**
     * 导出法诉案件列表
     */
    @PreAuthorize("@ss.hasPermi('litigation_case:litigation_case:export')")
    @Log(title = "法诉案件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LitigationCase litigationCase) {
        List<LitigationCase> list = litigationCaseService.selectLitigationCaseList(litigationCase);
        ExcelUtil<LitigationCase> util = new ExcelUtil<LitigationCase>(LitigationCase.class);
        util.exportExcel(response, list, "法诉案件数据");
    }

    /**
     * 获取法诉案件详细信息
     */
    @PreAuthorize("@ss.hasPermi('litigation_case:litigation_case:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(litigationCaseService.selectLitigationCaseById(id));
    }

    /**
     * 根据loanId获取法诉案件列表
     */
    @PreAuthorize("@ss.hasPermi('litigation_case:litigation_case:query')")
    @GetMapping(value = "/byLoanId/{loanId}")
    public AjaxResult getInfoByLoanId(@PathVariable("loanId") Long loanId) {
        LitigationCase queryParam = new LitigationCase();
        queryParam.setLoanId(loanId);
        List<LitigationCase> list = litigationCaseService.selectLitigationCaseList(queryParam);
        return success(list);
    }

    /**
     * 新增法诉案件
     */
    @PreAuthorize("@ss.hasPermi('litigation_case:litigation_case:add')")
    @Log(title = "法诉案件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LitigationCase litigationCase) {
        // 新增法诉时，同时把 car_order 的 dispatcher 设为2
        if (litigationCase.getLoanId() != null) {
            carOrderService.updateDispatcherByLoanId(litigationCase.getLoanId(), 2);
        }
        logger.info("新增法诉案件: {}", litigationCase);
        return toAjax(litigationCaseService.insertLitigationCase(litigationCase));
    }

    /**
     * 修改法诉案件
     */
    @PreAuthorize("@ss.hasPermi('litigation_case:litigation_case:edit')")
    @Log(title = "法诉案件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LitigationCase litigationCase) {
        return toAjax(litigationCaseService.updateLitigationCase(litigationCase));
    }

    /**
     * 删除法诉案件
     */
    @PreAuthorize("@ss.hasPermi('litigation_case:litigation_case:remove')")
    @Log(title = "法诉案件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(litigationCaseService.deleteLitigationCaseByIds(ids));
    }
}
