package com.ruoyi.bank_account.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.bank_account.domain.BankAccount;
import com.ruoyi.bank_account.service.IBankAccountService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 银行账户Controller
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@RestController
@RequestMapping("/bank_account/bank_account")
public class BankAccountController extends BaseController
{
    @Autowired
    private IBankAccountService bankAccountService;

    /**
     * 查询银行账户列表
     */
    @PreAuthorize("@ss.hasPermi('bank_account:bank_account:list')")
    @GetMapping("/list")
    public TableDataInfo list(BankAccount bankAccount)
    {
        startPage();
//        bankAccount.setIsOff(1);
        bankAccount.setDelFlag("0");
        List<BankAccount> list = bankAccountService.selectBankAccountList(bankAccount);
        return getDataTable(list);
    }
    /**
     * 查询银行账户列表
     */
//    @PreAuthorize("@ss.hasPermi('bank_account:bank_account:list')")
    @GetMapping("/bank")
    public TableDataInfo lists(BankAccount bankAccount)
    {
        startPage();
        bankAccount.setIsOff(1);
        bankAccount.setDelFlag("0");
        List<BankAccount> list = bankAccountService.selectBankAccountList(bankAccount);
        return getDataTable(list);
    }
    /**
     * 导出银行账户列表
     */
    @PreAuthorize("@ss.hasPermi('bank_account:bank_account:export')")
    @Log(title = "银行账户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BankAccount bankAccount)
    {
        List<BankAccount> list = bankAccountService.selectBankAccountList(bankAccount);
        ExcelUtil<BankAccount> util = new ExcelUtil<BankAccount>(BankAccount.class);
        util.exportExcel(response, list, "银行账户数据");
    }

    /**
     * 获取银行账户详细信息
     */
    @PreAuthorize("@ss.hasPermi('bank_account:bank_account:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(bankAccountService.selectBankAccountById(id));
    }

    /**
     * 新增银行账户
     */
    @PreAuthorize("@ss.hasPermi('bank_account:bank_account:add')")
    @Log(title = "银行账户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BankAccount bankAccount)
    {
        return toAjax(bankAccountService.insertBankAccount(bankAccount));
    }

    /**
     * 修改银行账户
     */
    @PreAuthorize("@ss.hasPermi('bank_account:bank_account:edit')")
    @Log(title = "银行账户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BankAccount bankAccount)
    {
        return toAjax(bankAccountService.updateBankAccount(bankAccount));
    }

    /**
     * 删除银行账户
     */
    @PreAuthorize("@ss.hasPermi('bank_account:bank_account:remove')")
    @Log(title = "银行账户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(bankAccountService.deleteBankAccountByIds(ids));
    }
}
