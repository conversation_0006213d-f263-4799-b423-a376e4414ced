package com.ruoyi.findcar.mapper;

import java.util.List;
import com.ruoyi.findcar.domain.FindCar;

/**
 * 找车结果上报Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface FindCarMapper 
{
    /**
     * 查询找车结果上报
     * 
     * @param id 找车结果上报主键
     * @return 找车结果上报
     */
    public FindCar selectFindCarById(String id);

    /**
     * 查询找车结果上报列表
     * 
     * @param findCar 找车结果上报
     * @return 找车结果上报集合
     */
    public List<FindCar> selectFindCarList(FindCar findCar);

    /**
     * 新增找车结果上报
     * 
     * @param findCar 找车结果上报
     * @return 结果
     */
    public int insertFindCar(FindCar findCar);

    /**
     * 修改找车结果上报
     * 
     * @param findCar 找车结果上报
     * @return 结果
     */
    public int updateFindCar(FindCar findCar);

    /**
     * 删除找车结果上报
     * 
     * @param id 找车结果上报主键
     * @return 结果
     */
    public int deleteFindCarById(String id);

    /**
     * 批量删除找车结果上报
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFindCarByIds(String[] ids);
}
