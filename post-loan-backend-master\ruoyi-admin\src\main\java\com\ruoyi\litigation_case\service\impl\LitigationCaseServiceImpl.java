package com.ruoyi.litigation_case.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.litigation_case.mapper.LitigationCaseMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.litigation_case.domain.LitigationCase;
import com.ruoyi.litigation_case.service.ILitigationCaseService;
import com.ruoyi.litigation_log.service.ILitigationLogService;
import com.ruoyi.litigation_log.domain.LitigationLog;
import com.ruoyi.loan_reminder.service.ILoanReminderService;
import com.ruoyi.loan_reminder.domain.LoanReminder;

/**
 * 法诉案件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
public class LitigationCaseServiceImpl implements ILitigationCaseService 
{
    @Autowired
    private LitigationCaseMapper litigationCaseMapper;

    @Autowired
    private ILitigationLogService litigationLogService;

    @Autowired
    private ILoanReminderService loanReminderService;

    /**
     * 查询法诉案件
     * 
     * @param id 法诉案件主键
     * @return 法诉案件
     */
    @Override
    public LitigationCase selectLitigationCaseById(Long id)
    {
        LitigationCase litigationCase = litigationCaseMapper.selectLitigationCaseById(id);
        if (litigationCase != null) {
            LitigationLog latestLog = litigationLogService.selectLatestLitigationLogByLitigationId(litigationCase.getId());
            litigationCase.setLatestLitigationLog(latestLog);
            LoanReminder latestReminder = loanReminderService.selectLatestLoanReminderByLitigationId(litigationCase.getId());
            if (latestReminder != null) {
                litigationCase.setUrgeMoney(latestReminder.getUrgeMoney());
                litigationCase.setRepaymentStatus(latestReminder.getRepaymentStatus());
                litigationCase.setUrgeStatus(latestReminder.getUrgeStatus());
            }
        }
        return litigationCase;
    }

    /**
     * 查询法诉案件列表
     * 
     * @param litigationCase 法诉案件
     * @return 法诉案件
     */
    @Override
    public List<LitigationCase> selectLitigationCaseList(LitigationCase litigationCase)
    {
        List<LitigationCase> list = litigationCaseMapper.selectLitigationCaseList(litigationCase);
        if (list != null) {
            for (LitigationCase lc : list) {
                if (lc != null && lc.getId() != null) {
                    LitigationLog latestLog = litigationLogService.selectLatestLitigationLogByLitigationId(lc.getId());
                    lc.setLatestLitigationLog(latestLog);
                    LoanReminder latestReminder = loanReminderService.selectLatestLoanReminderByLitigationId(lc.getId());
                    if (latestReminder != null) {
                        lc.setUrgeMoney(latestReminder.getUrgeMoney());
                        lc.setRepaymentStatus(latestReminder.getRepaymentStatus());
                        lc.setUrgeStatus(latestReminder.getUrgeStatus());
                    }
                }
            }
        }
        return list;
    }

    /**
     * 新增法诉案件
     * 
     * @param litigationCase 法诉案件
     * @return 结果
     */
    @Override
    public int insertLitigationCase(LitigationCase litigationCase)
    {
        litigationCase.setCreateTime(DateUtils.getNowDate());
        litigationCase.setCreateBy(SecurityUtils.getUsername());
        return litigationCaseMapper.insertLitigationCase(litigationCase);
    }

    /**
     * 修改法诉案件
     * 
     * @param litigationCase 法诉案件
     * @return 结果
     */
    @Override
    public int updateLitigationCase(LitigationCase litigationCase)
    {
        return litigationCaseMapper.updateLitigationCase(litigationCase);
    }

    /**
     * 批量删除法诉案件
     * 
     * @param ids 需要删除的法诉案件主键
     * @return 结果
     */
    @Override
    public int deleteLitigationCaseByIds(Long[] ids)
    {
        return litigationCaseMapper.deleteLitigationCaseByIds(ids);
    }

    /**
     * 删除法诉案件信息
     * 
     * @param id 法诉案件主键
     * @return 结果
     */
    @Override
    public int deleteLitigationCaseById(Long id)
    {
        return litigationCaseMapper.deleteLitigationCaseById(id);
    }
}
