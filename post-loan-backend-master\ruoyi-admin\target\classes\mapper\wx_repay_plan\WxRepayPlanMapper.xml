<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wx_repay_plan.mapper.WxRepayPlanMapper">
    
    <resultMap type="WxRepayPlan" id="WxRepayPlanResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="period"    column="period"    />
        <result property="repayDate"    column="repay_date"    />
        <result property="actualRepayDate"    column="actual_repay_date"    />
        <result property="repayAmount"    column="repay_amount"    />
        <result property="actualRepayAmount"    column="actual_repay_amount"    />
        <result property="capital"    column="capital"    />
        <result property="actualCapital"    column="actual_capital"    />
        <result property="interest"    column="interest"    />
        <result property="actualInterest"    column="actual_interest"    />
        <result property="defInterest"    column="def_interest"    />
        <result property="actualDefInterest"    column="actual_def_interest"    />
        <result property="balance"    column="balance"    />
        <result property="status"    column="status"    />
        <result property="buyBackStatus"    column="buy_back_status"    />
        <result property="buyBackCapital"    column="buy_back_capital"    />
        <result property="buyBackInterest"    column="buy_back_interest"    />
        <result property="flag"    column="flag"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
    </resultMap>

    <sql id="selectWxRepayPlanVo">
        select id, apply_id, period, repay_date, actual_repay_date, repay_amount, actual_repay_amount, capital, actual_capital, interest, actual_interest, def_interest, actual_def_interest, balance, status, buy_back_status, buy_back_capital, buy_back_interest, flag, create_date, update_date from wx_repay_plan
    </sql>

    <select id="selectWxRepayPlanList" parameterType="WxRepayPlan" resultMap="WxRepayPlanResult">
        <include refid="selectWxRepayPlanVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="repayDate != null  and repayDate != ''"> and repay_date = #{repayDate}</if>
            <if test="actualRepayDate != null  and actualRepayDate != ''"> and actual_repay_date = #{actualRepayDate}</if>
            <if test="repayAmount != null "> and repay_amount = #{repayAmount}</if>
            <if test="actualRepayAmount != null "> and actual_repay_amount = #{actualRepayAmount}</if>
            <if test="capital != null "> and capital = #{capital}</if>
            <if test="actualCapital != null "> and actual_capital = #{actualCapital}</if>
            <if test="interest != null "> and interest = #{interest}</if>
            <if test="actualInterest != null "> and actual_interest = #{actualInterest}</if>
            <if test="defInterest != null "> and def_interest = #{defInterest}</if>
            <if test="actualDefInterest != null "> and actual_def_interest = #{actualDefInterest}</if>
            <if test="balance != null "> and balance = #{balance}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="buyBackStatus != null  and buyBackStatus != ''"> and buy_back_status = #{buyBackStatus}</if>
            <if test="buyBackCapital != null "> and buy_back_capital = #{buyBackCapital}</if>
            <if test="buyBackInterest != null "> and buy_back_interest = #{buyBackInterest}</if>
            <if test="flag != null  and flag != ''"> and flag = #{flag}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
    </select>
    
    <select id="selectWxRepayPlanById" parameterType="String" resultMap="WxRepayPlanResult">
        <include refid="selectWxRepayPlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertWxRepayPlan" parameterType="WxRepayPlan">
        insert into wx_repay_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applyId != null">apply_id,</if>
            <if test="period != null">period,</if>
            <if test="repayDate != null">repay_date,</if>
            <if test="actualRepayDate != null">actual_repay_date,</if>
            <if test="repayAmount != null">repay_amount,</if>
            <if test="actualRepayAmount != null">actual_repay_amount,</if>
            <if test="capital != null">capital,</if>
            <if test="actualCapital != null">actual_capital,</if>
            <if test="interest != null">interest,</if>
            <if test="actualInterest != null">actual_interest,</if>
            <if test="defInterest != null">def_interest,</if>
            <if test="actualDefInterest != null">actual_def_interest,</if>
            <if test="balance != null">balance,</if>
            <if test="status != null">status,</if>
            <if test="buyBackStatus != null">buy_back_status,</if>
            <if test="buyBackCapital != null">buy_back_capital,</if>
            <if test="buyBackInterest != null">buy_back_interest,</if>
            <if test="flag != null">flag,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateDate != null">update_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applyId != null">#{applyId},</if>
            <if test="period != null">#{period},</if>
            <if test="repayDate != null">#{repayDate},</if>
            <if test="actualRepayDate != null">#{actualRepayDate},</if>
            <if test="repayAmount != null">#{repayAmount},</if>
            <if test="actualRepayAmount != null">#{actualRepayAmount},</if>
            <if test="capital != null">#{capital},</if>
            <if test="actualCapital != null">#{actualCapital},</if>
            <if test="interest != null">#{interest},</if>
            <if test="actualInterest != null">#{actualInterest},</if>
            <if test="defInterest != null">#{defInterest},</if>
            <if test="actualDefInterest != null">#{actualDefInterest},</if>
            <if test="balance != null">#{balance},</if>
            <if test="status != null">#{status},</if>
            <if test="buyBackStatus != null">#{buyBackStatus},</if>
            <if test="buyBackCapital != null">#{buyBackCapital},</if>
            <if test="buyBackInterest != null">#{buyBackInterest},</if>
            <if test="flag != null">#{flag},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateDate != null">#{updateDate},</if>
         </trim>
    </insert>

    <update id="updateWxRepayPlan" parameterType="WxRepayPlan">
        update wx_repay_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyId != null">apply_id = #{applyId},</if>
            <if test="period != null">period = #{period},</if>
            <if test="repayDate != null">repay_date = #{repayDate},</if>
            <if test="actualRepayDate != null">actual_repay_date = #{actualRepayDate},</if>
            <if test="repayAmount != null">repay_amount = #{repayAmount},</if>
            <if test="actualRepayAmount != null">actual_repay_amount = #{actualRepayAmount},</if>
            <if test="capital != null">capital = #{capital},</if>
            <if test="actualCapital != null">actual_capital = #{actualCapital},</if>
            <if test="interest != null">interest = #{interest},</if>
            <if test="actualInterest != null">actual_interest = #{actualInterest},</if>
            <if test="defInterest != null">def_interest = #{defInterest},</if>
            <if test="actualDefInterest != null">actual_def_interest = #{actualDefInterest},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="status != null">status = #{status},</if>
            <if test="buyBackStatus != null">buy_back_status = #{buyBackStatus},</if>
            <if test="buyBackCapital != null">buy_back_capital = #{buyBackCapital},</if>
            <if test="buyBackInterest != null">buy_back_interest = #{buyBackInterest},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWxRepayPlanById" parameterType="String">
        delete from wx_repay_plan where id = #{id}
    </delete>

    <delete id="deleteWxRepayPlanByIds" parameterType="String">
        delete from wx_repay_plan where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>