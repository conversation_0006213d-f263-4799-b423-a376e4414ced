<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.reimburse.mapper.ReimburseMapper">
    
    <resultMap type="Reimburse" id="ReimburseResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="content"    column="content"    />
        <result property="money"    column="money"    />
        <result property="img"    column="img"    />
        <result property="status"    column="status"    />
        <result property="state"    column="state"    />
        <result property="keyStatus"    column="key_status"    />
        <result property="remark"    column="remark"    />
        <result property="startAddress"    column="start_address"    />
        <result property="endAddress"    column="end_address"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectReimburseVo">
        select id, member_id, content, money, img, status, state, key_status, remark, start_address, end_address, create_by, create_date, update_by, update_date, del_flag from reimburse
    </sql>

    <select id="selectReimburseList" parameterType="Reimburse" resultMap="ReimburseResult">
        <include refid="selectReimburseVo"/>
        <where>  
            <if test="memberId != null  and memberId != ''"> and member_id = #{memberId}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="money != null "> and money = #{money}</if>
            <if test="img != null  and img != ''"> and img = #{img}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="keyStatus != null  and keyStatus != ''"> and key_status = #{keyStatus}</if>
            <if test="startAddress != null  and startAddress != ''"> and start_address = #{startAddress}</if>
            <if test="endAddress != null  and endAddress != ''"> and end_address = #{endAddress}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
    </select>
    
    <select id="selectReimburseById" parameterType="String" resultMap="ReimburseResult">
        <include refid="selectReimburseVo"/>
        where id = #{id}
    </select>

    <insert id="insertReimburse" parameterType="Reimburse" useGeneratedKeys="true" keyProperty="id">
        insert into reimburse
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="memberId != null">member_id,</if>
            <if test="content != null">content,</if>
            <if test="money != null">money,</if>
            <if test="img != null">img,</if>
            <if test="status != null">status,</if>
            <if test="state != null">state,</if>
            <if test="keyStatus != null">key_status,</if>
            <if test="remark != null">remark,</if>
            <if test="startAddress != null">start_address,</if>
            <if test="endAddress != null">end_address,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="memberId != null">#{memberId},</if>
            <if test="content != null">#{content},</if>
            <if test="money != null">#{money},</if>
            <if test="img != null">#{img},</if>
            <if test="status != null">#{status},</if>
            <if test="state != null">#{state},</if>
            <if test="keyStatus != null">#{keyStatus},</if>
            <if test="remark != null">#{remark},</if>
            <if test="startAddress != null">#{startAddress},</if>
            <if test="endAddress != null">#{endAddress},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateReimburse" parameterType="Reimburse">
        update reimburse
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="money != null">money = #{money},</if>
            <if test="img != null">img = #{img},</if>
            <if test="status != null">status = #{status},</if>
            <if test="state != null">state = #{state},</if>
            <if test="keyStatus != null">key_status = #{keyStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="startAddress != null">start_address = #{startAddress},</if>
            <if test="endAddress != null">end_address = #{endAddress},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteReimburseById" parameterType="String">
        delete from reimburse where id = #{id}
    </delete>

    <delete id="deleteReimburseByIds" parameterType="String">
        delete from reimburse where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>