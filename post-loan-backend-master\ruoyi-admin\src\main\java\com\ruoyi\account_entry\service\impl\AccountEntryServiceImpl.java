package com.ruoyi.account_entry.service.impl;

import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.account_entry.mapper.AccountEntryMapper;
import com.ruoyi.account_entry.domain.AccountEntry;
import com.ruoyi.account_entry.service.IAccountEntryService;

/**
 * 入账登记Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class AccountEntryServiceImpl implements IAccountEntryService {
    @Autowired
    private AccountEntryMapper accountEntryMapper;

    /**
     * 查询入账登记
     * 
     * @param id 入账登记主键
     * @return 入账登记
     */
    @Override
    public AccountEntry selectAccountEntryById(Long id) {
        return accountEntryMapper.selectAccountEntryById(id);
    }

    /**
     * 查询入账登记列表
     * 
     * @param accountEntry 入账登记
     * @return 入账登记
     */
    @Override
    public List<AccountEntry> selectAccountEntryList(AccountEntry accountEntry) {
        return accountEntryMapper.selectAccountEntryList(accountEntry);
    }

    /**
     * 新增入账登记
     * 
     * @param accountEntry 入账登记
     * @return 结果
     */
    @Override
    public int insertAccountEntry(AccountEntry accountEntry) {
        // 检查是否已存在相同代偿ID和入账类型的记录
        if (accountEntry.getLoanCompensationId() != null && accountEntry.getAmountType() != null) {
            boolean exists = existsByLoanCompensationIdAndAmountType(accountEntry.getLoanCompensationId(),
                    accountEntry.getAmountType());
            if (exists) {
                throw new RuntimeException("该类型入账类型已登记");
            }
        }

        String username = com.ruoyi.common.utils.SecurityUtils.getUsername();
        accountEntry.setCreateBy(username);
        accountEntry.setCreateDate(new Date());
        return accountEntryMapper.insertAccountEntry(accountEntry);
    }

    /**
     * 修改入账登记
     * 
     * @param accountEntry 入账登记
     * @return 结果
     */
    @Override
    public int updateAccountEntry(AccountEntry accountEntry) {
        String username = com.ruoyi.common.utils.SecurityUtils.getUsername();
        accountEntry.setUpdateBy(username);
        accountEntry.setUpdateDate(new Date());
        return accountEntryMapper.updateAccountEntry(accountEntry);
    }

    /**
     * 批量删除入账登记
     * 
     * @param ids 需要删除的入账登记主键
     * @return 结果
     */
    @Override
    public int deleteAccountEntryByIds(Long[] ids) {
        return accountEntryMapper.deleteAccountEntryByIds(ids);
    }

    /**
     * 删除入账登记信息
     * 
     * @param id 入账登记主键
     * @return 结果
     */
    @Override
    public int deleteAccountEntryById(Long id) {
        return accountEntryMapper.deleteAccountEntryById(id);
    }

    @Override
    public int batchInsertAccountEntry(List<AccountEntry> accountEntryList) {
        String username = com.ruoyi.common.utils.SecurityUtils.getUsername();
        for (AccountEntry entry : accountEntryList) {
            // 检查是否已存在相同代偿ID和入账类型的记录
            if (entry.getLoanCompensationId() != null && entry.getAmountType() != null) {
                boolean exists = existsByLoanCompensationIdAndAmountType(entry.getLoanCompensationId(),
                        entry.getAmountType());
                if (exists) {
                    throw new RuntimeException("该类型入账类型已登记");
                }
            }

            entry.setCreateBy(username);
            entry.setCreateDate(new Date());
        }
        return accountEntryMapper.batchInsertAccountEntry(accountEntryList);
    }

    @Override
    public boolean existsByLoanCompensationIdAndAmountType(Long loanCompensationId, String amountType) {
        int count = accountEntryMapper.countByLoanCompensationIdAndAmountType(loanCompensationId, amountType);
        return count >= 1;
    }
}
