<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.JJSyRepayPlanMapper">
    
    <resultMap type="JJSyRepayPlan" id="SyRepayPlanResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="syApplyId"    column="sy_apply_id"    />
        <result property="stageNum"    column="stage_num"    />
        <result property="loanDate"    column="loan_date"    />
        <result property="repayDate"    column="repay_date"    />
        <result property="actualRepayDate"    column="actual_repay_date"    />
        <result property="repayAmount"    column="repay_amount"    />
        <result property="capital"    column="capital"    />
        <result property="interest"    column="interest"    />
        <result property="lastCapital"    column="last_capital"    />
        <result property="bondAmount"    column="bond_amount"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="sareDate"    column="sare_date"    />
        <result property="shareAmount"    column="share_amount"    />
        <result property="lateCharge"    column="late_charge"    />
        <result property="realCharge"    column="real_charge"    />
        <result property="derateCharge"    column="derate_charge"    />
        <result property="status"    column="status"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
    </resultMap>

    <sql id="selectSyRepayPlanVo">
        select * from sy_repay_plan
    </sql>

    <select id="selectSyRepayPlanList" parameterType="JJSyRepayPlan" resultMap="SyRepayPlanResult">
        <include refid="selectSyRepayPlanVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>


    <select id="selectSyRepayAmount" parameterType="String" resultMap="SyRepayPlanResult">
        select SUM(repay_amount) as repay_amount from sy_repay_plan where apply_id = #{applyId} and status = '1'
    </select>


    <select id="selectSyRepayNow" parameterType="String" resultMap="SyRepayPlanResult">
        select * from sy_repay_plan where apply_id = #{applyId} and  YEAR(repay_date) = YEAR(CURDATE())  and MONTH(repay_date) = MONTH(CURDATE()) limit 1
    </select>



    <select id="selectSyRepayPlanListGroup" parameterType="JJSyRepayPlan" resultMap="SyRepayPlanResult">
        select * from sy_repay_plan
        <where>
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        group by apply_id
    </select>


</mapper>