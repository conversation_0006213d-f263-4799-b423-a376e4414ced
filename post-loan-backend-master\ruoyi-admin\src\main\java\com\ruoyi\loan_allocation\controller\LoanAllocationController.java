package com.ruoyi.loan_allocation.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.loan_allocation.domain.LoanAllocation;
import com.ruoyi.loan_allocation.service.ILoanAllocationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 流程跟催员分配Controller
 * 
 * <AUTHOR>
 * @date 2025-06-21
 */
@RestController
@RequestMapping("/loan_allocation/loan_allocation")
public class LoanAllocationController extends BaseController
{
    @Autowired
    private ILoanAllocationService loanAllocationService;

    /**
     * 查询流程跟催员分配列表
     */
    @PreAuthorize("@ss.hasPermi('loan_allocation:loan_allocation:list')")
    @GetMapping("/list")
    public TableDataInfo list(LoanAllocation loanAllocation)
    {
        startPage();
        List<LoanAllocation> list = loanAllocationService.selectLoanAllocationList(loanAllocation);
        return getDataTable(list);
    }

    /**
     * 导出流程跟催员分配列表
     */
    @PreAuthorize("@ss.hasPermi('loan_allocation:loan_allocation:export')")
    @Log(title = "流程跟催员分配", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LoanAllocation loanAllocation)
    {
        List<LoanAllocation> list = loanAllocationService.selectLoanAllocationList(loanAllocation);
        ExcelUtil<LoanAllocation> util = new ExcelUtil<LoanAllocation>(LoanAllocation.class);
        util.exportExcel(response, list, "流程跟催员分配数据");
    }

    /**
     * 获取流程跟催员分配详细信息
     */
    @PreAuthorize("@ss.hasPermi('loan_allocation:loan_allocation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(loanAllocationService.selectLoanAllocationById(id));
    }

    /**
     * 新增流程跟催员分配
     */
    @PreAuthorize("@ss.hasPermi('loan_allocation:loan_allocation:add')")
    @Log(title = "流程跟催员分配", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LoanAllocation loanAllocation)
    {
        return toAjax(loanAllocationService.insertLoanAllocation(loanAllocation));
    }

    /**
     * 修改流程跟催员分配
     */
    @PreAuthorize("@ss.hasPermi('loan_allocation:loan_allocation:edit')")
    @Log(title = "流程跟催员分配", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LoanAllocation loanAllocation)
    {
        return toAjax(loanAllocationService.updateLoanAllocation(loanAllocation));
    }

    /**
     * 删除流程跟催员分配
     */
    @PreAuthorize("@ss.hasPermi('loan_allocation:loan_allocation:remove')")
    @Log(title = "流程跟催员分配", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(loanAllocationService.deleteLoanAllocationByIds(ids));
    }
}
