package com.ruoyi.assign_table.mapper;

import java.util.List;
import com.ruoyi.assign_table.domain.AssignTable;

/**
 * 指派Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface AssignTableMapper 
{
    /**
     * 查询指派
     * 
     * @param id 指派主键
     * @return 指派
     */
    public AssignTable selectAssignTableById(Long id);

    /**
     * 查询指派列表
     * 
     * @param assignTable 指派
     * @return 指派集合
     */
    public List<AssignTable> selectAssignTableList(AssignTable assignTable);

    /**
     * 新增指派
     * 
     * @param assignTable 指派
     * @return 结果
     */
    public int insertAssignTable(AssignTable assignTable);

    /**
     * 修改指派
     * 
     * @param assignTable 指派
     * @return 结果
     */
    public int updateAssignTable(AssignTable assignTable);

    /**
     * 删除指派
     * 
     * @param id 指派主键
     * @return 结果
     */
    public int deleteAssignTableById(Long id);

    /**
     * 批量删除指派
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAssignTableByIds(Long[] ids);
}
