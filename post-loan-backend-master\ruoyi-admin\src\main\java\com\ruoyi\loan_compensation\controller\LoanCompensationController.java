package com.ruoyi.loan_compensation.controller;

import java.util.List;
import java.util.ArrayList;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.loan_settle.domain.LoanSettle;
import com.ruoyi.trial_balance.service.ITrialBalanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.loan_compensation.domain.LoanCompensation;
import com.ruoyi.loan_compensation.service.ILoanCompensationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.MimeTypeUtils;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.framework.config.ServerConfig;

import static com.ruoyi.common.utils.DateUtils.getNowDate;

/**
 * 代偿Controller
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Slf4j
@RestController
@RequestMapping("/loan_compensation/loan_compensation")
public class LoanCompensationController extends BaseController {
    @Autowired
    private ILoanCompensationService loanCompensationService;

    @Autowired
    private ITrialBalanceService trialBalanceService;

    @Autowired
    private ServerConfig serverConfig;

    /**
     * 查询代偿列表
     */
    // @PreAuthorize("@ss.hasPermi('loan_compensation:loan_compensation:list')")
    @GetMapping("/list")
    @Anonymous
    public TableDataInfo list(LoanCompensation loanCompensation) {
        startPage();
        List<LoanCompensation> list = loanCompensationService.selectLoanCompensationList(loanCompensation);
        for (LoanCompensation info : list) {
            info.setTrialBalance(trialBalanceService.selectTrialBalanceByIddc(info.getId()));
        }
        return getDataTable(list);
    }

    /**
     * 查询流程代偿详情
     */
    @PreAuthorize("@ss.hasPermi('loan_compensation:loan_compensation:list')")
    @GetMapping("/detail")
    // @Anonymous
    public AjaxResult detail(LoanCompensation loanCompensation) {
        startPage();
        LoanCompensation list = loanCompensationService
                .selectLoanCompensationByIds(String.valueOf(loanCompensation.getLoanId()));
        logger.info("查询流程代偿详情:{}", list);
        if (list != null) {
            list.setTrialBalance(trialBalanceService.selectTrialBalanceByIddc(list.getId()));
        }
        return success(list);
    }

    /**
     * 导出代偿列表
     */
    @PreAuthorize("@ss.hasPermi('loan_compensation:loan_compensation:export')")
    @Log(title = "代偿", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LoanCompensation loanCompensation) {
        List<LoanCompensation> list = loanCompensationService.selectLoanCompensationList(loanCompensation);
        ExcelUtil<LoanCompensation> util = new ExcelUtil<LoanCompensation>(LoanCompensation.class);
        util.exportExcel(response, list, "代偿数据");
    }

    /**
     * 获取代偿详细信息
     */
    @PreAuthorize("@ss.hasPermi('loan_compensation:loan_compensation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        LoanCompensation list = loanCompensationService.selectLoanCompensationById(id);
        if (list != null) {
            list.setTrialBalance(trialBalanceService.selectTrialBalanceByIddc(list.getId()));
        }
        return success(list);
    }

    /**
     * 新增代偿
     */
    @PreAuthorize("@ss.hasPermi('loan_compensation:loan_compensation:add')")
    @Log(title = "代偿", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LoanCompensation loanCompensation) {
        loanCompensation.setCreateBy(getUsername());
        loanCompensation.setCreateDate(getNowDate());
        loanCompensationService.insertLoanCompensation(loanCompensation);
        return success(loanCompensation);
    }

    /**
     * 发起代偿（带贷款状态验证）
     */
    @PreAuthorize("@ss.hasPermi('loan_compensation:loan_compensation:initiate')")
    @Log(title = "发起代偿", businessType = BusinessType.INSERT)
    @PostMapping("/initiate")
    public AjaxResult initiateCompensation(@RequestBody LoanCompensation loanCompensation) {
        try {
            log.info("发起代偿申请，贷款ID: {}", loanCompensation.getLoanId());

            // 设置创建者
            loanCompensation.setCreateBy(getUsername());

            // 调用服务方法，包含状态验证和更新
            return loanCompensationService.initiateCompensation(loanCompensation);
        } catch (Exception e) {
            log.error("发起代偿申请失败", e);
            return error("发起代偿申请失败：" + e.getMessage());
        }
    }

    /**
     * 修改代偿
     */
    @PreAuthorize("@ss.hasPermi('loan_compensation:loan_compensation:edit')")
    @Log(title = "代偿", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LoanCompensation loanCompensation) {
        try {
            // 参数验证
            if (loanCompensation == null) {
                return error("请求参数不能为空");
            }

            if (StringUtils.isEmpty(loanCompensation.getId())) {
                return error("代偿ID不能为空");
            }

            // 检查记录是否存在
            LoanCompensation existingRecord = loanCompensationService
                    .selectLoanCompensationById(loanCompensation.getId());
            if (existingRecord == null) {
                return error("代偿记录不存在");
            }

            // 设置更新信息
            loanCompensation.setUpdateBy(getUsername());
            loanCompensation.setUpdateDate(getNowDate());

            // 执行更新操作
            int result = loanCompensationService.updateLoanCompensation(loanCompensation);

            if (result > 0) {
                return success("更新成功");
            } else {
                return error("更新失败，请检查数据是否正确");
            }
        } catch (Exception e) {
            logger.error("代偿更新失败", e);
            return error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除代偿
     */
    @PreAuthorize("@ss.hasPermi('loan_compensation:loan_compensation:remove')")
    @Log(title = "代偿", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(loanCompensationService.deleteLoanCompensationByIds(ids));
    }
}
