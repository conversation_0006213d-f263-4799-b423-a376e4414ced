package com.ruoyi.garage.mapper;

import java.util.List;
import com.ruoyi.garage.domain.Garage;

/**
 * 车库Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface GarageMapper 
{
    /**
     * 查询车库
     * 
     * @param id 车库主键
     * @return 车库
     */
    public Garage selectGarageById(Integer id);

    /**
     * 查询车库列表
     * 
     * @param garage 车库
     * @return 车库集合
     */
    public List<Garage> selectGarageList(Garage garage);

    /**
     * 新增车库
     * 
     * @param garage 车库
     * @return 结果
     */
    public int insertGarage(Garage garage);

    /**
     * 修改车库
     * 
     * @param garage 车库
     * @return 结果
     */
    public int updateGarage(Garage garage);

    /**
     * 删除车库
     * 
     * @param id 车库主键
     * @return 结果
     */
    public int deleteGarageById(Integer id);

    /**
     * 批量删除车库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGarageByIds(Integer[] ids);
}
