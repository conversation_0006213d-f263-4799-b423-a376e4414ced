package com.ruoyi.daily_expense_approval.service;

import java.util.List;
import com.ruoyi.daily_expense_approval.domain.DailyExpenseApproval;

/**
 * 日常花费审批Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IDailyExpenseApprovalService 
{
    /**
     * 查询日常花费审批
     * 
     * @param id 日常花费审批主键
     * @return 日常花费审批
     */
    public DailyExpenseApproval selectDailyExpenseApprovalById(Long id);

    /**
     * 查询日常花费审批列表
     * 
     * @param dailyExpenseApproval 日常花费审批
     * @return 日常花费审批集合
     */
    public List<DailyExpenseApproval> selectDailyExpenseApprovalList(DailyExpenseApproval dailyExpenseApproval);

    /**
     * 新增日常花费审批
     * 
     * @param dailyExpenseApproval 日常花费审批
     * @return 结果
     */
    public int insertDailyExpenseApproval(DailyExpenseApproval dailyExpenseApproval);

    /**
     * 修改日常花费审批
     * 
     * @param dailyExpenseApproval 日常花费审批
     * @return 结果
     */
    public int updateDailyExpenseApproval(DailyExpenseApproval dailyExpenseApproval);

    /**
     * 批量删除日常花费审批
     * 
     * @param ids 需要删除的日常花费审批主键集合
     * @return 结果
     */
    public int deleteDailyExpenseApprovalByIds(Long[] ids);

    /**
     * 删除日常花费审批信息
     *
     * @param id 日常花费审批主键
     * @return 结果
     */
    public int deleteDailyExpenseApprovalById(Long id);

    /**
     * 审批日常花费申请
     *
     * @param id 日常花费审批主键
     * @param approvalStatus 审批状态（1-通过，2-拒绝）
     * @param approvalRemark 审批备注
     * @return 结果
     */
    public int approveDailyExpense(Long id, String approvalStatus, String approvalRemark);
}
