package com.ruoyi.loan_reminder.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.loan_reminder.domain.LoanReminder;

/**
 * 催记Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface LoanReminderMapper {
    /**
     * 查询催记
     * 
     * @param id 催记主键
     * @return 催记
     */
    public LoanReminder selectLoanReminderById(String id);

    /**
     * 查询催记列表
     * 
     * @param loanReminder 催记
     * @return 催记集合
     */
    public List<LoanReminder> selectLoanReminderList(LoanReminder loanReminder);

    /**
     * 查询催记详情
     * 
     * @param loanReminder 催记
     * @return 催记详情
     */
    public LoanReminder selectLoanReminderListDetail(LoanReminder loanReminder);

    /**
     * 新增催记
     * 
     * @param loanReminder 催记
     * @return 结果
     */
    public int insertLoanReminder(LoanReminder loanReminder);

    /**
     * 修改催记
     * 
     * @param loanReminder 催记
     * @return 结果
     */
    public int updateLoanReminder(LoanReminder loanReminder);

    /**
     * 删除催记
     * 
     * @param id 催记主键
     * @return 结果
     */
    public int deleteLoanReminderById(String id);

    /**
     * 批量删除催记
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLoanReminderByIds(String[] ids);

    /**
     * 审核催记
     * 
     * @param loanReminder 催记
     * @return 结果
     */
    public int updateLoanReminderApproval(LoanReminder loanReminder);

    /**
     * 根据贷款ID查询贷后还款状态
     * 
     * @param loanId 贷款ID
     * @return 贷后还款状态
     */
    public Integer getAccountLoanRepaymentStatus(Long loanId);
    
    /**
     * 根据贷款ID列表批量查询催记信息
     *
     * @param loanIds 贷款ID列表
     * @return 催记列表
     */
    public List<LoanReminder> selectLoanRemindersByLoanIds(List<Long> loanIds);

    /**
     * 根据贷款ID列表批量查询法诉日志（status=2）
     *
     * @param loanIds 贷款ID列表
     * @return 法诉日志列表
     */
    public List<LoanReminder> selectLitigationRemindersByLoanIds(List<Long> loanIds);

    /**
     * 更新贷款的贷后还款状态
     * 
     * @param loanId          贷款ID
     * @param repaymentStatus 贷后还款状态
     * @return 结果
     */
//    public int updateAccountLoanRepaymentStatus(@Param("loanId") Long loanId,@Param("repaymentStatus") Integer repaymentStatus);

    /**
     * 查询某法诉案件的最新一条催记记录
     * @param litigationId 法诉案件ID
     * @return 最新的催记
     */
    public LoanReminder selectLatestLoanReminderByLitigationId(Long litigationId);

    /**
     * 根据法诉案件ID列表批量查询最新催记记录
     * @param litigationIds 法诉案件ID列表
     * @return 催记列表（每个litigation_id只返回最新的一条记录）
     */
    public List<LoanReminder> selectLatestLoanRemindersByLitigationIds(List<Long> litigationIds);

    /**
     * 获取产品列表
     * @return 产品列表
     */
    public List<java.util.Map<String, Object>> selectProductList();
}
