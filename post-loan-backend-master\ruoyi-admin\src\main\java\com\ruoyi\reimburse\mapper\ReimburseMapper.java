package com.ruoyi.reimburse.mapper;

import java.util.List;
import com.ruoyi.reimburse.domain.Reimburse;

/**
 * 报销记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ReimburseMapper 
{
    /**
     * 查询报销记录
     * 
     * @param id 报销记录主键
     * @return 报销记录
     */
    public Reimburse selectReimburseById(String id);

    /**
     * 查询报销记录列表
     * 
     * @param reimburse 报销记录
     * @return 报销记录集合
     */
    public List<Reimburse> selectReimburseList(Reimburse reimburse);

    /**
     * 新增报销记录
     * 
     * @param reimburse 报销记录
     * @return 结果
     */
    public int insertReimburse(Reimburse reimburse);

    /**
     * 修改报销记录
     * 
     * @param reimburse 报销记录
     * @return 结果
     */
    public int updateReimburse(Reimburse reimburse);

    /**
     * 删除报销记录
     * 
     * @param id 报销记录主键
     * @return 结果
     */
    public int deleteReimburseById(String id);

    /**
     * 批量删除报销记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteReimburseByIds(String[] ids);
}
