package com.ruoyi.litigation_cost.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 法诉费用明细对象 litigation_cost
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
public class LitigationCost extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 关联法诉表ID */
    @Excel(name = "关联法诉表ID")
    private Long litigationCaseId;

    /** 律师费 */
    @Excel(name = "律师费")
    private BigDecimal lawyerFee;

    /** 法诉费 */
    @Excel(name = "法诉费")
    private BigDecimal litigationFee;

    /** 保全费 */
    @Excel(name = "保全费")
    private BigDecimal preservationFee;

    /** 布控费 */
    @Excel(name = "布控费")
    private BigDecimal surveillanceFee;

    /** 公告费 */
    @Excel(name = "公告费")
    private BigDecimal announcementFee;

    /** 评估费 */
    @Excel(name = "评估费")
    private BigDecimal appraisalFee;

    /** 执行费 */
    @Excel(name = "执行费")
    private BigDecimal executionFee;

    /** 违约金 */
    @Excel(name = "违约金")
    private BigDecimal penalty;

    /** 担保费 */
    @Excel(name = "担保费")
    private BigDecimal guaranteeFee;

    /** 居间费 */
    @Excel(name = "居间费")
    private BigDecimal intermediaryFee;

    /** 代偿金 */
    @Excel(name = "代偿金")
    private BigDecimal compensity;

    /** 判决金额 */
    @Excel(name = "判决金额")
    private BigDecimal judgmentAmount;

    /** 利息 */
    @Excel(name = "利息")
    private BigDecimal interest;

    /** 其他欠款 */
    @Excel(name = "其他欠款")
    private BigDecimal otherAmountsOwed;

    /** 保险费 */
    @Excel(name = "保险费")
    private BigDecimal insurance;

    /** 合计金额 */
    @Excel(name = "合计金额")
    private BigDecimal totalMoney;

    /** 审批状态(0-通过   1-拒绝) */
    @Excel(name = "审批状态(0-通过   1-拒绝)")
    private String approvalStatus;

    /** 拒绝理由 */
    @Excel(name = "拒绝理由")
    private String reasons;

    /** 审批人姓名 */
    @Excel(name = "审批人姓名")
    private String approveBy;

    /** 审批人角色 */
    @Excel(name = "审批人角色")
    private String approveRole;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date applicationTime;

    /** 申请人 */
    @Excel(name = "申请人")
    private String applicationBy;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLitigationCaseId(Long litigationCaseId) 
    {
        this.litigationCaseId = litigationCaseId;
    }

    public Long getLitigationCaseId() 
    {
        return litigationCaseId;
    }

    public void setLawyerFee(BigDecimal lawyerFee) 
    {
        this.lawyerFee = lawyerFee;
    }

    public BigDecimal getLawyerFee() 
    {
        return lawyerFee;
    }

    public void setLitigationFee(BigDecimal litigationFee) 
    {
        this.litigationFee = litigationFee;
    }

    public BigDecimal getLitigationFee() 
    {
        return litigationFee;
    }

    public void setPreservationFee(BigDecimal preservationFee) 
    {
        this.preservationFee = preservationFee;
    }

    public BigDecimal getPreservationFee() 
    {
        return preservationFee;
    }

    public void setSurveillanceFee(BigDecimal surveillanceFee) 
    {
        this.surveillanceFee = surveillanceFee;
    }

    public BigDecimal getSurveillanceFee() 
    {
        return surveillanceFee;
    }

    public void setAnnouncementFee(BigDecimal announcementFee) 
    {
        this.announcementFee = announcementFee;
    }

    public BigDecimal getAnnouncementFee() 
    {
        return announcementFee;
    }

    public void setAppraisalFee(BigDecimal appraisalFee) 
    {
        this.appraisalFee = appraisalFee;
    }

    public BigDecimal getAppraisalFee() 
    {
        return appraisalFee;
    }

    public void setExecutionFee(BigDecimal executionFee) 
    {
        this.executionFee = executionFee;
    }

    public BigDecimal getExecutionFee()
    {
        return executionFee;
    }

    public void setPenalty(BigDecimal penalty)
    {
        this.penalty = penalty;
    }

    public BigDecimal getPenalty() 
    {
        return penalty;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) 
    {
        this.guaranteeFee = guaranteeFee;
    }

    public BigDecimal getGuaranteeFee() 
    {
        return guaranteeFee;
    }

    public void setIntermediaryFee(BigDecimal intermediaryFee) 
    {
        this.intermediaryFee = intermediaryFee;
    }

    public BigDecimal getIntermediaryFee() 
    {
        return intermediaryFee;
    }

    public void setCompensity(BigDecimal compensity) 
    {
        this.compensity = compensity;
    }

    public BigDecimal getCompensity() 
    {
        return compensity;
    }

    public void setOtherAmountsOwed(BigDecimal otherAmountsOwed) 
    {
        this.otherAmountsOwed = otherAmountsOwed;
    }

    public BigDecimal getOtherAmountsOwed()
    {
        return otherAmountsOwed;
    }

    public void setJudgmentAmount(BigDecimal judgmentAmount)
    {
        this.judgmentAmount = judgmentAmount;
    }

    public BigDecimal getJudgmentAmount()
    {
        return judgmentAmount;
    }

    public void setInterest(BigDecimal interest)
    {
        this.interest = interest;
    }

    public BigDecimal getInterest()
    {
        return interest;
    }

    public void setInsurance(BigDecimal insurance)
    {
        this.insurance = insurance;
    }

    public BigDecimal getInsurance()
    {
        return insurance;
    }

    public void setTotalMoney(BigDecimal totalMoney)
    {
        this.totalMoney = totalMoney;
    }

    public BigDecimal getTotalMoney() 
    {
        return totalMoney;
    }

    public void setApprovalStatus(String approvalStatus) 
    {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatus() 
    {
        return approvalStatus;
    }

    public void setReasons(String reasons) 
    {
        this.reasons = reasons;
    }

    public String getReasons() 
    {
        return reasons;
    }

    public void setApproveBy(String approveBy) 
    {
        this.approveBy = approveBy;
    }

    public String getApproveBy() 
    {
        return approveBy;
    }

    public void setApproveRole(String approveRole) 
    {
        this.approveRole = approveRole;
    }

    public String getApproveRole() 
    {
        return approveRole;
    }

    public void setApplicationTime(Date applicationTime) 
    {
        this.applicationTime = applicationTime;
    }

    public Date getApplicationTime() 
    {
        return applicationTime;
    }

    public void setApplicationBy(String applicationBy) 
    {
        this.applicationBy = applicationBy;
    }

    public String getApplicationBy() 
    {
        return applicationBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("litigationCaseId", getLitigationCaseId())
            .append("lawyerFee", getLawyerFee())
            .append("litigationFee", getLitigationFee())
            .append("preservationFee", getPreservationFee())
            .append("surveillanceFee", getSurveillanceFee())
            .append("announcementFee", getAnnouncementFee())
            .append("appraisalFee", getAppraisalFee())
            .append("executionFee", getExecutionFee())
            .append("penalty", getPenalty())
            .append("guaranteeFee", getGuaranteeFee())
            .append("intermediaryFee", getIntermediaryFee())
            .append("compensity", getCompensity())
            .append("judgmentAmount", getJudgmentAmount())
            .append("interest", getInterest())
            .append("otherAmountsOwed", getOtherAmountsOwed())
            .append("insurance", getInsurance())
            .append("approvalStatus", getApprovalStatus())
            .append("reasons", getReasons())
            .append("approveBy", getApproveBy())
            .append("approveRole", getApproveRole())
            .append("applicationTime", getApplicationTime())
            .append("applicationBy", getApplicationBy())
            .append("totalMoney", getTotalMoney())
            .toString();
    }
}
