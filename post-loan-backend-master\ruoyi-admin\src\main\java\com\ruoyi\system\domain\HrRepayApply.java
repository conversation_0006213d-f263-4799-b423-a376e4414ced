package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 华瑞提前还款对象 hr_repay_apply
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
public class HrRepayApply extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyId;

    /** 身份证号码 */
    @Excel(name = "身份证号码")
    private String certId;

    /** 借据编号(华瑞) */
    @Excel(name = "借据编号(华瑞)")
    private String loanId;

    /** 还款流水号/代偿申请号(华瑞) */
    @Excel(name = "还款流水号/代偿申请号(华瑞)")
    private String repayApplyId;

    /** 还款账号 */
    @Excel(name = "还款账号")
    private String repayAcct;

    /** 提前结清类型:1-结清贷款 2-代偿 */
    @Excel(name = "提前结清类型:1-结清贷款 2-代偿")
    private String repayType;

    /** 还款总金额(含担保费) */
    @Excel(name = "还款总金额(含担保费)")
    private String totalRepayAmt;

    /** 还款金额(银行余额) */
    @Excel(name = "还款金额(银行余额)")
    private String repayAmt;

    /** 担保费 */
    @Excel(name = "担保费")
    private String feeAmt;

    /** 类型：1-已申请 */
    @Excel(name = "类型：1-已申请")
    private String type;

    /** 还款状态：0-初始化 1-还款成功 2-还款中 3-还款失败 4-部分还款成功 */
    @Excel(name = "还款状态：0-初始化 1-还款成功 2-还款中 3-还款失败 4-部分还款成功")
    private String repayStatus;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 代偿本金 */
    @Excel(name = "代偿本金")
    private String comPrincipal;

    /** 代偿利息 */
    @Excel(name = "代偿利息")
    private String comInterest;

    /** 代偿罚息 */
    @Excel(name = "代偿罚息")
    private String comOverdueFee;

    /** 代偿金额 */
    @Excel(name = "代偿金额")
    private String comAmt;

    /** 代偿失败原因 */
    @Excel(name = "代偿失败原因")
    private String comFailReason;

    /** 代偿申请时间 */
    @Excel(name = "代偿申请时间")
    private String comTime;

    /** 代偿类型：2-全额代偿 */
    @Excel(name = "代偿类型：2-全额代偿")
    private String comType;

    /** 代偿状态：02-代偿中 03-代偿成功 04-代偿失败 */
    @Excel(name = "代偿状态：02-代偿中 03-代偿成功 04-代偿失败")
    private String comStatus;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setApplyId(String applyId) 
    {
        this.applyId = applyId;
    }

    public String getApplyId() 
    {
        return applyId;
    }

    public void setCertId(String certId) 
    {
        this.certId = certId;
    }

    public String getCertId() 
    {
        return certId;
    }

    public void setLoanId(String loanId) 
    {
        this.loanId = loanId;
    }

    public String getLoanId() 
    {
        return loanId;
    }

    public void setRepayApplyId(String repayApplyId) 
    {
        this.repayApplyId = repayApplyId;
    }

    public String getRepayApplyId() 
    {
        return repayApplyId;
    }

    public void setRepayAcct(String repayAcct) 
    {
        this.repayAcct = repayAcct;
    }

    public String getRepayAcct() 
    {
        return repayAcct;
    }

    public void setRepayType(String repayType) 
    {
        this.repayType = repayType;
    }

    public String getRepayType() 
    {
        return repayType;
    }

    public void setTotalRepayAmt(String totalRepayAmt) 
    {
        this.totalRepayAmt = totalRepayAmt;
    }

    public String getTotalRepayAmt() 
    {
        return totalRepayAmt;
    }

    public void setRepayAmt(String repayAmt) 
    {
        this.repayAmt = repayAmt;
    }

    public String getRepayAmt() 
    {
        return repayAmt;
    }

    public void setFeeAmt(String feeAmt) 
    {
        this.feeAmt = feeAmt;
    }

    public String getFeeAmt() 
    {
        return feeAmt;
    }

    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    public void setRepayStatus(String repayStatus) 
    {
        this.repayStatus = repayStatus;
    }

    public String getRepayStatus() 
    {
        return repayStatus;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setComPrincipal(String comPrincipal) 
    {
        this.comPrincipal = comPrincipal;
    }

    public String getComPrincipal() 
    {
        return comPrincipal;
    }

    public void setComInterest(String comInterest) 
    {
        this.comInterest = comInterest;
    }

    public String getComInterest() 
    {
        return comInterest;
    }

    public void setComOverdueFee(String comOverdueFee) 
    {
        this.comOverdueFee = comOverdueFee;
    }

    public String getComOverdueFee() 
    {
        return comOverdueFee;
    }

    public void setComAmt(String comAmt) 
    {
        this.comAmt = comAmt;
    }

    public String getComAmt() 
    {
        return comAmt;
    }

    public void setComFailReason(String comFailReason) 
    {
        this.comFailReason = comFailReason;
    }

    public String getComFailReason() 
    {
        return comFailReason;
    }

    public void setComTime(String comTime) 
    {
        this.comTime = comTime;
    }

    public String getComTime() 
    {
        return comTime;
    }

    public void setComType(String comType) 
    {
        this.comType = comType;
    }

    public String getComType() 
    {
        return comType;
    }

    public void setComStatus(String comStatus) 
    {
        this.comStatus = comStatus;
    }

    public String getComStatus() 
    {
        return comStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("applyId", getApplyId())
            .append("certId", getCertId())
            .append("loanId", getLoanId())
            .append("repayApplyId", getRepayApplyId())
            .append("repayAcct", getRepayAcct())
            .append("repayType", getRepayType())
            .append("totalRepayAmt", getTotalRepayAmt())
            .append("repayAmt", getRepayAmt())
            .append("feeAmt", getFeeAmt())
            .append("type", getType())
            .append("repayStatus", getRepayStatus())
            .append("createDate", getCreateDate())
            .append("createBy", getCreateBy())
            .append("comPrincipal", getComPrincipal())
            .append("comInterest", getComInterest())
            .append("comOverdueFee", getComOverdueFee())
            .append("comAmt", getComAmt())
            .append("comFailReason", getComFailReason())
            .append("comTime", getComTime())
            .append("comType", getComType())
            .append("comStatus", getComStatus())
            .toString();
    }
}
