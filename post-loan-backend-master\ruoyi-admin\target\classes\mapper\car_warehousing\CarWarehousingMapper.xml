<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.car_warehousing.mapper.CarWarehousingMapper">
    
    <resultMap type="CarWarehousing" id="CarWarehousingResult">
        <result property="id"    column="id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="teamId"    column="team_id"    />
        <result property="garageId"    column="garage_id"    />
        <result property="libraryStatus"    column="library_status"    />
        <result property="inboundTime"    column="inbound_time"    />
        <result property="outboundTime"    column="outbound_time"    />
        <result property="locatingCommission"    column="locating_commission"    />
        <result property="carLocation"    column="car_location"    />
        <result property="parkingFee"    column="parking_fee"    />
        <result property="sellingFares"    column="selling_fares"    />
        <result property="outbounStatus"    column="outboun_status"    />
        <result property="accidentFlag"    column="accident_flag"    />
    </resultMap>

    <sql id="selectCarWarehousingVo">
        select id, create_by, create_date, update_by, update_date, apply_no, team_id, garage_id, library_status, inbound_time, outbound_time, locating_commission, car_location, parking_fee, selling_fares, outboun_status, accident_flag from car_warehousing
    </sql>

    <select id="selectCarWarehousingList" parameterType="CarWarehousing" resultMap="CarWarehousingResult">
        <include refid="selectCarWarehousingVo"/>
        <where>  
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="garageId != null "> and garage_id = #{garageId}</if>
            <if test="inboundTime != null "> and inbound_time = #{inboundTime}</if>
            <if test="keyStatus != null "> and key_status = #{keyStatus}</if>
            <if test="collectionMethod != null "> and collection_method = #{collectionMethod}</if>
            <if test="parkingFee != null "> and parking_fee = #{parkingFee}</if>
            <if test="sellingFares != null "> and selling_fares = #{sellingFares}</if>
            <if test="outbounStatus != null "> and outboun_status = #{outbounStatus}</if>
            <if test="accidentFlag != null "> and accident_flag = #{accidentFlag}</if>
        </where>
    </select>
    
    <select id="selectCarWarehousingById" parameterType="String" resultMap="CarWarehousingResult">
        <include refid="selectCarWarehousingVo"/>
        where id = #{id}
    </select>

    <insert id="insertCarWarehousing" parameterType="CarWarehousing">
        insert into car_warehousing
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="applyNo != null">apply_no,</if>
            <if test="teamId != null">team_id,</if>
            <if test="garageId != null">garage_id,</if>
            <if test="libraryStatus != null">library_status,</if>
            <if test="inboundTime != null">inbound_time,</if>
            <if test="outboundTime != null">outbound_time,</if>
            <if test="locatingCommission != null">locating_commission,</if>
            <if test="keyStatus != null">key_status,</if>
            <if test="collectionMethod != null">collection_method,</if>
            <if test="carLocation != null">car_location,</if>
            <if test="status != null">status,</if>
            <if test="parkingFee != null">parking_fee,</if>
            <if test="sellingFares != null">selling_fares,</if>
            <if test="outbounStatus != null">outboun_status,</if>
            <if test="accidentFlag != null">accident_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="garageId != null">#{garageId},</if>
            <if test="libraryStatus != null">#{libraryStatus},</if>
            <if test="inboundTime != null">#{inboundTime},</if>
            <if test="outboundTime != null">#{outboundTime},</if>
            <if test="locatingCommission != null">#{locatingCommission},</if>
            <if test="keyStatus != null">#{keyStatus},</if>
            <if test="collectionMethod != null">#{collectionMethod},</if>
            <if test="carLocation != null">#{carLocation},</if>
            <if test="status != null">#{status},</if>
            <if test="parkingFee != null">#{parkingFee},</if>
            <if test="sellingFares != null">#{sellingFares},</if>
            <if test="outbounStatus != null">#{outbounStatus},</if>
            <if test="accidentFlag != null">#{accidentFlag},</if>
         </trim>
    </insert>

    <update id="updateCarWarehousing" parameterType="CarWarehousing">
        UPDATE car_warehousing
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="garageId != null">garage_id = #{garageId},</if>
            <if test="libraryStatus != null">library_status = #{libraryStatus},</if>
            <if test="inboundTime != null">inbound_time = #{inboundTime},</if>
            <if test="outboundTime != null">outbound_time = #{outboundTime},</if>
            <if test="locatingCommission != null">locating_commission = #{locatingCommission},</if>
            <if test="carLocation != null">car_location = #{carLocation},</if>
            <if test="parkingFee != null">parking_fee = #{parkingFee},</if>
            <if test="sellingFares != null">selling_fares = #{sellingFares},</if>
            <if test="outbounStatus != null">outboun_status = #{outbounStatus},</if>
            <if test="accidentFlag != null">accident_flag = #{accidentFlag},</if>
        </trim>
        where apply_no = #{applyNo}
    </update>

    <delete id="deleteCarWarehousingById" parameterType="String">
        delete from car_warehousing where id = #{id}
    </delete>

    <delete id="deleteCarWarehousingByIds" parameterType="String">
        delete from car_warehousing where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>