package com.ruoyi.car_order_examine.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 找车费用审批对象 car_order_examine
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
public class CarOrderExamine extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    // 审批状态常量
    public static final int STATUS_PENDING = 0;        // 未审批
    public static final int STATUS_APPROVED = 1;       // 全部通过
    public static final int STATUS_LEGAL_SUPERVISOR = 3;  // 法诉主管审批
    public static final int STATUS_DIRECTOR = 4;       // 总监审批
    public static final int STATUS_DIRECTOR_CC = 5;    // 总监抄送
    public static final int STATUS_GENERAL_MANAGER = 6; // 总经理/董事长审批(抄送)
    public static final int STATUS_REJECTED = 7;       // 拒绝

    /** $column.columnComment */
    private String id;

    /** 创建时间 */
    private Date createDate;

    /** 更新时间 */
    private Date updateDate;

    /** 交通费 */
    @Excel(name = "交通费")
    private BigDecimal transportationFee;

    /** 拖车费 */
    @Excel(name = "拖车费")
    private BigDecimal towingFee;

    /** 贴机费 */
    @Excel(name = "贴机费")
    private BigDecimal trackerInstallationFee;

    /** 其他报销 */
    @Excel(name = "其他报销")
    private BigDecimal otherReimbursement;

    /** 合计费用 */
    @Excel(name = "合计费用")
    private BigDecimal totalCost;

    /** 审批：0-未审批，1-通过，2-拒绝 */
    @Excel(name = "审批：0-未审批，1-全部通过，3-法诉主管审批  4-总监审批  5-总监抄送  6-总经理/董事长审批(抄送)  7-拒绝")
    private Integer status;

    /** 审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date examineTime;

    /** 拒绝原因 */
    private String rejectReason;

    /** 当前审批人 */
    private String currentApprover;

    /** 审批历史记录 */
    private String approvalHistory;

//    public void setId(String id)
//    {
//        this.id = id;
//    }
//
//    public String getId()
//    {
//        return id;
//    }
//
//    public void setCreateDate(Date createDate)
//    {
//        this.createDate = createDate;
//    }
//
//    public Date getCreateDate()
//    {
//        return createDate;
//    }
//
//    public void setUpdateDate(Date updateDate)
//    {
//        this.updateDate = updateDate;
//    }
//
//    public Date getUpdateDate()
//    {
//        return updateDate;
//    }
//
//    public void setTransportationFee(BigDecimal transportationFee)
//    {
//        this.transportationFee = transportationFee;
//    }
//
//    public BigDecimal getTransportationFee()
//    {
//        return transportationFee;
//    }
//
//    public void setTowingFee(BigDecimal towingFee)
//    {
//        this.towingFee = towingFee;
//    }
//
//    public BigDecimal getTowingFee()
//    {
//        return towingFee;
//    }
//
//    public void setTrackerInstallationFee(BigDecimal trackerInstallationFee)
//    {
//        this.trackerInstallationFee = trackerInstallationFee;
//    }
//
//    public BigDecimal getTrackerInstallationFee()
//    {
//        return trackerInstallationFee;
//    }
//
//    public void setOtherReimbursement(BigDecimal otherReimbursement)
//    {
//        this.otherReimbursement = otherReimbursement;
//    }
//
//    public BigDecimal getOtherReimbursement()
//    {
//        return otherReimbursement;
//    }
//
//    public void setTotalCost(BigDecimal totalCost)
//    {
//        this.totalCost = totalCost;
//    }
//
//    public BigDecimal getTotalCost()
//    {
//        return totalCost;
//    }
//
//    public void setStatus(Integer status)
//    {
//        this.status = status;
//    }
//
//    public Integer getStatus()
//    {
//        return status;
//    }
//
//    public void setExamineTime(Date examineTime)
//    {
//        this.examineTime = examineTime;
//    }
//
//    public Date getExamineTime()
//    {
//        return examineTime;
//    }
//
//    public void setRejectReason(String rejectReason)
//    {
//        this.rejectReason = rejectReason;
//    }
//
//    public String getRejectReason()
//    {
//        return rejectReason;
//    }
//
//    @Override
//    public String toString() {
//        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
//            .append("id", getId())
//            .append("createBy", getCreateBy())
//            .append("createDate", getCreateDate())
//            .append("updateBy", getUpdateBy())
//            .append("updateDate", getUpdateDate())
//            .append("transportationFee", getTransportationFee())
//            .append("towingFee", getTowingFee())
//            .append("trackerInstallationFee", getTrackerInstallationFee())
//            .append("otherReimbursement", getOtherReimbursement())
//            .append("totalCost", getTotalCost())
//            .append("status", getStatus())
//            .append("examineTime", getExamineTime())
//            .append("rejectReason", getRejectReason())
//            .toString();
//    }

    /**
     * 获取下一个审批状态
     */
    public Integer getNextApprovalStatus() {
        if (this.status == null || this.status == STATUS_PENDING) {
            return STATUS_LEGAL_SUPERVISOR;
        } else if (this.status == STATUS_LEGAL_SUPERVISOR) {
            return STATUS_DIRECTOR;
        } else if (this.status == STATUS_DIRECTOR) {
            return STATUS_DIRECTOR_CC;
        } else if (this.status == STATUS_DIRECTOR_CC) {
            return STATUS_GENERAL_MANAGER;
        } else if (this.status == STATUS_GENERAL_MANAGER) {
            return STATUS_APPROVED;
        }
        return this.status;
    }

    /**
     * 检查是否可以进行审批
     */
    public boolean canApprove(String userRole) {
        if (this.status == null || this.status == STATUS_PENDING) {
            return "法诉主管".equals(userRole);
        } else if (this.status == STATUS_LEGAL_SUPERVISOR) {
            return "总监".equals(userRole);
        } else if (this.status == STATUS_DIRECTOR) {
            return "总监".equals(userRole);
        } else if (this.status == STATUS_DIRECTOR_CC) {
            return "总经理".equals(userRole) || "董事长".equals(userRole);
        }
        return false;
    }

    /**
     * 获取当前审批阶段描述
     */
    public String getStatusDescription() {
        if (this.status == null || this.status == STATUS_PENDING) {
            return "未审批";
        } else if (this.status == STATUS_LEGAL_SUPERVISOR) {
            return "法诉主管审批";
        } else if (this.status == STATUS_DIRECTOR) {
            return "总监审批";
        } else if (this.status == STATUS_DIRECTOR_CC) {
            return "总监抄送";
        } else if (this.status == STATUS_GENERAL_MANAGER) {
            return "总经理/董事长审批(抄送)";
        } else if (this.status == STATUS_APPROVED) {
            return "全部通过";
        } else if (this.status == STATUS_REJECTED) {
            return "已拒绝";
        }
        return "未知状态";
    }
}
