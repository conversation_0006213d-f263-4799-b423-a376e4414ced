package com.ruoyi.partner_info.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.partner_info.domain.PartnerInfo;
import com.ruoyi.partner_info.service.IPartnerInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 资金方管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
@RestController
@RequestMapping("/partner_info/partner_info")
public class PartnerInfoController extends BaseController
{
    @Autowired
    private IPartnerInfoService partnerInfoService;

    /**
     * 查询资金方管理列表
     */
    @PreAuthorize("@ss.hasPermi('partner_info:partner_info:list')")
    @GetMapping("/list")
    public TableDataInfo list(PartnerInfo partnerInfo)
    {
        startPage();
        List<PartnerInfo> list = partnerInfoService.selectPartnerInfoList(partnerInfo);
        return getDataTable(list);
    }

    /**
     * 导出资金方管理列表
     */
    @PreAuthorize("@ss.hasPermi('partner_info:partner_info:export')")
    @Log(title = "资金方管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PartnerInfo partnerInfo)
    {
        List<PartnerInfo> list = partnerInfoService.selectPartnerInfoList(partnerInfo);
        ExcelUtil<PartnerInfo> util = new ExcelUtil<PartnerInfo>(PartnerInfo.class);
        util.exportExcel(response, list, "资金方管理数据");
    }

    /**
     * 获取资金方管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('partner_info:partner_info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(partnerInfoService.selectPartnerInfoById(id));
    }

    /**
     * 新增资金方管理
     */
    @PreAuthorize("@ss.hasPermi('partner_info:partner_info:add')")
    @Log(title = "资金方管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PartnerInfo partnerInfo)
    {
        return toAjax(partnerInfoService.insertPartnerInfo(partnerInfo));
    }

    /**
     * 修改资金方管理
     */
    @PreAuthorize("@ss.hasPermi('partner_info:partner_info:edit')")
    @Log(title = "资金方管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PartnerInfo partnerInfo)
    {
        return toAjax(partnerInfoService.updatePartnerInfo(partnerInfo));
    }

    /**
     * 删除资金方管理
     */
    @PreAuthorize("@ss.hasPermi('partner_info:partner_info:remove')")
    @Log(title = "资金方管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(partnerInfoService.deletePartnerInfoByIds(ids));
    }

    /**
     * 查询所有资金方的id和orgName
     */
    @GetMapping("/id-name-list")
    public AjaxResult idNameList() {
        return success(partnerInfoService.selectPartnerInfoIdAndNameList());
    }
}
