<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HrRepayApplyMapper">
    
    <resultMap type="HrRepayApply" id="HrRepayApplyResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="certId"    column="cert_id"    />
        <result property="loanId"    column="loan_id"    />
        <result property="repayApplyId"    column="repay_apply_id"    />
        <result property="repayAcct"    column="repay_acct"    />
        <result property="repayType"    column="repay_type"    />
        <result property="totalRepayAmt"    column="total_repay_amt"    />
        <result property="repayAmt"    column="repay_amt"    />
        <result property="feeAmt"    column="fee_amt"    />
        <result property="type"    column="type"    />
        <result property="repayStatus"    column="repay_status"    />
        <result property="createDate"    column="create_date"    />
        <result property="comPrincipal"    column="com_principal"    />
        <result property="comInterest"    column="com_interest"    />
        <result property="comOverdueFee"    column="com_overdue_fee"    />
        <result property="comAmt"    column="com_amt"    />
        <result property="comFailReason"    column="com_fail_reason"    />
        <result property="comTime"    column="com_time"    />
        <result property="comType"    column="com_type"    />
        <result property="comStatus"    column="com_status"    />
    </resultMap>

    <sql id="selectHrRepayApplyVo">
        select id, apply_id, cert_id, loan_id, repay_apply_id, repay_acct, repay_type, total_repay_amt, repay_amt, fee_amt, type, repay_status, create_date, com_principal, com_interest, com_overdue_fee, com_amt, com_fail_reason, com_time, com_type, com_status from hr_repay_apply
    </sql>

    <select id="selectHrRepayApplyList" parameterType="HrRepayApply" resultMap="HrRepayApplyResult">
        <include refid="selectHrRepayApplyVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="certId != null  and certId != ''"> and cert_id = #{certId}</if>
            <if test="loanId != null  and loanId != ''"> and loan_id = #{loanId}</if>
            <if test="repayApplyId != null  and repayApplyId != ''"> and repay_apply_id = #{repayApplyId}</if>
            <if test="repayType != null  and repayType != ''"> and repay_type = #{repayType}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="repayStatus != null  and repayStatus != ''"> and repay_status = #{repayStatus}</if>
        </where>
    </select>
    
    <select id="selectHrRepayApplyLists" parameterType="HrRepayApply" resultMap="HrRepayApplyResult">
        <include refid="selectHrRepayApplyVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="certId != null  and certId != ''"> and cert_id = #{certId}</if>
            <if test="loanId != null  and loanId != ''"> and loan_id = #{loanId}</if>
            <if test="repayApplyId != null  and repayApplyId != ''"> and repay_apply_id = #{repayApplyId}</if>
            <if test="repayType != null  and repayType != ''"> and repay_type = #{repayType}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="repayStatus != null  and repayStatus != ''"> and repay_status = #{repayStatus}</if>
        </where>
        limit 1
    </select>
    
    <select id="selectHrRepayApplyById" parameterType="String" resultMap="HrRepayApplyResult">
        <include refid="selectHrRepayApplyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertHrRepayApply" parameterType="HrRepayApply" useGeneratedKeys="true" keyProperty="id">
        insert into hr_repay_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyId != null">apply_id,</if>
            <if test="certId != null">cert_id,</if>
            <if test="loanId != null">loan_id,</if>
            <if test="repayApplyId != null">repay_apply_id,</if>
            <if test="repayAcct != null">repay_acct,</if>
            <if test="repayType != null">repay_type,</if>
            <if test="totalRepayAmt != null">total_repay_amt,</if>
            <if test="repayAmt != null">repay_amt,</if>
            <if test="feeAmt != null">fee_amt,</if>
            <if test="type != null">type,</if>
            <if test="repayStatus != null">repay_status,</if>
            <if test="createDate != null">create_date,</if>
            <if test="comPrincipal != null">com_principal,</if>
            <if test="comInterest != null">com_interest,</if>
            <if test="comOverdueFee != null">com_overdue_fee,</if>
            <if test="comAmt != null">com_amt,</if>
            <if test="comFailReason != null">com_fail_reason,</if>
            <if test="comTime != null">com_time,</if>
            <if test="comType != null">com_type,</if>
            <if test="comStatus != null">com_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyId != null">#{applyId},</if>
            <if test="certId != null">#{certId},</if>
            <if test="loanId != null">#{loanId},</if>
            <if test="repayApplyId != null">#{repayApplyId},</if>
            <if test="repayAcct != null">#{repayAcct},</if>
            <if test="repayType != null">#{repayType},</if>
            <if test="totalRepayAmt != null">#{totalRepayAmt},</if>
            <if test="repayAmt != null">#{repayAmt},</if>
            <if test="feeAmt != null">#{feeAmt},</if>
            <if test="type != null">#{type},</if>
            <if test="repayStatus != null">#{repayStatus},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="comPrincipal != null">#{comPrincipal},</if>
            <if test="comInterest != null">#{comInterest},</if>
            <if test="comOverdueFee != null">#{comOverdueFee},</if>
            <if test="comAmt != null">#{comAmt},</if>
            <if test="comFailReason != null">#{comFailReason},</if>
            <if test="comTime != null">#{comTime},</if>
            <if test="comType != null">#{comType},</if>
            <if test="comStatus != null">#{comStatus},</if>
         </trim>
    </insert>

    <update id="updateHrRepayApply" parameterType="HrRepayApply">
        update hr_repay_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyId != null">apply_id = #{applyId},</if>
            <if test="certId != null">cert_id = #{certId},</if>
            <if test="loanId != null">loan_id = #{loanId},</if>
            <if test="repayApplyId != null">repay_apply_id = #{repayApplyId},</if>
            <if test="repayAcct != null">repay_acct = #{repayAcct},</if>
            <if test="repayType != null">repay_type = #{repayType},</if>
            <if test="totalRepayAmt != null">total_repay_amt = #{totalRepayAmt},</if>
            <if test="repayAmt != null">repay_amt = #{repayAmt},</if>
            <if test="feeAmt != null">fee_amt = #{feeAmt},</if>
            <if test="type != null">type = #{type},</if>
            <if test="repayStatus != null">repay_status = #{repayStatus},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="comPrincipal != null">com_principal = #{comPrincipal},</if>
            <if test="comInterest != null">com_interest = #{comInterest},</if>
            <if test="comOverdueFee != null">com_overdue_fee = #{comOverdueFee},</if>
            <if test="comAmt != null">com_amt = #{comAmt},</if>
            <if test="comFailReason != null">com_fail_reason = #{comFailReason},</if>
            <if test="comTime != null">com_time = #{comTime},</if>
            <if test="comType != null">com_type = #{comType},</if>
            <if test="comStatus != null">com_status = #{comStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHrRepayApplyById" parameterType="String">
        delete from hr_repay_apply where id = #{id}
    </delete>

    <delete id="deleteHrRepayApplyByIds" parameterType="String">
        delete from hr_repay_apply where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 