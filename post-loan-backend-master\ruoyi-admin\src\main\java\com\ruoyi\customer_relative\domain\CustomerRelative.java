package com.ruoyi.customer_relative.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 客户联系人信息对象 customer_relative
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public class CustomerRelative extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyNo;

    /** 客户编号 */
    @Excel(name = "客户编号")
    private String customerId;

    /** 联系人关系 */
    @Excel(name = "联系人关系")
    private String relationship;

    /** 联系人姓名 */
    @Excel(name = "联系人姓名")
    private String customerName;

    /** 联系人手机号码 */
    @Excel(name = "联系人手机号码")
    private String mobileNo;

    /** 联系人证件类型 */
    @Excel(name = "联系人证件类型")
    private String certType;

    /** 联系人证件号码 */
    @Excel(name = "联系人证件号码")
    private String certId;

    /** 联系人工作单位 */
    @Excel(name = "联系人工作单位")
    private String workCorp;

    /** 省份 */
    @Excel(name = "省份")
    private String province;

    /** 城市 */
    @Excel(name = "城市")
    private String city;

    /** 地区 */
    @Excel(name = "地区")
    private String borough;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String address;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date createDate;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date updateDate;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setApplyNo(String applyNo) 
    {
        this.applyNo = applyNo;
    }

    public String getApplyNo() 
    {
        return applyNo;
    }

    public void setCustomerId(String customerId) 
    {
        this.customerId = customerId;
    }

    public String getCustomerId() 
    {
        return customerId;
    }

    public void setRelationship(String relationship) 
    {
        this.relationship = relationship;
    }

    public String getRelationship() 
    {
        return relationship;
    }

    public void setCustomerName(String customerName) 
    {
        this.customerName = customerName;
    }

    public String getCustomerName() 
    {
        return customerName;
    }

    public void setMobileNo(String mobileNo) 
    {
        this.mobileNo = mobileNo;
    }

    public String getMobileNo() 
    {
        return mobileNo;
    }

    public void setCertType(String certType) 
    {
        this.certType = certType;
    }

    public String getCertType() 
    {
        return certType;
    }

    public void setCertId(String certId) 
    {
        this.certId = certId;
    }

    public String getCertId() 
    {
        return certId;
    }

    public void setWorkCorp(String workCorp) 
    {
        this.workCorp = workCorp;
    }

    public String getWorkCorp() 
    {
        return workCorp;
    }

    public void setProvince(String province) 
    {
        this.province = province;
    }

    public String getProvince() 
    {
        return province;
    }

    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }

    public void setBorough(String borough) 
    {
        this.borough = borough;
    }

    public String getBorough() 
    {
        return borough;
    }

    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("applyNo", getApplyNo())
            .append("customerId", getCustomerId())
            .append("relationship", getRelationship())
            .append("customerName", getCustomerName())
            .append("mobileNo", getMobileNo())
            .append("certType", getCertType())
            .append("certId", getCertId())
            .append("workCorp", getWorkCorp())
            .append("province", getProvince())
            .append("city", getCity())
            .append("borough", getBorough())
            .append("address", getAddress())
            .append("createBy", getCreateBy())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .append("remarks", getRemarks())
            .toString();
    }
}
