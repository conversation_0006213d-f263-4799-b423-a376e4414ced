package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SyPrepaymentApply;

/**
 * 苏银提前还款Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface SyPrepaymentApplyMapper 
{
    /**
     * 查询苏银提前还款
     * 
     * @param id 苏银提前还款主键
     * @return 苏银提前还款
     */
    public SyPrepaymentApply selectSyPrepaymentApplyById(String id);

    /**
     * 查询苏银提前还款列表
     * 
     * @param syPrepaymentApply 苏银提前还款
     * @return 苏银提前还款集合
     */
    public List<SyPrepaymentApply> selectSyPrepaymentApplyList(SyPrepaymentApply syPrepaymentApply);


    public SyPrepaymentApply selectSyPrepaymentApplyLists(SyPrepaymentApply syPrepaymentApply);

    /**
     * 新增苏银提前还款
     * 
     * @param syPrepaymentApply 苏银提前还款
     * @return 结果
     */
    public int insertSyPrepaymentApply(SyPrepaymentApply syPrepaymentApply);

    /**
     * 修改苏银提前还款
     * 
     * @param syPrepaymentApply 苏银提前还款
     * @return 结果
     */
    public int updateSyPrepaymentApply(SyPrepaymentApply syPrepaymentApply);

    /**
     * 删除苏银提前还款
     * 
     * @param id 苏银提前还款主键
     * @return 结果
     */
    public int deleteSyPrepaymentApplyById(String id);

    /**
     * 批量删除苏银提前还款
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSyPrepaymentApplyByIds(String[] ids);
}
