package com.ruoyi.loan_compensation.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.loan_compensation.domain.LoanCompensation;

/**
 * 代偿Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface LoanCompensationMapper {
    /**
     * 查询代偿
     * 
     * @param id 代偿主键
     * @return 代偿
     */
    public LoanCompensation selectLoanCompensationById(String id);

    /**
     * 查询代偿列表
     * 
     * @param loanCompensation 代偿
     * @return 代偿集合
     */
    public List<LoanCompensation> selectLoanCompensationList(LoanCompensation loanCompensation);

    /**
     * 新增代偿
     * 
     * @param loanCompensation 代偿
     * @return 结果
     */
    public int insertLoanCompensation(LoanCompensation loanCompensation);

    /**
     * 修改代偿
     * 
     * @param loanCompensation 代偿
     * @return 结果
     */
    public int updateLoanCompensation(LoanCompensation loanCompensation);

    /**
     * 删除代偿
     * 
     * @param id 代偿主键
     * @return 结果
     */
    public int deleteLoanCompensationById(String id);

    /**
     * 批量删除代偿
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLoanCompensationByIds(String[] ids);

    /**
     * 根据贷款ID查询贷后还款状态
     * 
     * @param loanId 贷款ID
     * @return 贷后还款状态
     */
//    public Integer getAccountLoanRepaymentStatus(Long loanId);

    /**
     * 更新贷款的贷后还款状态
     * 
     * @param loanId          贷款ID
     * @param repaymentStatus 贷后还款状态
     * @return 结果
     */
//    public int updateAccountLoanRepaymentStatus(@Param("loanId") Long loanId,@Param("repaymentStatus") Integer repaymentStatus);

    /**
     * 根据流程ID查询代偿
     * 
     * @param loanId 流程ID
     * @return 代偿
     */
    public LoanCompensation selectLoanCompensationByIds(String loanId);
}
