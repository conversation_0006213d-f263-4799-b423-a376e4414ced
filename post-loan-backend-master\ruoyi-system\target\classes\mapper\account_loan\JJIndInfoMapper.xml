<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.JJIndInfoMapper">
    
    <resultMap type="JJIndInfo" id="IndInfoResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="formerName"    column="former_name"    />
        <result property="highestEducation"    column="highest_education"    />
        <result property="degree"    column="degree"    />
        <result property="marriageStatus"    column="marriage_status"    />
        <result property="incomeType"    column="income_type"    />
        <result property="monthlyIncome"    column="monthly_income"    />
        <result property="incomeDescribe"    column="income_describe"    />
        <result property="liveStatus"    column="live_status"    />
        <result property="localResidentAge"    column="local_resident_age"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="haveChildren"    column="have_children"    />
        <result property="localResidentStatus"    column="local_resident_status"    />
        <result property="haveHouse"    column="have_house"    />
        <result property="spouseName"    column="spouse_name"    />
        <result property="spouseCardId"    column="spouse_card_id"    />
        <result property="spousePhone"    column="spouse_phone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="borough"    column="borough"    />
        <result property="liveAddress"    column="live_address"    />
        <result property="detailAddress"    column="detail_address"    />
        <result property="zipCode"    column="zip_code"    />
        <result property="remarks"    column="remarks"    />
        <result property="docProvince"    column="doc_province"    />
        <result property="docCity"    column="doc_city"    />
        <result property="docBorough"    column="doc_borough"    />
        <result property="docAddress"    column="doc_address"    />
        <result property="docDetailAddress"    column="doc_detail_address"    />
        <result property="docAddressType"    column="doc_address_type"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="censusProvince"    column="census_province"    />
        <result property="censusCity"    column="census_city"    />
        <result property="censusArea"    column="census_area"    />
        <result property="censusAddress"    column="census_address"    />
        <result property="mortgageContact"    column="mortgage_contact"    />
        <result property="mortgageContactPhone"    column="mortgage_contact_phone"    />
        <result property="personalIncome"    column="personal_income"    />
        <result property="familyIncome"    column="family_income"    />
        <result property="email"    column="email"    />
        <result property="repaySource"    column="repay_source"    />
        <result property="customerSource"    column="customer_source"    />
        <result property="localType"    column="local_type"    />
        <result property="mortgageAgentId"    column="mortgage_agent_id"    />
        <result property="spouseUnit"    column="spouse_unit"    />
        <result property="localFlag"    column="local_flag"    />
    </resultMap>

    <sql id="selectIndInfoVo">
        select * from ind_info
    </sql>

    <select id="selectIndInfoList" parameterType="JJIndInfo" resultMap="IndInfoResult">
        <include refid="selectIndInfoVo"/>
        <where>
            DATE(create_date) = CURDATE() or DATE(update_date) = CURDATE()
        </where>
    </select>
    
    <select id="selectIndInfoById" parameterType="String" resultMap="IndInfoResult">
        <include refid="selectIndInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertIndInfo" parameterType="JJIndInfo">
        insert into ind_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerId != null and customerId != ''">customer_id,</if>
            <if test="formerName != null">former_name,</if>
            <if test="highestEducation != null">highest_education,</if>
            <if test="degree != null">degree,</if>
            <if test="marriageStatus != null">marriage_status,</if>
            <if test="incomeType != null">income_type,</if>
            <if test="monthlyIncome != null">monthly_income,</if>
            <if test="incomeDescribe != null">income_describe,</if>
            <if test="liveStatus != null">live_status,</if>
            <if test="localResidentAge != null">local_resident_age,</if>
            <if test="mobilePhone != null">mobile_phone,</if>
            <if test="haveChildren != null">have_children,</if>
            <if test="localResidentStatus != null">local_resident_status,</if>
            <if test="haveHouse != null">have_house,</if>
            <if test="spouseName != null">spouse_name,</if>
            <if test="spouseCardId != null">spouse_card_id,</if>
            <if test="spousePhone != null">spouse_phone,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="borough != null">borough,</if>
            <if test="liveAddress != null">live_address,</if>
            <if test="detailAddress != null">detail_address,</if>
            <if test="zipCode != null">zip_code,</if>
            <if test="remarks != null">remarks,</if>
            <if test="docProvince != null">doc_province,</if>
            <if test="docCity != null">doc_city,</if>
            <if test="docBorough != null">doc_borough,</if>
            <if test="docAddress != null">doc_address,</if>
            <if test="docDetailAddress != null">doc_detail_address,</if>
            <if test="docAddressType != null">doc_address_type,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="censusProvince != null">census_province,</if>
            <if test="censusCity != null">census_city,</if>
            <if test="censusArea != null">census_area,</if>
            <if test="censusAddress != null">census_address,</if>
            <if test="mortgageContact != null">mortgage_contact,</if>
            <if test="mortgageContactPhone != null">mortgage_contact_phone,</if>
            <if test="personalIncome != null">personal_income,</if>
            <if test="familyIncome != null">family_income,</if>
            <if test="email != null">email,</if>
            <if test="repaySource != null">repay_source,</if>
            <if test="customerSource != null">customer_source,</if>
            <if test="localType != null">local_type,</if>
            <if test="mortgageAgentId != null">mortgage_agent_id,</if>
            <if test="spouseUnit != null">spouse_unit,</if>
            <if test="localFlag != null">local_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerId != null and customerId != ''">#{customerId},</if>
            <if test="formerName != null">#{formerName},</if>
            <if test="highestEducation != null">#{highestEducation},</if>
            <if test="degree != null">#{degree},</if>
            <if test="marriageStatus != null">#{marriageStatus},</if>
            <if test="incomeType != null">#{incomeType},</if>
            <if test="monthlyIncome != null">#{monthlyIncome},</if>
            <if test="incomeDescribe != null">#{incomeDescribe},</if>
            <if test="liveStatus != null">#{liveStatus},</if>
            <if test="localResidentAge != null">#{localResidentAge},</if>
            <if test="mobilePhone != null">#{mobilePhone},</if>
            <if test="haveChildren != null">#{haveChildren},</if>
            <if test="localResidentStatus != null">#{localResidentStatus},</if>
            <if test="haveHouse != null">#{haveHouse},</if>
            <if test="spouseName != null">#{spouseName},</if>
            <if test="spouseCardId != null">#{spouseCardId},</if>
            <if test="spousePhone != null">#{spousePhone},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="borough != null">#{borough},</if>
            <if test="liveAddress != null">#{liveAddress},</if>
            <if test="detailAddress != null">#{detailAddress},</if>
            <if test="zipCode != null">#{zipCode},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="docProvince != null">#{docProvince},</if>
            <if test="docCity != null">#{docCity},</if>
            <if test="docBorough != null">#{docBorough},</if>
            <if test="docAddress != null">#{docAddress},</if>
            <if test="docDetailAddress != null">#{docDetailAddress},</if>
            <if test="docAddressType != null">#{docAddressType},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="censusProvince != null">#{censusProvince},</if>
            <if test="censusCity != null">#{censusCity},</if>
            <if test="censusArea != null">#{censusArea},</if>
            <if test="censusAddress != null">#{censusAddress},</if>
            <if test="mortgageContact != null">#{mortgageContact},</if>
            <if test="mortgageContactPhone != null">#{mortgageContactPhone},</if>
            <if test="personalIncome != null">#{personalIncome},</if>
            <if test="familyIncome != null">#{familyIncome},</if>
            <if test="email != null">#{email},</if>
            <if test="repaySource != null">#{repaySource},</if>
            <if test="customerSource != null">#{customerSource},</if>
            <if test="localType != null">#{localType},</if>
            <if test="mortgageAgentId != null">#{mortgageAgentId},</if>
            <if test="spouseUnit != null">#{spouseUnit},</if>
            <if test="localFlag != null">#{localFlag},</if>
         </trim>
    </insert>

    <update id="updateIndInfo" parameterType="JJIndInfo">
        update ind_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null and customerId != ''">customer_id = #{customerId},</if>
            <if test="formerName != null">former_name = #{formerName},</if>
            <if test="highestEducation != null">highest_education = #{highestEducation},</if>
            <if test="degree != null">degree = #{degree},</if>
            <if test="marriageStatus != null">marriage_status = #{marriageStatus},</if>
            <if test="incomeType != null">income_type = #{incomeType},</if>
            <if test="monthlyIncome != null">monthly_income = #{monthlyIncome},</if>
            <if test="incomeDescribe != null">income_describe = #{incomeDescribe},</if>
            <if test="liveStatus != null">live_status = #{liveStatus},</if>
            <if test="localResidentAge != null">local_resident_age = #{localResidentAge},</if>
            <if test="mobilePhone != null">mobile_phone = #{mobilePhone},</if>
            <if test="haveChildren != null">have_children = #{haveChildren},</if>
            <if test="localResidentStatus != null">local_resident_status = #{localResidentStatus},</if>
            <if test="haveHouse != null">have_house = #{haveHouse},</if>
            <if test="spouseName != null">spouse_name = #{spouseName},</if>
            <if test="spouseCardId != null">spouse_card_id = #{spouseCardId},</if>
            <if test="spousePhone != null">spouse_phone = #{spousePhone},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="borough != null">borough = #{borough},</if>
            <if test="liveAddress != null">live_address = #{liveAddress},</if>
            <if test="detailAddress != null">detail_address = #{detailAddress},</if>
            <if test="zipCode != null">zip_code = #{zipCode},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="docProvince != null">doc_province = #{docProvince},</if>
            <if test="docCity != null">doc_city = #{docCity},</if>
            <if test="docBorough != null">doc_borough = #{docBorough},</if>
            <if test="docAddress != null">doc_address = #{docAddress},</if>
            <if test="docDetailAddress != null">doc_detail_address = #{docDetailAddress},</if>
            <if test="docAddressType != null">doc_address_type = #{docAddressType},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="censusProvince != null">census_province = #{censusProvince},</if>
            <if test="censusCity != null">census_city = #{censusCity},</if>
            <if test="censusArea != null">census_area = #{censusArea},</if>
            <if test="censusAddress != null">census_address = #{censusAddress},</if>
            <if test="mortgageContact != null">mortgage_contact = #{mortgageContact},</if>
            <if test="mortgageContactPhone != null">mortgage_contact_phone = #{mortgageContactPhone},</if>
            <if test="personalIncome != null">personal_income = #{personalIncome},</if>
            <if test="familyIncome != null">family_income = #{familyIncome},</if>
            <if test="email != null">email = #{email},</if>
            <if test="repaySource != null">repay_source = #{repaySource},</if>
            <if test="customerSource != null">customer_source = #{customerSource},</if>
            <if test="localType != null">local_type = #{localType},</if>
            <if test="mortgageAgentId != null">mortgage_agent_id = #{mortgageAgentId},</if>
            <if test="spouseUnit != null">spouse_unit = #{spouseUnit},</if>
            <if test="localFlag != null">local_flag = #{localFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIndInfoById" parameterType="String">
        delete from ind_info where id = #{id}
    </delete>

    <delete id="deleteIndInfoByIds" parameterType="String">
        delete from ind_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>