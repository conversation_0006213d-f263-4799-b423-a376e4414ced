package com.ruoyi.garage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.garage.domain.Garage;
import com.ruoyi.garage.service.IGarageService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 车库Controller
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/garage/garage")
public class GarageController extends BaseController
{
    @Autowired
    private IGarageService garageService;

    /**
     * 查询车库列表
     */
    @PreAuthorize("@ss.hasPermi('garage:garage:list')")
    @GetMapping("/list")
    public TableDataInfo list(Garage garage)
    {
        startPage();
        List<Garage> list = garageService.selectGarageList(garage);
        return getDataTable(list);
    }

    /**
     * 导出车库列表
     */
    @PreAuthorize("@ss.hasPermi('garage:garage:export')")
    @Log(title = "车库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Garage garage)
    {
        List<Garage> list = garageService.selectGarageList(garage);
        ExcelUtil<Garage> util = new ExcelUtil<Garage>(Garage.class);
        util.exportExcel(response, list, "车库数据");
    }

    /**
     * 获取车库详细信息
     */
    @PreAuthorize("@ss.hasPermi('garage:garage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(garageService.selectGarageById(id));
    }

    /**
     * 新增车库
     */
    @PreAuthorize("@ss.hasPermi('garage:garage:add')")
    @Log(title = "车库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Garage garage)
    {
        return toAjax(garageService.insertGarage(garage));
    }

    /**
     * 修改车库
     */
    @PreAuthorize("@ss.hasPermi('garage:garage:edit')")
    @Log(title = "车库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Garage garage)
    {
        return toAjax(garageService.updateGarage(garage));
    }

    /**
     * 删除车库
     */
    @PreAuthorize("@ss.hasPermi('garage:garage:remove')")
    @Log(title = "车库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(garageService.deleteGarageByIds(ids));
    }
}
