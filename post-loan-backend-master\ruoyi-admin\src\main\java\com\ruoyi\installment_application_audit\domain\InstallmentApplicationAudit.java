package com.ruoyi.installment_application_audit.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.vw_loan_compensation.domain.VwLoanCompensation;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 分期申请审核中间对象 installment_application_audit
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public class InstallmentApplicationAudit extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 分期期数 */
    @Excel(name = "分期期数")
    private Long periodCount;

    /** 每期账单金额 */
    @Excel(name = "每期账单金额")
    private BigDecimal billAmount;

    /** 每期还款日 */
    @Excel(name = "每期还款日")
    private Integer repayDay;

    /** 账号类型 */
    @Excel(name = "账号类型")
    private String accountType;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 审核状态 1=待审核 2=通过 3=拒绝 */
    @Excel(name = "审核状态 1=待审核 2=通过 3=拒绝")
    private Integer status;

    /** 拒绝理由 */
    @Excel(name = "拒绝理由")
    private String reason;

    /** 流程ID */
    @Excel(name = "流程ID")
    private Long loanId;

    /** 尾款金额 */
    @Excel(name = "尾款金额")
    private BigDecimal tailAmount;

    /** 申请分期金额 */
    @Excel(name = "申请分期金额")
    private BigDecimal applyAmount;

    /** 尾款支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "尾款支付时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date tailPayTime;

    /** 代偿详情 */
    @Excel(name = "代偿详情")
    private VwLoanCompensation compensateDetail;

    public void setCompensateDetail(VwLoanCompensation compensateDetail) { this.compensateDetail = compensateDetail; }

    public VwLoanCompensation getCompensateDetail() { return this.compensateDetail; }

    private String customerName;
    public void setCustomerName(String customerName) { this.customerName = customerName; }
    public String getCustomerName() { return this.customerName; }

    private String plateNo;
    public void setPlateNo(String plateNo) { this.plateNo = plateNo; }
    public String getPlateNo() { return this.plateNo; }

    private String jgName;
    public void  setJgName(String jgName) { this.jgName = jgName; }
    public String getJgName() { return this.jgName; }

    private String partnerId;
    public void setPartnerId(String partnerId) { this.partnerId = partnerId; }
    public String getPartnerId() { return this.partnerId; }

    private Date tailPayTimeStart;
    private Date tailPayTimeEnd;
    public Date getTailPayTimeStart() { return tailPayTimeStart; }
    public void setTailPayTimeStart(Date tailPayTimeStart) { this.tailPayTimeStart = tailPayTimeStart; }
    public Date getTailPayTimeEnd() { return tailPayTimeEnd; }
    public void setTailPayTimeEnd(Date tailPayTimeEnd) { this.tailPayTimeEnd = tailPayTimeEnd; }


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setPeriodCount(Long periodCount) {
        this.periodCount = periodCount;
    }

    public Long getPeriodCount() {
        return periodCount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setRepayDay(Integer repayDay) {
        this.repayDay = repayDay;
    }

    public Integer getRepayDay() {
        return repayDay;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getReason() {
        return reason;
    }

    public void setLoanId(Long loanId) {
        this.loanId = loanId;
    }

    public Long getLoanId() {
        return loanId;
    }

    public void setTailAmount(BigDecimal tailAmount) {
        this.tailAmount = tailAmount;
    }

    public BigDecimal getTailAmount() {
        return tailAmount;
    }

    public void setApplyAmount(BigDecimal applyAmount) {
        this.applyAmount = applyAmount;
    }

    public BigDecimal getApplyAmount() {
        return applyAmount;
    }

    public void setTailPayTime(Date tailPayTime) {
        this.tailPayTime = tailPayTime;
    }

    public Date getTailPayTime() {
        return tailPayTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("periodCount", getPeriodCount())
                .append("billAmount", getBillAmount())
                .append("repayDay", getRepayDay())
                .append("accountType", getAccountType())
                .append("createBy", getCreateBy())
                .append("createDate", getCreateDate())
                .append("updateDate", getUpdateDate())
                .append("status", getStatus())
                .append("reason", getReason())
                .append("loanId", getLoanId())
                .append("tailAmount", getTailAmount())
                .append("applyAmount", getApplyAmount())
                .append("tailPayTime", getTailPayTime())
                .toString();
    }
}
