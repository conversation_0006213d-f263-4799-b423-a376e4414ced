package com.ruoyi.loan_settle.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.loan_compensation.domain.LoanCompensation;
import com.ruoyi.loan_compensation.mapper.LoanCompensationMapper;
import com.ruoyi.loan_reminder.domain.LoanReminder;
import com.ruoyi.loan_reminder.mapper.LoanReminderMapper;
import com.ruoyi.loan_settle.mapper.LoanSettleMapper;
import com.ruoyi.loan_settle.domain.LoanSettle;
import com.ruoyi.loan_settle.service.ILoanSettleService;

/**
 * 流程结清Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
public class LoanSettleServiceImpl implements ILoanSettleService {
    private static final Logger log = LoggerFactory.getLogger(LoanSettleServiceImpl.class);

    @Autowired
    private LoanSettleMapper loanSettleMapper;

    @Autowired
    private LoanCompensationMapper loanCompensationMapper;

    @Autowired
    private LoanReminderMapper loanReminderMapper;

    /**
     * 查询流程结清
     * 
     * @param id 流程结清主键
     * @return 流程结清
     */
    @Override
    public LoanSettle selectLoanSettleById(String id) {
        return loanSettleMapper.selectLoanSettleById(id);
    }

    /**
     * 查询流程结清列表
     * 
     * @param loanSettle 流程结清
     * @return 流程结清
     */
    @Override
    public List<LoanSettle> selectLoanSettleList(LoanSettle loanSettle) {
        return loanSettleMapper.selectLoanSettleList(loanSettle);
    }

    @Override
    public LoanSettle selectLoanSettleListDetail(LoanSettle loanSettle) {
        return loanSettleMapper.selectLoanSettleListDetail(loanSettle);
    }

    /**
     * 新增流程结清
     * 
     * @param loanSettle 流程结清
     * @return 结果
     */
    @Override
    public int insertLoanSettle(LoanSettle loanSettle) {
        return loanSettleMapper.insertLoanSettle(loanSettle);
    }

    /**
     * 修改流程结清
     * 
     * @param loanSettle 流程结清
     * @return 结果
     */
    @Override
    public int updateLoanSettle(LoanSettle loanSettle) {
        return loanSettleMapper.updateLoanSettle(loanSettle);
    }

    /**
     * 批量删除流程结清
     * 
     * @param ids 需要删除的流程结清主键
     * @return 结果
     */
    @Override
    public int deleteLoanSettleByIds(String[] ids) {
        return loanSettleMapper.deleteLoanSettleByIds(ids);
    }

    /**
     * 删除流程结清信息
     * 
     * @param id 流程结清主键
     * @return 结果
     */
    @Override
    public int deleteLoanSettleById(String id) {
        return loanSettleMapper.deleteLoanSettleById(id);
    }

    /**
     * 计算结清金额
     * 
     * @param loanId 贷款ID
     * @return 包含 totalMoney 和 remainingAmount 的对象数组 [totalMoney, remainingAmount]
     */
    private BigDecimal[] calculateSettlementAmounts(Long loanId) {
        // 查询催记金额总和
        LoanReminder reminder = new LoanReminder();
        reminder.setLoanId(loanId);
        reminder.setStatus("1"); // status为1是代偿催记
        List<LoanReminder> reminderList = loanReminderMapper.selectLoanReminderList(reminder);

        // 计算催记金额总和（totalMoney存储催记金额总和）
        BigDecimal totalReminderAmount = BigDecimal.ZERO;
        if (reminderList != null && !reminderList.isEmpty()) {
            for (LoanReminder item : reminderList) {
                // 汇总所有催记还款金额
                if (item.getBMoney() != null) {
                    totalReminderAmount = totalReminderAmount.add(item.getBMoney());
                }
                if (item.getDMoney() != null) {
                    totalReminderAmount = totalReminderAmount.add(item.getDMoney());
                }
                if (item.getOMoney() != null) {
                    totalReminderAmount = totalReminderAmount.add(item.getOMoney());
                }
                if (item.getCMoney() != null) {
                    totalReminderAmount = totalReminderAmount.add(item.getCMoney());
                }
            }
        }

        // 查询代偿信息获取总金额
        LoanCompensation compensation = new LoanCompensation();
        compensation.setLoanId(loanId);
        List<LoanCompensation> compensationList = loanCompensationMapper.selectLoanCompensationList(compensation);

        BigDecimal compensationTotalMoney = BigDecimal.ZERO;
        if (compensationList != null && !compensationList.isEmpty()) {
            compensationTotalMoney = compensationList.get(0).getTotalMoney();
            if (compensationTotalMoney == null) {
                compensationTotalMoney = BigDecimal.ZERO;
            }
        }

        // 计算结清剩余金额（remainingAmount存储结清计算后的结果）
        BigDecimal remainingAmount = compensationTotalMoney.subtract(totalReminderAmount);
        if (remainingAmount.compareTo(BigDecimal.ZERO) < 0) {
            remainingAmount = BigDecimal.ZERO;
        }

        return new BigDecimal[] { totalReminderAmount, remainingAmount };
    }

    /**
     * 代偿结清功能
     * 从loan_compensation获取数据，减去loan_reminder中status为1的值，保存到loan_settle表
     *
     * @param loanId 贷款ID
     * @return 结果
     */
    //@Override
    //public AjaxResult processCompensationSettlement(Long loanId) {
    //    // 0. 先检查loan_settle表中是否已有该贷款ID的记录
    //    LoanSettle existingSettle = new LoanSettle();
    //    existingSettle.setLoanId(loanId);
    //    existingSettle.setStatus(1);
    //    List<LoanSettle> existingSettleList = loanSettleMapper.selectLoanSettleList(existingSettle);
    //
    //    if (existingSettleList != null && !existingSettleList.isEmpty()) {
    //        // 如果已有记录，直接返回查询到的数据
    //        return AjaxResult.success("已存在代偿结清记录", existingSettleList.get(0));
    //    }
    //
    //    // 1. 查询贷款代偿信息
    //    LoanCompensation compensation = new LoanCompensation();
    //    compensation.setLoanId(loanId);
    //    List<LoanCompensation> compensationList = loanCompensationMapper.selectLoanCompensationList(compensation);
    //
    //    if (compensationList == null || compensationList.isEmpty()) {
    //        return AjaxResult.error("未找到对应的代偿信息");
    //    }
    //
    //    LoanCompensation loanCompensation = compensationList.get(0);
    //
    //    // 2. 查询status为1的催记信息（代偿催记）
    //    LoanReminder reminder = new LoanReminder();
    //    reminder.setLoanId(loanId);
    //    reminder.setStatus(1); // status为1是代偿催记
    //    List<LoanReminder> reminderList = loanReminderMapper.selectLoanReminderList(reminder);
    //
    //    // 3. 计算结清金额
    //    BigDecimal[] amounts = calculateSettlementAmounts(loanId);
    //    BigDecimal totalReminderAmount = amounts[0];
    //    BigDecimal remainingAmount = amounts[1];
    //
    //    // 5. 保存到loan_settle表
    //    LoanSettle loanSettle = new LoanSettle();
    //    loanSettle.setLoanId(loanId);
    //    loanSettle.setApplyId(loanCompensation.getApplyId());
    //    loanSettle.setCustomerName(loanCompensation.getCustomerName());
    //    loanSettle.setSalesman(loanCompensation.getSalesman());
    //    loanSettle.setOrgName(loanCompensation.getOrgName());
    //    loanSettle.setPartnerId(loanCompensation.getPartnerId());
    //    loanSettle.setBank(loanCompensation.getBank());
    //    loanSettle.setLoanAmount(loanCompensation.getLoanAmount());
    //    loanSettle.setOtherDebt(loanCompensation.getOtherDebt());
    //    loanSettle.setTotalMoney(totalReminderAmount); // 催记金额总和
    //    loanSettle.setRemainingAmount(remainingAmount); // 结清剩余金额
    //    loanSettle.setStatus(1); // 1-结清
    //    loanSettle.setExamineStatus(0); // 0-跟催员发起
    //    loanSettle.setLoanStatus(1); // 1-代偿结清
    //    loanSettle.setCreateDate(new Date());
    //    loanSettle.setCreateBy(SecurityContextHolder.getContext().getAuthentication().getName());
    //
    //    int result = loanSettleMapper.insertLoanSettle(loanSettle);
    //    if (result > 0) {
    //        return AjaxResult.success("代偿结清处理成功", loanSettle);
    //    } else {
    //        return AjaxResult.error("代偿结清处理失败");
    //    }
    //}

    @Override
    public boolean hasProcessingSettle(Long loanId) {
        LoanSettle query = new LoanSettle();
        query.setLoanId(loanId);
        // 查询所有该 loanId 的结清流程
        List<LoanSettle> settleList = loanSettleMapper.selectLoanSettleList(query);
        if (settleList == null || settleList.isEmpty()) {
            return false;
        }
        // examineStatus=4 表示拒绝，其他都算正在处理
        for (LoanSettle settle : settleList) {
            if (settle.getExamineStatus() == null || settle.getExamineStatus() != 4) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<LoanSettle> selectProcessingSettlesByLoanId(Long loanId) {
        LoanSettle query = new LoanSettle();
        query.setLoanId(loanId);
        List<LoanSettle> settleList = loanSettleMapper.selectLoanSettleList(query);
        // 只返回未被拒绝的结清流程（examineStatus != 4）
        List<LoanSettle> result = new java.util.ArrayList<>();
        if (settleList != null) {
            for (LoanSettle settle : settleList) {
                if (settle.getExamineStatus() == null || settle.getExamineStatus() != 4) {
                    result.add(settle);
                }
            }
        }
        return result;
    }

    @Override
    public AjaxResult processCompensationSettlement(Long loanId, Integer status) {
        // 0. 先检查loan_settle表中是否已有该贷款ID和status的记录
        LoanSettle query = new LoanSettle();
        query.setLoanId(loanId);
        query.setStatus(status);
        List<LoanSettle> existList = loanSettleMapper.selectLoanSettleList(query);

        // 1. 查询贷款代偿信息
        LoanCompensation compensation = new LoanCompensation();
        compensation.setLoanId(loanId);
        List<LoanCompensation> compensationList = loanCompensationMapper.selectLoanCompensationList(compensation);
        if (compensationList == null || compensationList.isEmpty()) {
            return AjaxResult.error("未找到对应的代偿信息");
        }
        LoanCompensation loanCompensation = compensationList.get(0);
        // 2. 查询status为1的催记信息（代偿催记）
        LoanReminder reminder = new LoanReminder();
        reminder.setLoanId(loanId);
        reminder.setStatus("1"); // status为1是代偿催记
        List<LoanReminder> reminderList = loanReminderMapper.selectLoanReminderList(reminder);
        // 3. 计算结清金额
        BigDecimal[] amounts = calculateSettlementAmounts(loanId);
        BigDecimal totalReminderAmount = amounts[0];
        BigDecimal remainingAmount = amounts[1];
        // 4. 构建新LoanSettle对象
        LoanSettle loanSettle = new LoanSettle();
        loanSettle.setLoanId(loanId);
        loanSettle.setApplyId(loanCompensation.getApplyId());
        loanSettle.setCustomerName(loanCompensation.getCustomerName());
        loanSettle.setSalesman(loanCompensation.getSalesman());
        loanSettle.setOrgName(loanCompensation.getOrgName());
        loanSettle.setPartnerId(loanCompensation.getPartnerId());
        loanSettle.setBank(loanCompensation.getBank());
        loanSettle.setLoanAmount(loanCompensation.getLoanAmount());
        loanSettle.setOtherDebt(loanCompensation.getOtherDebt());
        loanSettle.setTotalMoney(totalReminderAmount); // 催记金额总和
        loanSettle.setRemainingAmount(remainingAmount); // 结清剩余金额
        loanSettle.setStatus(status); // 1-全额结清，2-减免结清
        loanSettle.setExamineStatus(0); // 0-跟催员发起
        loanSettle.setLoanStatus(1); // 1-代偿结清
        loanSettle.setCreateDate(new Date());
        loanSettle.setCreateBy(SecurityContextHolder.getContext().getAuthentication().getName());

        if (existList != null && !existList.isEmpty()) {
            // 有重复，执行update
            LoanSettle exist = existList.get(0);
            loanSettle.setId(exist.getId());
            loanSettleMapper.updateLoanSettle(loanSettle);
            return AjaxResult.success("已存在，已自动更新", loanSettle);
        }
        // 没有重复，正常插入
        int result = loanSettleMapper.insertLoanSettle(loanSettle);
        if (result > 0) {
            return AjaxResult.success("代偿结清处理成功", loanSettle);
        } else {
            return AjaxResult.error("代偿结清处理失败");
        }
    }
}
