package com.ruoyi.coborrower_info.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.coborrower_info.domain.CoborrowerInfo;
import com.ruoyi.coborrower_info.service.ICoborrowerInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 担保人/共借人Controller
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/coborrower_info/coborrower_info")
public class CoborrowerInfoController extends BaseController
{
    @Autowired
    private ICoborrowerInfoService coborrowerInfoService;

    /**
     * 查询担保人/共借人列表
     */
    @PreAuthorize("@ss.hasPermi('coborrower_info:coborrower_info:list')")
    @GetMapping("/list")
    public TableDataInfo list(CoborrowerInfo coborrowerInfo)
    {
        startPage();
        List<CoborrowerInfo> list = coborrowerInfoService.selectCoborrowerInfoList(coborrowerInfo);
        return getDataTable(list);
    }

    /**
     * 导出担保人/共借人列表
     */
    @PreAuthorize("@ss.hasPermi('coborrower_info:coborrower_info:export')")
    @Log(title = "担保人/共借人", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CoborrowerInfo coborrowerInfo)
    {
        List<CoborrowerInfo> list = coborrowerInfoService.selectCoborrowerInfoList(coborrowerInfo);
        ExcelUtil<CoborrowerInfo> util = new ExcelUtil<CoborrowerInfo>(CoborrowerInfo.class);
        util.exportExcel(response, list, "担保人/共借人数据");
    }

    /**
     * 获取担保人/共借人详细信息
     */
    @PreAuthorize("@ss.hasPermi('coborrower_info:coborrower_info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(coborrowerInfoService.selectCoborrowerInfoById(id));
    }

    /**
     * 新增担保人/共借人
     */
    @PreAuthorize("@ss.hasPermi('coborrower_info:coborrower_info:add')")
    @Log(title = "担保人/共借人", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CoborrowerInfo coborrowerInfo)
    {
        return toAjax(coborrowerInfoService.insertCoborrowerInfo(coborrowerInfo));
    }

    /**
     * 修改担保人/共借人
     */
    @PreAuthorize("@ss.hasPermi('coborrower_info:coborrower_info:edit')")
    @Log(title = "担保人/共借人", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CoborrowerInfo coborrowerInfo)
    {
        return toAjax(coborrowerInfoService.updateCoborrowerInfo(coborrowerInfo));
    }

    /**
     * 删除担保人/共借人
     */
    @PreAuthorize("@ss.hasPermi('coborrower_info:coborrower_info:remove')")
    @Log(title = "担保人/共借人", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(coborrowerInfoService.deleteCoborrowerInfoByIds(ids));
    }
}
