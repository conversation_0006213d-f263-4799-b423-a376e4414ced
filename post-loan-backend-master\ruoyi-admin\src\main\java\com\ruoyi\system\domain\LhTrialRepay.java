package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 蓝海还款试算对象 lh_trial_repay
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
public class LhTrialRepay extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyId;

    /** 还款类型:1-提前结清 5-全额代偿 */
    @Excel(name = "还款类型:1-提前结清 5-全额代偿")
    private String repayType;

    /** 约定还款日 */
    @Excel(name = "约定还款日")
    private String repayDate;

    /** 还款金额 */
    @Excel(name = "还款金额")
    private BigDecimal amount;

    /** 还款总金额 */
    @Excel(name = "还款总金额")
    private BigDecimal repayAmount;

    /** 还款总本金 */
    @Excel(name = "还款总本金")
    private BigDecimal repayPrincipal;

    /** 还款总利息 */
    @Excel(name = "还款总利息")
    private BigDecimal repayInterest;

    /** 还款总费用 */
    @Excel(name = "还款总费用")
    private BigDecimal repayFee;

    /** 还款总罚息 */
    @Excel(name = "还款总罚息")
    private BigDecimal repayOverdueFee;

    /** 还款总复利 */
    @Excel(name = "还款总复利")
    private BigDecimal repayCompoundInterest;

    /** 优惠总利息 */
    @Excel(name = "优惠总利息")
    private BigDecimal discountInterest;

    /** 优惠总费用 */
    @Excel(name = "优惠总费用")
    private BigDecimal discountFee;

    /** 状态：0待发起 1已发起 */
    @Excel(name = "状态：0待发起 1已发起")
    private String repayStatus;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 当期担保费 */
    @Excel(name = "当期担保费")
    private BigDecimal currentFee;

    /** 担保费补偿金 */
    @Excel(name = "担保费补偿金")
    private BigDecimal claimFee;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setApplyId(String applyId) 
    {
        this.applyId = applyId;
    }

    public String getApplyId() 
    {
        return applyId;
    }

    public void setRepayType(String repayType) 
    {
        this.repayType = repayType;
    }

    public String getRepayType() 
    {
        return repayType;
    }

    public void setRepayDate(String repayDate) 
    {
        this.repayDate = repayDate;
    }

    public String getRepayDate() 
    {
        return repayDate;
    }

    public void setAmount(BigDecimal amount) 
    {
        this.amount = amount;
    }

    public BigDecimal getAmount() 
    {
        return amount;
    }

    public void setRepayAmount(BigDecimal repayAmount) 
    {
        this.repayAmount = repayAmount;
    }

    public BigDecimal getRepayAmount() 
    {
        return repayAmount;
    }

    public void setRepayPrincipal(BigDecimal repayPrincipal) 
    {
        this.repayPrincipal = repayPrincipal;
    }

    public BigDecimal getRepayPrincipal() 
    {
        return repayPrincipal;
    }

    public void setRepayInterest(BigDecimal repayInterest) 
    {
        this.repayInterest = repayInterest;
    }

    public BigDecimal getRepayInterest() 
    {
        return repayInterest;
    }

    public void setRepayFee(BigDecimal repayFee) 
    {
        this.repayFee = repayFee;
    }

    public BigDecimal getRepayFee() 
    {
        return repayFee;
    }

    public void setRepayOverdueFee(BigDecimal repayOverdueFee) 
    {
        this.repayOverdueFee = repayOverdueFee;
    }

    public BigDecimal getRepayOverdueFee() 
    {
        return repayOverdueFee;
    }

    public void setRepayCompoundInterest(BigDecimal repayCompoundInterest) 
    {
        this.repayCompoundInterest = repayCompoundInterest;
    }

    public BigDecimal getRepayCompoundInterest() 
    {
        return repayCompoundInterest;
    }

    public void setDiscountInterest(BigDecimal discountInterest) 
    {
        this.discountInterest = discountInterest;
    }

    public BigDecimal getDiscountInterest() 
    {
        return discountInterest;
    }

    public void setDiscountFee(BigDecimal discountFee) 
    {
        this.discountFee = discountFee;
    }

    public BigDecimal getDiscountFee() 
    {
        return discountFee;
    }

    public void setRepayStatus(String repayStatus) 
    {
        this.repayStatus = repayStatus;
    }

    public String getRepayStatus() 
    {
        return repayStatus;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    public void setCurrentFee(BigDecimal currentFee) 
    {
        this.currentFee = currentFee;
    }

    public BigDecimal getCurrentFee() 
    {
        return currentFee;
    }

    public void setClaimFee(BigDecimal claimFee) 
    {
        this.claimFee = claimFee;
    }

    public BigDecimal getClaimFee() 
    {
        return claimFee;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("applyId", getApplyId())
            .append("repayType", getRepayType())
            .append("repayDate", getRepayDate())
            .append("amount", getAmount())
            .append("repayAmount", getRepayAmount())
            .append("repayPrincipal", getRepayPrincipal())
            .append("repayInterest", getRepayInterest())
            .append("repayFee", getRepayFee())
            .append("repayOverdueFee", getRepayOverdueFee())
            .append("repayCompoundInterest", getRepayCompoundInterest())
            .append("discountInterest", getDiscountInterest())
            .append("discountFee", getDiscountFee())
            .append("repayStatus", getRepayStatus())
            .append("createBy", getCreateBy())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .append("currentFee", getCurrentFee())
            .append("claimFee", getClaimFee())
            .toString();
    }
}
