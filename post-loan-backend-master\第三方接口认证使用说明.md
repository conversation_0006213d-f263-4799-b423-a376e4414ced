# 第三方数据接口对接说明

## 接口概述
本文档为第三方系统对接我方数据接口的技术说明。接口采用认证+加密的双重安全机制，确保数据传输安全。

## 对接准备
在开始对接前，您需要从我方获取以下信息：
- 系统登录用户名和密码
- 系统访问地址
- AES加密密钥（32位）
- AES初始向量（16位）
- 签名密钥
- 合作方标识（partnerId）

## 接口调用流程

### 第一步：通过网页登录获取访问令牌

1. **打开系统登录页面**
   - 访问地址：`https://dhtest.ichedai.cn/`
   - 使用我方提供的用户名和密码登录

2. **获取Token**
   - 登录成功后，在浏览器开发者工具中查看请求
   - 或在页面中找到token信息（具体位置请咨询我方技术人员）
   - Token格式类似：`eyJhbGciOiJIUzUxMiJ9...`

3. **Token使用说明**
   - Token用于后续接口调用认证
   - 建议缓存token，避免频繁登录
   - Token有过期时间，过期后需重新登录获取

### 第二步：调用数据接口
**接口地址**: `https://dhtest.ichedai.cn/prod-api/api/third-party/receive-data`
**请求方式**: POST
**Content-Type**: application/json

**请求头**:
```
Authorization: Bearer {第一步获取的token}
```

**请求参数**:
```json
{
  "partnerId": "您的合作方标识",        // 必填：我方分配的合作方ID
  "data": "AES加密后的业务数据",       // 必填：业务数据AES加密后Base64编码
  "timestamp": 1690123456789,         // 必填：当前时间戳(毫秒)
  "sign": "请求签名",                  // 必填：按规则生成的MD5签名
  "nonce": "a1b2c3d4e5f6g7h8",        // 可选：防重放随机数，每次请求不同
  "version": "1.0",                   // 可选：接口版本号
  "vin": "车架号",                     // 可选：17位车架号
  "status": 1                         // 可选：车辆状态
}
```

**成功响应**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": "处理结果"
}
```

## 数据加密说明

### AES加密
业务数据需要使用AES-128-CBC模式加密：

1. **加密步骤**:
   ```
   原始数据 -> AES加密 -> Base64编码 -> 放入data字段
   ```

2. **加密参数**:
   - 算法：AES-128-CBC
   - 密钥：32位密钥（我方提供）
   - 初始向量：16位IV（我方提供）
   - 填充：PKCS7Padding

3. **示例代码（Java）**:
   ```java
   // 使用我方提供的密钥和IV
   String secretKey = "xG9wsVjgofBHOCppVx9V0N7z6omBnbNd";
   String iv = "9cYqorGFpPHx8LSo";
   String signKey = ">HLiZM=<BDY(=D?bW4Q#.n_:i4Uy;ba";  


   // 加密业务数据
   String encryptedData = AESUtil.encrypt(businessData, secretKey, iv);
   ```

## nonce生成说明

### 什么是nonce
nonce（Number Once）是"只使用一次的数字"，用于防止重放攻击，确保每次请求的唯一性。

### 生成方法

**Java示例**:
```java
// 方法1：使用UUID（推荐）
String nonce = UUID.randomUUID().toString().replace("-", "");

// 方法2：时间戳+随机数
String nonce = System.currentTimeMillis() + "_" + new Random().nextInt(100000);

// 方法3：随机字符串
String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
StringBuilder nonce = new StringBuilder();
Random random = new Random();
for (int i = 0; i < 16; i++) {
    nonce.append(chars.charAt(random.nextInt(chars.length())));
}
```

**Python示例**:
```python
import uuid
import random
import string

# 方法1：UUID
nonce = str(uuid.uuid4()).replace('-', '')

# 方法2：随机字符串
nonce = ''.join(random.choices(string.ascii_letters + string.digits, k=16))
```

**JavaScript示例**:
```javascript
// 方法1：随机字符串
function generateNonce(length = 16) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}
```

### 使用要求
1. **每次请求都必须生成新的nonce**
2. **长度建议8-32位字符**
3. **包含字母和数字的随机组合**
4. **如果提供nonce，必须参与签名计算**

### 签名生成规则
为确保请求安全，需要对请求参数进行签名：

1. **签名步骤**:
   - 将所有非空参数按字典序排列
   - 按 `key=value&` 格式拼接参数
   - 末尾添加 `key=签名密钥`
   - 对拼接字符串进行MD5加密
   - 转为大写作为签名

2. **参与签名的字段**:
   - partnerId（必须）
   - data（必须）
   - timestamp（必须）
   - nonce（如果有值）
   - version（如果有值）
   - vin（如果有值）
   - status（如果有值）

3. **nonce生成规则**:
   - 每次请求必须生成不同的nonce
   - 建议使用8-32位随机字符串
   - 可以使用UUID、时间戳+随机数等方式生成
   - 示例：`UUID.randomUUID().toString().replace("-", "")`

4. **签名示例**:
   ```
   原始参数：
   partnerId=daihou001
   data=U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K14=
   timestamp=1690123456789
   nonce=a1b2c3d4e5f6g7h8

   拼接字符串（按字典序）：
   data=U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K14=&nonce=a1b2c3d4e5f6g7h8&partnerId=daihou001&timestamp=1690123456789&key=您的签名密钥

   MD5加密并转大写：
   A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6
   ```

### 签名生成代码示例

**Java实现**:
```java
import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;

public static String generateSign(Map<String, Object> params, String signKey) {
    try {
        // 使用TreeMap自动按key排序
        TreeMap<String, String> sortedParams = new TreeMap<>();

        // 添加所有非空参数
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (entry.getValue() != null && !entry.getValue().toString().isEmpty()) {
                sortedParams.put(entry.getKey(), entry.getValue().toString());
            }
        }

        // 拼接参数
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }

        // 添加签名密钥
        sb.append("key=").append(signKey);

        // MD5加密并转大写
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] digest = md.digest(sb.toString().getBytes("UTF-8"));

        StringBuilder result = new StringBuilder();
        for (byte b : digest) {
            result.append(String.format("%02x", b));
        }

        return result.toString().toUpperCase();

    } catch (Exception e) {
        throw new RuntimeException("生成签名失败", e);
    }
}
```

**Python实现**:
```python
import hashlib
from collections import OrderedDict

def generate_sign(params, sign_key):
    # 过滤空值并排序
    sorted_params = OrderedDict()
    for key in sorted(params.keys()):
        if params[key] is not None and str(params[key]) != '':
            sorted_params[key] = str(params[key])

    # 拼接参数
    param_str = '&'.join([f"{k}={v}" for k, v in sorted_params.items()])
    param_str += f"&key={sign_key}"

    # MD5加密并转大写
    md5_hash = hashlib.md5(param_str.encode('utf-8')).hexdigest()
    return md5_hash.upper()
```

## 完整调用示例

### 示例1：获取Token的方法

**方法1：通过浏览器开发者工具**
1. 打开浏览器，访问系统登录页面
2. 按F12打开开发者工具，切换到Network标签
3. 输入用户名密码登录
4. 在Network中找到登录请求的响应，复制token值

**方法2：通过程序模拟登录**
```bash
curl -X POST https://your-domain.com/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "您的用户名",
    "password": "您的密码",
    "code": "",
    "uuid": ""
  }'
```

### 示例2：调用数据接口
```bash
curl -X POST https://your-domain.com/api/third-party/receive-data \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzUxMiJ9..." \
  -d '{
    "partnerId": "您的合作方ID",
    "data": "AES加密后的数据",
    "timestamp": 1690123456789,
    "sign": "生成的签名"
  }'
```

### 示例3：Java代码示例

```java
// 1. 登录获取token（可选，也可以通过网页获取）
public String getTokenByApi() {
    String loginUrl = "https://dhtest.ichedai.cn/prod-api/login";
    Map<String, String> loginData = new HashMap<>();
    loginData.put("username", "您的用户名");
    loginData.put("password", "您的密码");
    loginData.put("code", "");
    loginData.put("uuid", "");

    // 发送POST请求获取token
    String response = httpPost(loginUrl, loginData);
    // 解析响应获取token
    return parseToken(response);
}

// 2. 调用数据接口
public String sendData(String businessData, String token) {
    String dataUrl = "https://dhtest.ichedai.cn/prod-api/api/third-party/receive-data";

    // 加密业务数据
    String encryptedData = AESUtil.encrypt(businessData);

    // 生成nonce
    String nonce = UUID.randomUUID().toString().replace("-", "");

    // 构建请求参数
    Map<String, Object> params = new HashMap<>();
    params.put("partnerId", "您的合作方ID");
    params.put("data", encryptedData);
    params.put("timestamp", System.currentTimeMillis());
    params.put("nonce", nonce);  

    // 生成签名（注意：nonce参与签名计算）
    String sign = generateSign(params);
    params.put("sign", sign);

    // 设置请求头
    Map<String, String> headers = new HashMap<>();
    headers.put("Authorization", "Bearer " + token);
    headers.put("Content-Type", "application/json");

    // 发送请求
    return httpPost(dataUrl, params, headers);
}
```

### 示例4：Java签名生成代码
```bash
public class SignatureHelper {

    /**
     * 生成签名示例
     * 第三方系统可以参考此方法实现签名生成
     */
    public static void main(String[] args) {
        // ========== 请修改以下参数 ==========
        String partnerId = "daihou001";  // 您的合作方标识
        String data = "LaJ3fXRanrqTFXx66+M1u4xlWiOIi8qIeT2dGovp4LA=";  // AES加密后的数据
        long timestamp = System.currentTimeMillis();  // 当前时间戳
        String nonce = "test_nonce_123";  // 随机数
        String version = "1.0";  // 版本号
        String vin = "1HGBH41JXMN109186";  // 车架号（可选）
        int status = 1;  // 车辆状态（可选）
        String signKey = ">HLiZM=<BDY(=D?bW4Q#.n_:i4Uy;ba";  // 您的签名密钥
        // =====================================
        
        // 生成签名
        String signature = generateSignature(partnerId, data, timestamp, nonce, version, vin, status, signKey);
        
        System.out.println("=== 签名生成示例 ===");
        System.out.println("partnerId: " + partnerId);
        System.out.println("data: " + data);
        System.out.println("timestamp: " + timestamp);
        System.out.println("nonce: " + nonce);
        System.out.println("version: " + version);
        System.out.println("vin: " + vin);
        System.out.println("status: " + status);
        System.out.println("signKey: " + signKey);
        System.out.println();
        System.out.println("生成的签名: " + signature);
        System.out.println();
        
        // 输出完整的请求JSON
        System.out.println("=== 完整请求JSON ===");
        System.out.println("{");
        System.out.println("  \"partnerId\": \"" + partnerId + "\",");
        System.out.println("  \"data\": \"" + data + "\",");
        System.out.println("  \"timestamp\": " + timestamp + ",");
        System.out.println("  \"sign\": \"" + signature + "\",");
        System.out.println("  \"nonce\": \"" + nonce + "\",");
        System.out.println("  \"version\": \"" + version + "\",");
        System.out.println("  \"vin\": \"" + vin + "\",");
        System.out.println("  \"status\": " + status);
        System.out.println("}");
    }
    
    /**
     * 生成签名
     * @param partnerId 合作方ID
     * @param data 加密数据
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param version 版本号
     * @param vin 车架号
     * @param status 车辆状态
     * @param signKey 签名密钥
     * @return 签名字符串
     */
    public static String generateSignature(String partnerId, String data, long timestamp, 
                                         String nonce, String version, String vin, 
                                         Integer status, String signKey) {
        try {
            // 构建签名参数（按字典序排列）
            TreeMap<String, String> params = new TreeMap<>();
            
            // 必填参数
            params.put("partnerId", partnerId);
            params.put("data", data);
            params.put("timestamp", String.valueOf(timestamp));
            
            // 可选参数（只有非空时才加入）
            if (nonce != null && !nonce.trim().isEmpty()) {
                params.put("nonce", nonce);
            }
            if (version != null && !version.trim().isEmpty()) {
                params.put("version", version);
            }
            if (vin != null && !vin.trim().isEmpty()) {
                params.put("vin", vin);
            }
            if (status != null) {
                params.put("status", String.valueOf(status));
            }
            
            // 按字典序拼接参数
            StringBuilder sb = new StringBuilder();
            for (String key : params.keySet()) {
                String value = params.get(key);
                if (value != null && !value.isEmpty()) {
                    sb.append(key).append("=").append(value).append("&");
                }
            }
            
            // 添加签名密钥
            sb.append("key=").append(signKey);
            
            String signStr = sb.toString();
            System.out.println("签名字符串: " + signStr);
            
            // MD5加密并转大写
            return md5(signStr).toUpperCase();
            
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * MD5加密
     */
    private static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
```



## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 200 | 成功 | - |
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 401 | 认证失败 | 检查用户名密码或token是否有效 |
| 403 | 权限不足 | 联系管理员检查账号权限或IP白名单 |
| 500 | 系统内部错误 | 联系技术支持 |

## 常见问题

### Q1: 如何处理token过期？
A: 当接口返回401错误时，说明token已过期，需要重新通过网页登录或登录接口获取新的token。

### Q2: 签名验证失败怎么办？
A: 请检查：
- 参数是否按字典序排列
- 是否包含了所有非空参数
- 签名密钥是否正确
- MD5加密后是否转为大写

### Q3: AES加密失败怎么办？
A: 请检查：
- 密钥和IV是否正确
- 加密模式是否为AES-128-CBC
- 填充方式是否为PKCS7Padding
- 加密后是否进行了Base64编码

### Q4: 时间戳验证失败？
A: 请确保：
- 使用当前时间的毫秒时间戳
- 服务器时间与我方服务器时间差不超过5分钟

