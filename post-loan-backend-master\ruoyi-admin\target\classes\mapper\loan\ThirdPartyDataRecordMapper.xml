<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.loan.mapper.ThirdPartyDataRecordMapper">
    
    <resultMap type="com.loan.domain.ThirdPartyDataRecord" id="ThirdPartyDataRecordResult">
        <result property="id"                column="id"                />
        <result property="partnerId"         column="partner_id"        />
        <result property="requestId"         column="request_id"        />
        <result property="originalData"      column="original_data"     />
        <result property="decryptedData"     column="decrypted_data"    />
        <result property="vin"               column="vin"               />
        <result property="status"            column="status"            />
        <result property="requestTimestamp"  column="request_timestamp" />
        <result property="nonce"             column="nonce"             />
        <result property="version"           column="version"           />
        <result property="processStatus"     column="process_status"    />
        <result property="processResult"     column="process_result"    />
        <result property="errorMessage"      column="error_message"     />
        <result property="clientIp"          column="client_ip"         />
        <result property="processTime"       column="process_time"      />
        <result property="businessData"      column="business_data"     />
        <result property="dataType"          column="data_type"         />
        <result property="createBy"          column="create_by"         />
        <result property="createTime"        column="create_time"       />
        <result property="updateBy"          column="update_by"         />
        <result property="updateTime"        column="update_time"       />
        <result property="remark"            column="remark"            />
    </resultMap>

    <sql id="selectThirdPartyDataRecordVo">
        select id, partner_id, request_id, original_data, decrypted_data, vin, status, 
               request_timestamp, nonce, version, process_status, process_result, 
               error_message, client_ip, process_time, business_data, data_type,
               create_by, create_time, update_by, update_time, remark 
        from third_party_data_record
    </sql>

    <select id="selectThirdPartyDataRecordList" parameterType="com.loan.domain.ThirdPartyDataRecord" resultMap="ThirdPartyDataRecordResult">
        <include refid="selectThirdPartyDataRecordVo"/>
        <where>  
            <if test="partnerId != null  and partnerId != ''"> and partner_id = #{partnerId}</if>
            <if test="requestId != null  and requestId != ''"> and request_id = #{requestId}</if>
            <if test="vin != null  and vin != ''"> and vin = #{vin}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="processStatus != null "> and process_status = #{processStatus}</if>
            <if test="clientIp != null  and clientIp != ''"> and client_ip = #{clientIp}</if>
            <if test="dataType != null  and dataType != ''"> and data_type = #{dataType}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectThirdPartyDataRecordById" parameterType="Long" resultMap="ThirdPartyDataRecordResult">
        <include refid="selectThirdPartyDataRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectByRequestId" parameterType="String" resultMap="ThirdPartyDataRecordResult">
        <include refid="selectThirdPartyDataRecordVo"/>
        where request_id = #{requestId}
    </select>

    <select id="selectByPartnerAndTimeRange" resultMap="ThirdPartyDataRecordResult">
        <include refid="selectThirdPartyDataRecordVo"/>
        where partner_id = #{partnerId}
        <if test="startTime != null and startTime != ''">
            and create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and create_time &lt;= #{endTime}
        </if>
        order by create_time desc
    </select>

    <select id="countByPartnerId" parameterType="String" resultType="int">
        select count(*) from third_party_data_record where partner_id = #{partnerId}
    </select>
        
    <insert id="insertThirdPartyDataRecord" parameterType="com.loan.domain.ThirdPartyDataRecord" useGeneratedKeys="true" keyProperty="id">
        insert into third_party_data_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="partnerId != null and partnerId != ''">partner_id,</if>
            <if test="requestId != null and requestId != ''">request_id,</if>
            <if test="originalData != null">original_data,</if>
            <if test="decryptedData != null">decrypted_data,</if>
            <if test="vin != null">vin,</if>
            <if test="status != null">status,</if>
            <if test="requestTimestamp != null">request_timestamp,</if>
            <if test="nonce != null">nonce,</if>
            <if test="version != null">version,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="processResult != null">process_result,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="clientIp != null">client_ip,</if>
            <if test="processTime != null">process_time,</if>
            <if test="businessData != null">business_data,</if>
            <if test="dataType != null">data_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="partnerId != null and partnerId != ''">#{partnerId},</if>
            <if test="requestId != null and requestId != ''">#{requestId},</if>
            <if test="originalData != null">#{originalData},</if>
            <if test="decryptedData != null">#{decryptedData},</if>
            <if test="vin != null">#{vin},</if>
            <if test="status != null">#{status},</if>
            <if test="requestTimestamp != null">#{requestTimestamp},</if>
            <if test="nonce != null">#{nonce},</if>
            <if test="version != null">#{version},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="processResult != null">#{processResult},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="clientIp != null">#{clientIp},</if>
            <if test="processTime != null">#{processTime},</if>
            <if test="businessData != null">#{businessData},</if>
            <if test="dataType != null">#{dataType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateThirdPartyDataRecord" parameterType="com.loan.domain.ThirdPartyDataRecord">
        update third_party_data_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="partnerId != null and partnerId != ''">partner_id = #{partnerId},</if>
            <if test="requestId != null and requestId != ''">request_id = #{requestId},</if>
            <if test="originalData != null">original_data = #{originalData},</if>
            <if test="decryptedData != null">decrypted_data = #{decryptedData},</if>
            <if test="vin != null">vin = #{vin},</if>
            <if test="status != null">status = #{status},</if>
            <if test="requestTimestamp != null">request_timestamp = #{requestTimestamp},</if>
            <if test="nonce != null">nonce = #{nonce},</if>
            <if test="version != null">version = #{version},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="processResult != null">process_result = #{processResult},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="clientIp != null">client_ip = #{clientIp},</if>
            <if test="processTime != null">process_time = #{processTime},</if>
            <if test="businessData != null">business_data = #{businessData},</if>
            <if test="dataType != null">data_type = #{dataType},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteThirdPartyDataRecordById" parameterType="Long">
        delete from third_party_data_record where id = #{id}
    </delete>

    <delete id="deleteThirdPartyDataRecordByIds" parameterType="String">
        delete from third_party_data_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
