package com.ruoyi.reimburse.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.reimburse.mapper.ReimburseMapper;
import com.ruoyi.reimburse.domain.Reimburse;
import com.ruoyi.reimburse.service.IReimburseService;

/**
 * 报销记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
@Service
public class ReimburseServiceImpl implements IReimburseService 
{
    @Autowired
    private ReimburseMapper reimburseMapper;

    /**
     * 查询报销记录
     * 
     * @param id 报销记录主键
     * @return 报销记录
     */
    @Override
    public Reimburse selectReimburseById(String id)
    {
        return reimburseMapper.selectReimburseById(id);
    }

    /**
     * 查询报销记录列表
     * 
     * @param reimburse 报销记录
     * @return 报销记录
     */
    @Override
    public List<Reimburse> selectReimburseList(Reimburse reimburse)
    {
        return reimburseMapper.selectReimburseList(reimburse);
    }

    /**
     * 新增报销记录
     * 
     * @param reimburse 报销记录
     * @return 结果
     */
    @Override
    public int insertReimburse(Reimburse reimburse)
    {
        return reimburseMapper.insertReimburse(reimburse);
    }

    /**
     * 修改报销记录
     * 
     * @param reimburse 报销记录
     * @return 结果
     */
    @Override
    public int updateReimburse(Reimburse reimburse)
    {
        return reimburseMapper.updateReimburse(reimburse);
    }

    /**
     * 批量删除报销记录
     * 
     * @param ids 需要删除的报销记录主键
     * @return 结果
     */
    @Override
    public int deleteReimburseByIds(String[] ids)
    {
        return reimburseMapper.deleteReimburseByIds(ids);
    }

    /**
     * 删除报销记录信息
     * 
     * @param id 报销记录主键
     * @return 结果
     */
    @Override
    public int deleteReimburseById(String id)
    {
        return reimburseMapper.deleteReimburseById(id);
    }
}
