<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vw_loan_office.mapper.VwLoanOfficeMapper">

    <resultMap type="VwLoanOffice" id="VwLoanOfficeResult">
        <result property="id"    column="id"    />
        <result property="urgeName"    column="urge_name"    />
        <result property="applyId"    column="apply_id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="partnerId"    column="partner_id"    />
        <result property="orgId"    column="org_id"    />
        <result property="dhUser"    column="dh_user"    />
        <result property="dhTime"    column="dh_time"    />
        <result property="urgeTime"    column="urge_time"    />
        <result property="petitionTime"    column="petition_time"    />
        <result property="lawTime"    column="law_time"    />
        <result property="followUp"    column="follow_up"    />
        <result property="urgeUser"    column="urge_user"    />
        <result property="petitionUser"    column="petition_user"    />
        <result property="lawUser"    column="law_user"    />
        <result property="period"    column="period"    />
        <result property="realReturnMoney"    column="real_return_money"    />
        <result property="reminderDate"    column="reminder_date"    />
        <result property="isPetition"    column="is_petition"    />
        <result property="badDebt"    column="bad_debt"    />
        <result property="status"    column="status"    />
        <result property="billStatus"    column="bill_status"    />
        <result property="slippageStatus"    column="slippage_status"    />
        <result property="followStatus"    column="follow_status"    />
        <result property="isExtension"    column="is_extension"    />
        <result property="extensionDate"    column="extension_date"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="name"    column="name"    />
        <result property="primaryPerson"    column="primary_person"    />
        <result property="deputyPerson"    column="deputy_person"    />
        <result property="phone"    column="phone"    />
        <result property="areaName"    column="area_name"    />
    </resultMap>

    <sql id="selectVwLoanOfficeVo">
        select id, urge_name, apply_id, customer_id, customer_name, partner_id, org_id, dh_user, dh_time, urge_time, petition_time, law_time, follow_up, urge_user, petition_user, law_user, period, real_return_money, reminder_date, is_petition, bad_debt, status, bill_status, slippage_status, follow_status, is_extension, extension_date, del_flag, create_by, create_time, update_by, update_time, name, primary_person, deputy_person, phone, area_name from vw_loan_office
    </sql>

    <select id="selectVwLoanOfficeList" parameterType="VwLoanOffice" resultMap="VwLoanOfficeResult">
        <include refid="selectVwLoanOfficeVo"/>
        <where>
            <if test="urgeName != null  and urgeName != ''"> and urge_name like concat('%', #{urgeName}, '%')</if>
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="customerId != null  and customerId != ''"> and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="partnerId != null  and partnerId != ''"> and partner_id = #{partnerId}</if>
            <if test="orgId != null  and orgId != ''"> and org_id = #{orgId}</if>
            <if test="dhUser != null  and dhUser != ''"> and dh_user = #{dhUser}</if>
            <if test="dhTime != null "> and dh_time = #{dhTime}</if>
            <if test="urgeTime != null "> and urge_time = #{urgeTime}</if>
            <if test="petitionTime != null "> and petition_time = #{petitionTime}</if>
            <if test="lawTime != null "> and law_time = #{lawTime}</if>
            <if test="followUp != null  and followUp != ''"> and follow_up = #{followUp}</if>
            <if test="urgeUser != null  and urgeUser != ''"> and urge_user = #{urgeUser}</if>
            <if test="petitionUser != null  and petitionUser != ''"> and petition_user = #{petitionUser}</if>
            <if test="lawUser != null  and lawUser != ''"> and law_user = #{lawUser}</if>
            <if test="period != null  and period != ''"> and period = #{period}</if>
            <if test="realReturnMoney != null "> and real_return_money = #{realReturnMoney}</if>
            <if test="reminderDate != null "> and reminder_date = #{reminderDate}</if>
            <if test="isPetition != null  and isPetition != ''"> and is_petition = #{isPetition}</if>
            <if test="badDebt != null  and badDebt != ''"> and bad_debt = #{badDebt}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="billStatus != null  and billStatus != ''"> and bill_status = #{billStatus}</if>
            <if test="slippageStatus != null  and slippageStatus != ''"> and slippage_status = #{slippageStatus}</if>
            <if test="followStatus != null  and followStatus != ''"> and follow_status = #{followStatus}</if>
            <if test="isExtension != null  and isExtension != ''"> and is_extension = #{isExtension}</if>
            <if test="extensionDate != null "> and extension_date = #{extensionDate}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="primaryPerson != null  and primaryPerson != ''"> and primary_person = #{primaryPerson}</if>
            <if test="deputyPerson != null  and deputyPerson != ''"> and deputy_person = #{deputyPerson}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
        </where>
    </select>

    <select id="selectVwLoanOfficeById" parameterType="String" resultMap="VwLoanOfficeResult">
        <include refid="selectVwLoanOfficeVo"/>
        where id = #{id}
    </select>

    <insert id="insertVwLoanOffice" parameterType="VwLoanOffice">
        insert into vw_loan_office
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="urgeName != null">urge_name,</if>
            <if test="applyId != null">apply_id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="partnerId != null">partner_id,</if>
            <if test="orgId != null">org_id,</if>
            <if test="dhUser != null">dh_user,</if>
            <if test="dhTime != null">dh_time,</if>
            <if test="urgeTime != null">urge_time,</if>
            <if test="petitionTime != null">petition_time,</if>
            <if test="lawTime != null">law_time,</if>
            <if test="followUp != null">follow_up,</if>
            <if test="urgeUser != null">urge_user,</if>
            <if test="petitionUser != null">petition_user,</if>
            <if test="lawUser != null">law_user,</if>
            <if test="period != null">period,</if>
            <if test="realReturnMoney != null">real_return_money,</if>
            <if test="reminderDate != null">reminder_date,</if>
            <if test="isPetition != null">is_petition,</if>
            <if test="badDebt != null">bad_debt,</if>
            <if test="status != null">status,</if>
            <if test="billStatus != null">bill_status,</if>
            <if test="slippageStatus != null">slippage_status,</if>
            <if test="followStatus != null">follow_status,</if>
            <if test="isExtension != null">is_extension,</if>
            <if test="extensionDate != null">extension_date,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="name != null">name,</if>
            <if test="primaryPerson != null">primary_person,</if>
            <if test="deputyPerson != null">deputy_person,</if>
            <if test="phone != null">phone,</if>
            <if test="areaName != null">area_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="urgeName != null">#{urgeName},</if>
            <if test="applyId != null">#{applyId},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="partnerId != null">#{partnerId},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="dhUser != null">#{dhUser},</if>
            <if test="dhTime != null">#{dhTime},</if>
            <if test="urgeTime != null">#{urgeTime},</if>
            <if test="petitionTime != null">#{petitionTime},</if>
            <if test="lawTime != null">#{lawTime},</if>
            <if test="followUp != null">#{followUp},</if>
            <if test="urgeUser != null">#{urgeUser},</if>
            <if test="petitionUser != null">#{petitionUser},</if>
            <if test="lawUser != null">#{lawUser},</if>
            <if test="period != null">#{period},</if>
            <if test="realReturnMoney != null">#{realReturnMoney},</if>
            <if test="reminderDate != null">#{reminderDate},</if>
            <if test="isPetition != null">#{isPetition},</if>
            <if test="badDebt != null">#{badDebt},</if>
            <if test="status != null">#{status},</if>
            <if test="billStatus != null">#{billStatus},</if>
            <if test="slippageStatus != null">#{slippageStatus},</if>
            <if test="followStatus != null">#{followStatus},</if>
            <if test="isExtension != null">#{isExtension},</if>
            <if test="extensionDate != null">#{extensionDate},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="name != null">#{name},</if>
            <if test="primaryPerson != null">#{primaryPerson},</if>
            <if test="deputyPerson != null">#{deputyPerson},</if>
            <if test="phone != null">#{phone},</if>
            <if test="areaName != null">#{areaName},</if>
        </trim>
    </insert>

    <update id="updateVwLoanOffice" parameterType="VwLoanOffice">
        update vw_loan_office
        <trim prefix="SET" suffixOverrides=",">
            <if test="urgeName != null">urge_name = #{urgeName},</if>
            <if test="applyId != null">apply_id = #{applyId},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="partnerId != null">partner_id = #{partnerId},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="dhUser != null">dh_user = #{dhUser},</if>
            <if test="dhTime != null">dh_time = #{dhTime},</if>
            <if test="urgeTime != null">urge_time = #{urgeTime},</if>
            <if test="petitionTime != null">petition_time = #{petitionTime},</if>
            <if test="lawTime != null">law_time = #{lawTime},</if>
            <if test="followUp != null">follow_up = #{followUp},</if>
            <if test="urgeUser != null">urge_user = #{urgeUser},</if>
            <if test="petitionUser != null">petition_user = #{petitionUser},</if>
            <if test="lawUser != null">law_user = #{lawUser},</if>
            <if test="period != null">period = #{period},</if>
            <if test="realReturnMoney != null">real_return_money = #{realReturnMoney},</if>
            <if test="reminderDate != null">reminder_date = #{reminderDate},</if>
            <if test="isPetition != null">is_petition = #{isPetition},</if>
            <if test="badDebt != null">bad_debt = #{badDebt},</if>
            <if test="status != null">status = #{status},</if>
            <if test="billStatus != null">bill_status = #{billStatus},</if>
            <if test="slippageStatus != null">slippage_status = #{slippageStatus},</if>
            <if test="followStatus != null">follow_status = #{followStatus},</if>
            <if test="isExtension != null">is_extension = #{isExtension},</if>
            <if test="extensionDate != null">extension_date = #{extensionDate},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="name != null">name = #{name},</if>
            <if test="primaryPerson != null">primary_person = #{primaryPerson},</if>
            <if test="deputyPerson != null">deputy_person = #{deputyPerson},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVwLoanOfficeById" parameterType="String">
        delete from vw_loan_office where id = #{id}
    </delete>

    <delete id="deleteVwLoanOfficeByIds" parameterType="String">
        delete from vw_loan_office where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>