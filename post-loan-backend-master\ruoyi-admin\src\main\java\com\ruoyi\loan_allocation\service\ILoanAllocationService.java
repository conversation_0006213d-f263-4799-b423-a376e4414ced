package com.ruoyi.loan_allocation.service;

import java.util.List;
import com.ruoyi.loan_allocation.domain.LoanAllocation;

/**
 * 流程跟催员分配Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-21
 */
public interface ILoanAllocationService 
{
    /**
     * 查询流程跟催员分配
     * 
     * @param id 流程跟催员分配主键
     * @return 流程跟催员分配
     */
    public LoanAllocation selectLoanAllocationById(String id);

    /**
     * 查询流程跟催员分配列表
     * 
     * @param loanAllocation 流程跟催员分配
     * @return 流程跟催员分配集合
     */
    public List<LoanAllocation> selectLoanAllocationList(LoanAllocation loanAllocation);

    /**
     * 新增流程跟催员分配
     * 
     * @param loanAllocation 流程跟催员分配
     * @return 结果
     */
    public int insertLoanAllocation(LoanAllocation loanAllocation);

    /**
     * 修改流程跟催员分配
     * 
     * @param loanAllocation 流程跟催员分配
     * @return 结果
     */
    public int updateLoanAllocation(LoanAllocation loanAllocation);

    /**
     * 批量删除流程跟催员分配
     * 
     * @param ids 需要删除的流程跟催员分配主键集合
     * @return 结果
     */
    public int deleteLoanAllocationByIds(String[] ids);

    /**
     * 删除流程跟催员分配信息
     * 
     * @param id 流程跟催员分配主键
     * @return 结果
     */
    public int deleteLoanAllocationById(String id);
}
