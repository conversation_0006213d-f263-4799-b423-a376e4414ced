package com.ruoyi.customer_info.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 客户信息对象 customer_info
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
public class CustomerInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 客户编号 */
    @Excel(name = "客户编号")
    private String id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String customerName;

    /** 性别 */
    @Excel(name = "性别")
    private String sex;

    /** 年龄 */
    @Excel(name = "年龄")
    private String age;

    /** 出生日期 */
    @Excel(name = "出生日期")
    private String birthday;

    /** 民族 */
    @Excel(name = "民族")
    private String nation;

    /** 证件号码 */
    private String certId;

    /** 证件类型 */
    @Excel(name = "证件类型")
    private String certType;

    /** 签发机关 */
    @Excel(name = "签发机关")
    private String creditissueOrg;

    /** 身份证-地址 */
    @Excel(name = "身份证-地址")
    private String address;

    /** 有效开始日期 */
    @Excel(name = "有效开始日期")
    private String effecteddate;

    /** 有效结束日期 */
    @Excel(name = "有效结束日期")
    private String expireddate;

    /** 客户类型 */
    @Excel(name = "客户类型")
    private String customerType;

    /** 个人类型（字典：personalType） */
    @Excel(name = "个人类型", readConverterExp = "字=典：personalType")
    private String personalType;

    /** 客户状态 */
    @Excel(name = "客户状态")
    private String customerStatus;

    /** 有效状态 */
    @Excel(name = "有效状态")
    private String validStatus;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date createDate;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date updateDate;

    /** 删除标记 */
    private String delFlag;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setCustomerName(String customerName) 
    {
        this.customerName = customerName;
    }

    public String getCustomerName() 
    {
        return customerName;
    }

    public void setSex(String sex) 
    {
        this.sex = sex;
    }

    public String getSex() 
    {
        return sex;
    }

    public void setAge(String age) 
    {
        this.age = age;
    }

    public String getAge() 
    {
        return age;
    }

    public void setBirthday(String birthday) 
    {
        this.birthday = birthday;
    }

    public String getBirthday() 
    {
        return birthday;
    }

    public void setNation(String nation) 
    {
        this.nation = nation;
    }

    public String getNation() 
    {
        return nation;
    }

    public void setCertId(String certId) 
    {
        this.certId = certId;
    }

    public String getCertId() 
    {
        return certId;
    }

    public void setCertType(String certType) 
    {
        this.certType = certType;
    }

    public String getCertType() 
    {
        return certType;
    }

    public void setCreditissueOrg(String creditissueOrg) 
    {
        this.creditissueOrg = creditissueOrg;
    }

    public String getCreditissueOrg() 
    {
        return creditissueOrg;
    }

    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }

    public void setEffecteddate(String effecteddate) 
    {
        this.effecteddate = effecteddate;
    }

    public String getEffecteddate() 
    {
        return effecteddate;
    }

    public void setExpireddate(String expireddate) 
    {
        this.expireddate = expireddate;
    }

    public String getExpireddate() 
    {
        return expireddate;
    }

    public void setCustomerType(String customerType) 
    {
        this.customerType = customerType;
    }

    public String getCustomerType() 
    {
        return customerType;
    }

    public void setPersonalType(String personalType) 
    {
        this.personalType = personalType;
    }

    public String getPersonalType() 
    {
        return personalType;
    }

    public void setCustomerStatus(String customerStatus) 
    {
        this.customerStatus = customerStatus;
    }

    public String getCustomerStatus() 
    {
        return customerStatus;
    }

    public void setValidStatus(String validStatus) 
    {
        this.validStatus = validStatus;
    }

    public String getValidStatus() 
    {
        return validStatus;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("customerName", getCustomerName())
            .append("sex", getSex())
            .append("age", getAge())
            .append("birthday", getBirthday())
            .append("nation", getNation())
            .append("certId", getCertId())
            .append("certType", getCertType())
            .append("creditissueOrg", getCreditissueOrg())
            .append("address", getAddress())
            .append("effecteddate", getEffecteddate())
            .append("expireddate", getExpireddate())
            .append("customerType", getCustomerType())
            .append("personalType", getPersonalType())
            .append("customerStatus", getCustomerStatus())
            .append("validStatus", getValidStatus())
            .append("createBy", getCreateBy())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
