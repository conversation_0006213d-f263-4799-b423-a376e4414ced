package com.ruoyi.ind_info.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 个人客户信息对象 ind_info
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public class IndInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 客户编号 */
    @Excel(name = "客户编号")
    private String customerId;

    /** 曾用名 */
    @Excel(name = "曾用名")
    private String formerName;

    /** 学历 */
    @Excel(name = "学历")
    private String highestEducation;

    /** 学位 */
    @Excel(name = "学位")
    private String degree;

    /** 婚姻状况 */
    @Excel(name = "婚姻状况")
    private String marriageStatus;

    /** 收入类型 */
    @Excel(name = "收入类型")
    private String incomeType;

    /** 月收入 */
    @Excel(name = "月收入")
    private BigDecimal monthlyIncome;

    /** 收入情况简述 */
    @Excel(name = "收入情况简述")
    private String incomeDescribe;

    /** 居住状况 */
    @Excel(name = "居住状况")
    private String liveStatus;

    /** 本地居住年限 */
    @Excel(name = "本地居住年限")
    private String localResidentAge;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String mobilePhone;

    /** 是否有子女 */
    @Excel(name = "是否有子女")
    private String haveChildren;

    /** 是否本地户口 */
    @Excel(name = "是否本地户口")
    private String localResidentStatus;

    /** 是否有房产 */
    @Excel(name = "是否有房产")
    private String haveHouse;

    /** 配偶姓名 */
    @Excel(name = "配偶姓名")
    private String spouseName;

    /** 配偶身份证号码 */
    @Excel(name = "配偶身份证号码")
    private String spouseCardId;

    /** 配偶手机号码 */
    @Excel(name = "配偶手机号码")
    private String spousePhone;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date createDate;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date updateDate;

    /** 居住地址-省 */
    @Excel(name = "居住地址-省")
    private String province;

    /** 居住地址-市 */
    @Excel(name = "居住地址-市")
    private String city;

    /** 居住地址-区 */
    @Excel(name = "居住地址-区")
    private String borough;

    /** 居住地址 */
    @Excel(name = "居住地址")
    private String liveAddress;

    /** 居住详细地址 */
    @Excel(name = "居住详细地址")
    private String detailAddress;

    /** 居住邮编 */
    @Excel(name = "居住邮编")
    private String zipCode;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 文书送达地址-省 */
    @Excel(name = "文书送达地址-省")
    private String docProvince;

    /** 文书送达地址-市 */
    @Excel(name = "文书送达地址-市")
    private String docCity;

    /** 文书送达地址-区 */
    @Excel(name = "文书送达地址-区")
    private String docBorough;

    /** 文书送达地址 */
    @Excel(name = "文书送达地址")
    private String docAddress;

    /** 文书送达详细地址 */
    @Excel(name = "文书送达详细地址")
    private String docDetailAddress;

    /** 送达地址属性 */
    @Excel(name = "送达地址属性")
    private String docAddressType;

    /** 删除标记 */
    private String delFlag;

    /** 身份证-省份 */
    @Excel(name = "身份证-省份")
    private String censusProvince;

    /** 身份证-城市 */
    @Excel(name = "身份证-城市")
    private String censusCity;

    /** 身份证-区县 */
    @Excel(name = "身份证-区县")
    private String censusArea;

    /** 身份证-地址 */
    @Excel(name = "身份证-地址")
    private String censusAddress;

    /** 抵押联系人(苏银) */
    @Excel(name = "抵押联系人(苏银)")
    private String mortgageContact;

    /** 抵押联系人手机号(苏银) */
    @Excel(name = "抵押联系人手机号(苏银)")
    private String mortgageContactPhone;

    /** 个人年收入 */
    @Excel(name = "个人年收入")
    private BigDecimal personalIncome;

    /** 家庭年收入 */
    @Excel(name = "家庭年收入")
    private BigDecimal familyIncome;

    /** 电子邮箱(中关村) */
    @Excel(name = "电子邮箱(中关村)")
    private String email;

    /** 还款来源(蓝海) */
    @Excel(name = "还款来源(蓝海)")
    private String repaySource;

    /** 客户属性(华瑞) */
    @Excel(name = "客户属性(华瑞)")
    private String customerSource;

    /** 户籍类型(皖新)1-农业 2-⾮农业 */
    @Excel(name = "户籍类型(皖新)1-农业 2-⾮农业")
    private String localType;

    /** 抵押代理人ID(蓝海) */
    @Excel(name = "抵押代理人ID(蓝海)")
    private String mortgageAgentId;

    /** 配偶单位名称(皖新) */
    @Excel(name = "配偶单位名称(皖新)")
    private String spouseUnit;

    /** 居住地址是否居住满一年(蓝海) */
    @Excel(name = "居住地址是否居住满一年(蓝海)")
    private String localFlag;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setCustomerId(String customerId) 
    {
        this.customerId = customerId;
    }

    public String getCustomerId() 
    {
        return customerId;
    }

    public void setFormerName(String formerName) 
    {
        this.formerName = formerName;
    }

    public String getFormerName() 
    {
        return formerName;
    }

    public void setHighestEducation(String highestEducation) 
    {
        this.highestEducation = highestEducation;
    }

    public String getHighestEducation() 
    {
        return highestEducation;
    }

    public void setDegree(String degree) 
    {
        this.degree = degree;
    }

    public String getDegree() 
    {
        return degree;
    }

    public void setMarriageStatus(String marriageStatus) 
    {
        this.marriageStatus = marriageStatus;
    }

    public String getMarriageStatus() 
    {
        return marriageStatus;
    }

    public void setIncomeType(String incomeType) 
    {
        this.incomeType = incomeType;
    }

    public String getIncomeType() 
    {
        return incomeType;
    }

    public void setMonthlyIncome(BigDecimal monthlyIncome) 
    {
        this.monthlyIncome = monthlyIncome;
    }

    public BigDecimal getMonthlyIncome() 
    {
        return monthlyIncome;
    }

    public void setIncomeDescribe(String incomeDescribe) 
    {
        this.incomeDescribe = incomeDescribe;
    }

    public String getIncomeDescribe() 
    {
        return incomeDescribe;
    }

    public void setLiveStatus(String liveStatus) 
    {
        this.liveStatus = liveStatus;
    }

    public String getLiveStatus() 
    {
        return liveStatus;
    }

    public void setLocalResidentAge(String localResidentAge) 
    {
        this.localResidentAge = localResidentAge;
    }

    public String getLocalResidentAge() 
    {
        return localResidentAge;
    }

    public void setMobilePhone(String mobilePhone) 
    {
        this.mobilePhone = mobilePhone;
    }

    public String getMobilePhone() 
    {
        return mobilePhone;
    }

    public void setHaveChildren(String haveChildren) 
    {
        this.haveChildren = haveChildren;
    }

    public String getHaveChildren() 
    {
        return haveChildren;
    }

    public void setLocalResidentStatus(String localResidentStatus) 
    {
        this.localResidentStatus = localResidentStatus;
    }

    public String getLocalResidentStatus() 
    {
        return localResidentStatus;
    }

    public void setHaveHouse(String haveHouse) 
    {
        this.haveHouse = haveHouse;
    }

    public String getHaveHouse() 
    {
        return haveHouse;
    }

    public void setSpouseName(String spouseName) 
    {
        this.spouseName = spouseName;
    }

    public String getSpouseName() 
    {
        return spouseName;
    }

    public void setSpouseCardId(String spouseCardId) 
    {
        this.spouseCardId = spouseCardId;
    }

    public String getSpouseCardId() 
    {
        return spouseCardId;
    }

    public void setSpousePhone(String spousePhone) 
    {
        this.spousePhone = spousePhone;
    }

    public String getSpousePhone() 
    {
        return spousePhone;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    public void setProvince(String province) 
    {
        this.province = province;
    }

    public String getProvince() 
    {
        return province;
    }

    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }

    public void setBorough(String borough) 
    {
        this.borough = borough;
    }

    public String getBorough() 
    {
        return borough;
    }

    public void setLiveAddress(String liveAddress) 
    {
        this.liveAddress = liveAddress;
    }

    public String getLiveAddress() 
    {
        return liveAddress;
    }

    public void setDetailAddress(String detailAddress) 
    {
        this.detailAddress = detailAddress;
    }

    public String getDetailAddress() 
    {
        return detailAddress;
    }

    public void setZipCode(String zipCode) 
    {
        this.zipCode = zipCode;
    }

    public String getZipCode() 
    {
        return zipCode;
    }

    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }

    public void setDocProvince(String docProvince) 
    {
        this.docProvince = docProvince;
    }

    public String getDocProvince() 
    {
        return docProvince;
    }

    public void setDocCity(String docCity) 
    {
        this.docCity = docCity;
    }

    public String getDocCity() 
    {
        return docCity;
    }

    public void setDocBorough(String docBorough) 
    {
        this.docBorough = docBorough;
    }

    public String getDocBorough() 
    {
        return docBorough;
    }

    public void setDocAddress(String docAddress) 
    {
        this.docAddress = docAddress;
    }

    public String getDocAddress() 
    {
        return docAddress;
    }

    public void setDocDetailAddress(String docDetailAddress) 
    {
        this.docDetailAddress = docDetailAddress;
    }

    public String getDocDetailAddress() 
    {
        return docDetailAddress;
    }

    public void setDocAddressType(String docAddressType) 
    {
        this.docAddressType = docAddressType;
    }

    public String getDocAddressType() 
    {
        return docAddressType;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setCensusProvince(String censusProvince) 
    {
        this.censusProvince = censusProvince;
    }

    public String getCensusProvince() 
    {
        return censusProvince;
    }

    public void setCensusCity(String censusCity) 
    {
        this.censusCity = censusCity;
    }

    public String getCensusCity() 
    {
        return censusCity;
    }

    public void setCensusArea(String censusArea) 
    {
        this.censusArea = censusArea;
    }

    public String getCensusArea() 
    {
        return censusArea;
    }

    public void setCensusAddress(String censusAddress) 
    {
        this.censusAddress = censusAddress;
    }

    public String getCensusAddress() 
    {
        return censusAddress;
    }

    public void setMortgageContact(String mortgageContact) 
    {
        this.mortgageContact = mortgageContact;
    }

    public String getMortgageContact() 
    {
        return mortgageContact;
    }

    public void setMortgageContactPhone(String mortgageContactPhone) 
    {
        this.mortgageContactPhone = mortgageContactPhone;
    }

    public String getMortgageContactPhone() 
    {
        return mortgageContactPhone;
    }

    public void setPersonalIncome(BigDecimal personalIncome) 
    {
        this.personalIncome = personalIncome;
    }

    public BigDecimal getPersonalIncome() 
    {
        return personalIncome;
    }

    public void setFamilyIncome(BigDecimal familyIncome) 
    {
        this.familyIncome = familyIncome;
    }

    public BigDecimal getFamilyIncome() 
    {
        return familyIncome;
    }

    public void setEmail(String email) 
    {
        this.email = email;
    }

    public String getEmail() 
    {
        return email;
    }

    public void setRepaySource(String repaySource) 
    {
        this.repaySource = repaySource;
    }

    public String getRepaySource() 
    {
        return repaySource;
    }

    public void setCustomerSource(String customerSource) 
    {
        this.customerSource = customerSource;
    }

    public String getCustomerSource() 
    {
        return customerSource;
    }

    public void setLocalType(String localType) 
    {
        this.localType = localType;
    }

    public String getLocalType() 
    {
        return localType;
    }

    public void setMortgageAgentId(String mortgageAgentId) 
    {
        this.mortgageAgentId = mortgageAgentId;
    }

    public String getMortgageAgentId() 
    {
        return mortgageAgentId;
    }

    public void setSpouseUnit(String spouseUnit) 
    {
        this.spouseUnit = spouseUnit;
    }

    public String getSpouseUnit() 
    {
        return spouseUnit;
    }

    public void setLocalFlag(String localFlag) 
    {
        this.localFlag = localFlag;
    }

    public String getLocalFlag() 
    {
        return localFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("customerId", getCustomerId())
            .append("formerName", getFormerName())
            .append("highestEducation", getHighestEducation())
            .append("degree", getDegree())
            .append("marriageStatus", getMarriageStatus())
            .append("incomeType", getIncomeType())
            .append("monthlyIncome", getMonthlyIncome())
            .append("incomeDescribe", getIncomeDescribe())
            .append("liveStatus", getLiveStatus())
            .append("localResidentAge", getLocalResidentAge())
            .append("mobilePhone", getMobilePhone())
            .append("haveChildren", getHaveChildren())
            .append("localResidentStatus", getLocalResidentStatus())
            .append("haveHouse", getHaveHouse())
            .append("spouseName", getSpouseName())
            .append("spouseCardId", getSpouseCardId())
            .append("spousePhone", getSpousePhone())
            .append("createBy", getCreateBy())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .append("province", getProvince())
            .append("city", getCity())
            .append("borough", getBorough())
            .append("liveAddress", getLiveAddress())
            .append("detailAddress", getDetailAddress())
            .append("zipCode", getZipCode())
            .append("remarks", getRemarks())
            .append("docProvince", getDocProvince())
            .append("docCity", getDocCity())
            .append("docBorough", getDocBorough())
            .append("docAddress", getDocAddress())
            .append("docDetailAddress", getDocDetailAddress())
            .append("docAddressType", getDocAddressType())
            .append("delFlag", getDelFlag())
            .append("censusProvince", getCensusProvince())
            .append("censusCity", getCensusCity())
            .append("censusArea", getCensusArea())
            .append("censusAddress", getCensusAddress())
            .append("mortgageContact", getMortgageContact())
            .append("mortgageContactPhone", getMortgageContactPhone())
            .append("personalIncome", getPersonalIncome())
            .append("familyIncome", getFamilyIncome())
            .append("email", getEmail())
            .append("repaySource", getRepaySource())
            .append("customerSource", getCustomerSource())
            .append("localType", getLocalType())
            .append("mortgageAgentId", getMortgageAgentId())
            .append("spouseUnit", getSpouseUnit())
            .append("localFlag", getLocalFlag())
            .toString();
    }
}
