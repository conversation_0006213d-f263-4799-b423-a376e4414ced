package com.ruoyi.car_team.mapper;

import java.util.List;
import com.ruoyi.car_team.domain.GarageTeam;

/**
 * 团队管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface GarageTeamMapper 
{
    /**
     * 查询团队管理
     * 
     * @param id 团队管理主键
     * @return 团队管理
     */
    public GarageTeam selectGarageTeamById(Long id);

    /**
     * 查询团队管理列表
     * 
     * @param garageTeam 团队管理
     * @return 团队管理集合
     */
    public List<GarageTeam> selectGarageTeamList(GarageTeam garageTeam);

    /**
     * 新增团队管理
     * 
     * @param garageTeam 团队管理
     * @return 结果
     */
    public int insertGarageTeam(GarageTeam garageTeam);

    /**
     * 修改团队管理
     * 
     * @param garageTeam 团队管理
     * @return 结果
     */
    public int updateGarageTeam(GarageTeam garageTeam);

    /**
     * 删除团队管理
     * 
     * @param id 团队管理主键
     * @return 结果
     */
    public int deleteGarageTeamById(Long id);

    /**
     * 批量删除团队管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGarageTeamByIds(Long[] ids);
}
