package com.ruoyi.assign_table.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.assign_table.domain.AssignTable;
import com.ruoyi.assign_table.service.IAssignTableService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 指派Controller
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/assign_table/assign_table")
public class AssignTableController extends BaseController
{
    @Autowired
    private IAssignTableService assignTableService;

    /**
     * 查询指派列表
     */
    @PreAuthorize("@ss.hasPermi('assign_table:assign_table:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssignTable assignTable)
    {
        startPage();
        List<AssignTable> list = assignTableService.selectAssignTableList(assignTable);
        return getDataTable(list);
    }

    /**
     * 导出指派列表
     */
    @PreAuthorize("@ss.hasPermi('assign_table:assign_table:export')")
    @Log(title = "指派", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssignTable assignTable)
    {
        List<AssignTable> list = assignTableService.selectAssignTableList(assignTable);
        ExcelUtil<AssignTable> util = new ExcelUtil<AssignTable>(AssignTable.class);
        util.exportExcel(response, list, "指派数据");
    }

    /**
     * 获取指派详细信息
     */
    @PreAuthorize("@ss.hasPermi('assign_table:assign_table:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(assignTableService.selectAssignTableById(id));
    }

    /**
     * 新增指派
     */
    @PreAuthorize("@ss.hasPermi('assign_table:assign_table:add')")
    @Log(title = "指派", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AssignTable assignTable)
    {
        return toAjax(assignTableService.insertAssignTable(assignTable));
    }

    /**
     * 修改指派
     */
    @PreAuthorize("@ss.hasPermi('assign_table:assign_table:edit')")
    @Log(title = "指派", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AssignTable assignTable)
    {
        return toAjax(assignTableService.updateAssignTable(assignTable));
    }

    /**
     * 删除指派
     */
    @PreAuthorize("@ss.hasPermi('assign_table:assign_table:remove')")
    @Log(title = "指派", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(assignTableService.deleteAssignTableByIds(ids));
    }

    /**
     * 撤销指派
     */
    @PreAuthorize("@ss.hasPermi('assign_table:assign_table:revoke')")
    @Log(title = "指派", businessType = BusinessType.UPDATE)
    @PutMapping("/{ids}")
    public AjaxResult revoke(@PathVariable Long ids, @RequestBody AssignTable assignTable)
    { 
        return toAjax(assignTableService.revokeAssignTable(ids, assignTable));
    }
}
