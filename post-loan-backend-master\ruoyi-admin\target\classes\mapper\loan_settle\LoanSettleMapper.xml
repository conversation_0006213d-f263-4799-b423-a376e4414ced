<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.loan_settle.mapper.LoanSettleMapper">
    
    <resultMap type="LoanSettle" id="LoanSettleResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="loanId"    column="loan_id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="salesman"    column="salesman"    />
        <result property="orgName"    column="org_name"    />
        <result property="partnerId"    column="partner_id"    />
        <result property="bank"    column="bank"    />
        <result property="loanAmount"    column="loan_amount"    />
        <result property="totalMoney"    column="total_money"    />
        <result property="status"    column="status"    />
        <result property="examineStatus"    column="examine_status"    />
        <result property="reason"    column="reason"    />
        <result property="otherDebt"    column="other_debt"    />
        <result property="accountNumber1"    column="account_number1"    />
        <result property="accountNumber2"    column="account_number2"    />
        <result property="accountNumber3"    column="account_number3"    />
        <result property="accountNumber4"    column="account_number4"    />
        <result property="accountNumber5"    column="account_number5"    />
        <result property="accountImg1"    column="account_img1"    />
        <result property="accountImg2"    column="account_img2"    />
        <result property="accountImg3"    column="account_img3"    />
        <result property="accountImg4"    column="account_img4"    />
        <result property="accountImg5"    column="account_img5"    />
        <result property="reductionImg"    column="reduction_img"    />
        <result property="loanStatus"    column="loan_status"    />
        <result property="remainingAmount" column="remaining_amount" />
        <result property="deductionAmount" column="deduction_amount" />
        <result property="accountMoney1"    column="account_money1"    />
        <result property="accountMoney2"    column="account_money2"    />
        <result property="accountMoney3"    column="account_money3"    />
        <result property="accountMoney4"    column="account_money4"    />
        <result property="pMoney"    column="P_money"    />
        <result property="pPrepaymentImg"    column="P_prepayment_img"    />
        <result property="pAccount"    column="P_account"    />
        <result property="plateNo"    column="plate_no"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="overdueDays" column="overdue_days" />
        <result property="bRepaymentAmounts" column="B_repayment_amounts" />
        <result property="overdueAmt" column="overdue_amt" />
        <result property="certId" column="cert_id" />
    </resultMap>

    <sql id="selectLoanSettleVo">
        select s.*, l.customer_id, c.plate_no, al.overdue_days as overdue_days, al.B_repayment_amounts as B_repayment_amounts, al.overdue_amt as overdue_amt, ci.cert_id as cert_id
        from loan_settle s
        left join loan_list l on s.loan_id = l.id
        left join car_card c on s.apply_id = c.apply_no
        left join account_loan al on s.apply_id = al.apply_id
        left join customer_info ci on l.customer_id = ci.id
    </sql>

    <select id="selectLoanSettleListDetail" parameterType="LoanSettle" resultMap="LoanSettleResult">
        <include refid="selectLoanSettleVo"/>
        <where>
            <if test="loanId != null "> and s.loan_id = #{loanId}</if>
            <if test="status != null "> and s.status = #{status}</if>
        </where>
    </select>

    <select id="selectLoanSettleList" parameterType="LoanSettle" resultMap="LoanSettleResult">
        <include refid="selectLoanSettleVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and s.apply_id = #{applyId}</if>
            <if test="loanId != null "> and s.loan_id = #{loanId}</if>
            <if test="customerName != null  and customerName != ''"> and s.customer_name like concat('%', #{customerName}, '%')</if>
            <if test="salesman != null  and salesman != ''"> and s.salesman like concat('%', #{salesman}, '%')</if>
            <if test="orgName != null  and orgName != ''"> and s.org_name like concat('%', #{orgName}, '%')</if>
            <if test="partnerId != null  and partnerId != ''"> and s.partner_id = #{partnerId}</if>
            <if test="bank != null  and bank != ''"> and s.bank = #{bank}</if>
             <if test="loanAmount != null "> and s.loan_amount = #{loanAmount}</if>
            <if test="totalMoney != null "> and s.total_money = #{totalMoney}</if>

            <if test="status != null "> and s.status = #{status}</if>
            <if test="examineStatus != null "> and s.examine_status = #{examineStatus}</if>
            <if test="reason != null  and reason != ''"> and s.reason = #{reason}</if>
            <if test="otherDebt != null "> and s.other_debt = #{otherDebt}</if>
            <if test="accountNumber1 != null  and accountNumber1 != ''"> and s.account_number1 = #{accountNumber1}</if>
            <if test="accountNumber2 != null  and accountNumber2 != ''"> and s.account_number2 = #{accountNumber2}</if>
            <if test="accountNumber3 != null  and accountNumber3 != ''"> and s.account_number3 = #{accountNumber3}</if>
            <if test="accountNumber4 != null  and accountNumber4 != ''"> and s.account_number4 = #{accountNumber4}</if>
            <if test="accountNumber5 != null  and accountNumber5 != ''"> and s.account_number5 = #{accountNumber5}</if>
            <if test="accountImg1 != null  and accountImg1 != ''"> and s.account_img1 = #{accountImg1}</if>
            <if test="accountImg2 != null  and accountImg2 != ''"> and s.account_img2 = #{accountImg2}</if>
            <if test="accountImg3 != null  and accountImg3 != ''"> and s.account_img3 = #{accountImg3}</if>
            <if test="accountImg4 != null  and accountImg4 != ''"> and s.account_img4 = #{accountImg4}</if>
            <if test="accountImg5 != null  and accountImg5 != ''"> and s.account_img5 = #{accountImg5}</if>
            <if test="reductionImg != null  and reductionImg != ''"> and s.reduction_img = #{reductionImg}</if>
            <if test="loanStatus != null "> and s.loan_status = #{loanStatus}</if>
            <if test="deductionAmount != null "> and s.deduction_amount = #{deductionAmount}</if>
            <if test="accountMoney1 != null  and accountMoney1 != ''"> and s.account_money1 = #{accountMoney1}</if>
            <if test="accountMoney2 != null  and accountMoney2 != ''"> and s.account_money2 = #{accountMoney2}</if>
            <if test="accountMoney3 != null  and accountMoney3 != ''"> and s.account_money3 = #{accountMoney3}</if>
            <if test="accountMoney4 != null  and accountMoney4 != ''"> and s.account_money4 = #{accountMoney4}</if>
            <if test="pMoney != null "> and s.P_money = #{pMoney}</if>
            <if test="pPrepaymentImg != null  and pPrepaymentImg != ''"> and s.P_prepayment_img = #{pPrepaymentImg}</if>
            <if test="pAccount != null  and pAccount != ''"> and s.P_account = #{pAccount}</if>
            <if test="startTime != null and endTime !=null "> and s.create_date between #{startTime} and #{endTime}</if>
            <if test="updateDate != null "> and s.update_date = #{updateDate}</if>
            <if test="plateNo != null and plateNo != ''"> and c.plate_no like concat('%', #{plateNo}, '%')</if>
            <if test="createBy != null "> and s.create_by like concat('%', #{createBy}, '%')</if>
            <if test="certId != null and certId != ''"> and ci.cert_id like concat('%', #{certId}, '%')</if>
        </where>
    </select>
    
    <select id="selectLoanSettleById" parameterType="String" resultMap="LoanSettleResult">
        <include refid="selectLoanSettleVo"/>
       where s.id = #{id}
    </select>

    <insert id="insertLoanSettle" parameterType="LoanSettle" useGeneratedKeys="true" keyProperty="id">
        insert into loan_settle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyId != null">apply_id,</if>
            <if test="loanId != null">loan_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="salesman != null">salesman,</if>
            <if test="orgName != null">org_name,</if>
            <if test="partnerId != null">partner_id,</if>
            <if test="bank != null">bank,</if>
            <if test="loanAmount != null">loan_amount,</if>
            <if test="status != null">status,</if>
            <if test="otherDebt != null">other_debt,</if>
            <if test="totalMoney != null">total_money,</if>


            <if test="examineStatus != null">examine_status,</if>
            <if test="reason != null">reason,</if>

            <if test="accountNumber1 != null">account_number1,</if>
            <if test="accountNumber2 != null">account_number2,</if>
            <if test="accountNumber3 != null">account_number3,</if>
            <if test="accountNumber4 != null">account_number4,</if>
            <if test="accountNumber5 != null">account_number5,</if>
            <if test="accountImg1 != null">account_img1,</if>
            <if test="accountImg2 != null">account_img2,</if>
            <if test="accountImg3 != null">account_img3,</if>
            <if test="accountImg4 != null">account_img4,</if>
            <if test="accountImg5 != null">account_img5,</if>
            <if test="reductionImg != null">reduction_img,</if>
            <if test="loanStatus != null">loan_status,</if>
            <if test="remainingAmount != null">remaining_amount,</if>
            <if test="deductionAmount != null">deduction_amount,</if>
            <if test="accountMoney1 != null">account_money1,</if>
            <if test="accountMoney2 != null">account_money2,</if>
            <if test="accountMoney3 != null">account_money3,</if>
            <if test="accountMoney4 != null">account_money4,</if>
            <if test="pMoney != null">P_money,</if>
            <if test="pPrepaymentImg != null">P_prepayment_img,</if>
            <if test="pAccount != null">P_account,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyId != null">#{applyId},</if>
            <if test="loanId != null">#{loanId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="salesman != null">#{salesman},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="partnerId != null">#{partnerId},</if>
            <if test="bank != null">#{bank},</if>
            <if test="loanAmount != null">#{loanAmount},</if>
            <if test="status != null">#{status},</if>
            
            <if test="otherDebt != null">#{otherDebt},</if>
            <if test="totalMoney != null">#{totalMoney},</if>
            <if test="examineStatus != null">#{examineStatus},</if>
            <if test="reason != null">#{reason},</if>


            <if test="accountNumber1 != null">#{accountNumber1},</if>
            <if test="accountNumber2 != null">#{accountNumber2},</if>
            <if test="accountNumber3 != null">#{accountNumber3},</if>
            <if test="accountNumber4 != null">#{accountNumber4},</if>
            <if test="accountNumber5 != null">#{accountNumber5},</if>
            <if test="accountImg1 != null">#{accountImg1},</if>
            <if test="accountImg2 != null">#{accountImg2},</if>
            <if test="accountImg3 != null">#{accountImg3},</if>
            <if test="accountImg4 != null">#{accountImg4},</if>
            <if test="accountImg5 != null">#{accountImg5},</if>
            <if test="reductionImg != null">#{reductionImg},</if>
            <if test="loanStatus != null">#{loanStatus},</if>
            <if test="remainingAmount != null">#{remainingAmount},</if>
            <if test="deductionAmount != null">#{deductionAmount},</if>
            <if test="accountMoney1 != null">#{accountMoney1},</if>
            <if test="accountMoney2 != null">#{accountMoney2},</if>
            <if test="accountMoney3 != null">#{accountMoney3},</if>
            <if test="accountMoney4 != null">#{accountMoney4},</if>
            <if test="pMoney != null">#{pMoney},</if>
            <if test="pPrepaymentImg != null">#{pPrepaymentImg},</if>
            <if test="pAccount != null">#{pAccount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
         </trim>
    </insert>

    <update id="updateLoanSettle" parameterType="LoanSettle">
        update loan_settle
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyId != null">apply_id = #{applyId},</if>
            <if test="loanId != null">loan_id = #{loanId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="salesman != null">salesman = #{salesman},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="partnerId != null">partner_id = #{partnerId},</if>
            <if test="bank != null">bank = #{bank},</if>
            <if test="loanAmount != null">loan_amount = #{loanAmount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="examineStatus != null">examine_status = #{examineStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="otherDebt != null">other_debt = #{otherDebt},</if>
            <if test="totalMoney != null">total_money = #{totalMoney},</if>


            <if test="accountNumber1 != null">account_number1 = #{accountNumber1},</if>
            <if test="accountNumber2 != null">account_number2 = #{accountNumber2},</if>
            <if test="accountNumber3 != null">account_number3 = #{accountNumber3},</if>
            <if test="accountNumber4 != null">account_number4 = #{accountNumber4},</if>
            <if test="accountNumber5 != null">account_number5 = #{accountNumber5},</if>
            <if test="accountImg1 != null">account_img1 = #{accountImg1},</if>
            <if test="accountImg2 != null">account_img2 = #{accountImg2},</if>
            <if test="accountImg3 != null">account_img3 = #{accountImg3},</if>
            <if test="accountImg4 != null">account_img4 = #{accountImg4},</if>
            <if test="accountImg5 != null">account_img5 = #{accountImg5},</if>
            <if test="reductionImg != null">reduction_img = #{reductionImg},</if>
            <if test="loanStatus != null">loan_status = #{loanStatus},</if>
            <if test="remainingAmount != null">remaining_amount = #{remainingAmount},</if>
            <if test="deductionAmount != null">deduction_amount = #{deductionAmount},</if>
            <if test="accountMoney1 != null">account_money1 = #{accountMoney1},</if>
            <if test="accountMoney2 != null">account_money2 = #{accountMoney2},</if>
            <if test="accountMoney3 != null">account_money3 = #{accountMoney3},</if>
            <if test="accountMoney4 != null">account_money4 = #{accountMoney4},</if>
            <if test="pMoney != null">P_money = #{pMoney},</if>
            <if test="pPrepaymentImg != null">P_prepayment_img = #{pPrepaymentImg},</if>
            <if test="pAccount != null">P_account = #{pAccount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLoanSettleById" parameterType="String">
        delete from loan_settle where id = #{id}
    </delete>

    <delete id="deleteLoanSettleByIds" parameterType="String">
        delete from loan_settle where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>