package com.ruoyi.guaranty_info.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.guaranty_info.domain.GuarantyInfo;
import com.ruoyi.guaranty_info.service.IGuarantyInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 抵押登记Controller
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@RestController
@RequestMapping("/guaranty_info/guaranty_info")
public class GuarantyInfoController extends BaseController
{
    @Autowired
    private IGuarantyInfoService guarantyInfoService;

    /**
     * 查询抵押登记列表
     */
    @PreAuthorize("@ss.hasPermi('guaranty_info:guaranty_info:list')")
    @GetMapping("/list")
    public TableDataInfo list(GuarantyInfo guarantyInfo)
    {
        startPage();
        List<GuarantyInfo> list = guarantyInfoService.selectGuarantyInfoList(guarantyInfo);
        return getDataTable(list);
    }

    /**
     * 导出抵押登记列表
     */
    @PreAuthorize("@ss.hasPermi('guaranty_info:guaranty_info:export')")
    @Log(title = "抵押登记", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GuarantyInfo guarantyInfo)
    {
        List<GuarantyInfo> list = guarantyInfoService.selectGuarantyInfoList(guarantyInfo);
        ExcelUtil<GuarantyInfo> util = new ExcelUtil<GuarantyInfo>(GuarantyInfo.class);
        util.exportExcel(response, list, "抵押登记数据");
    }

    /**
     * 获取抵押登记详细信息
     */
//    @PreAuthorize("@ss.hasPermi('guaranty_info:guaranty_info:query')")
    @GetMapping(value = "/{id}")
    @Anonymous
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(guarantyInfoService.selectGuarantyInfoById(id));
    }

    /**
     * 新增抵押登记
     */
    @PreAuthorize("@ss.hasPermi('guaranty_info:guaranty_info:add')")
    @Log(title = "抵押登记", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GuarantyInfo guarantyInfo)
    {
        return toAjax(guarantyInfoService.insertGuarantyInfo(guarantyInfo));
    }

    /**
     * 修改抵押登记
     */
    @PreAuthorize("@ss.hasPermi('guaranty_info:guaranty_info:edit')")
    @Log(title = "抵押登记", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GuarantyInfo guarantyInfo)
    {
        return toAjax(guarantyInfoService.updateGuarantyInfo(guarantyInfo));
    }

    /**
     * 删除抵押登记
     */
    @PreAuthorize("@ss.hasPermi('guaranty_info:guaranty_info:remove')")
    @Log(title = "抵押登记", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(guarantyInfoService.deleteGuarantyInfoByIds(ids));
    }
}
