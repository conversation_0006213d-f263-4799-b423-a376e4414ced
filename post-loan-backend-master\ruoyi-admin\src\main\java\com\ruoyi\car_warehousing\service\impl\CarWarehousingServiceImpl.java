package com.ruoyi.car_warehousing.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.car_warehousing.mapper.CarWarehousingMapper;
import com.ruoyi.car_warehousing.domain.CarWarehousing;
import com.ruoyi.car_warehousing.service.ICarWarehousingService;

/**
 * 车辆出入库Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
public class CarWarehousingServiceImpl implements ICarWarehousingService 
{
    @Autowired
    private CarWarehousingMapper carWarehousingMapper;

    /**
     * 查询车辆出入库
     * 
     * @param id 车辆出入库主键
     * @return 车辆出入库
     */
    @Override
    public CarWarehousing selectCarWarehousingById(String id)
    {
        return carWarehousingMapper.selectCarWarehousingById(id);
    }

    /**
     * 查询车辆出入库列表
     * 
     * @param carWarehousing 车辆出入库
     * @return 车辆出入库
     */
    @Override
    public List<CarWarehousing> selectCarWarehousingList(CarWarehousing carWarehousing)
    {
        return carWarehousingMapper.selectCarWarehousingList(carWarehousing);
    }

    /**
     * 新增车辆出入库
     * 
     * @param carWarehousing 车辆出入库
     * @return 结果
     */
    @Override
    public int insertCarWarehousing(CarWarehousing carWarehousing)
    {
        return carWarehousingMapper.insertCarWarehousing(carWarehousing);
    }

    /**
     * 修改车辆出入库
     * 
     * @param carWarehousing 车辆出入库
     * @return 结果
     */
    @Override
    public int updateCarWarehousing(CarWarehousing carWarehousing)
    {
        return carWarehousingMapper.updateCarWarehousing(carWarehousing);
    }

    /**
     * 批量删除车辆出入库
     * 
     * @param ids 需要删除的车辆出入库主键
     * @return 结果
     */
    @Override
    public int deleteCarWarehousingByIds(String[] ids)
    {
        return carWarehousingMapper.deleteCarWarehousingByIds(ids);
    }

    /**
     * 删除车辆出入库信息
     * 
     * @param id 车辆出入库主键
     * @return 结果
     */
    @Override
    public int deleteCarWarehousingById(String id)
    {
        return carWarehousingMapper.deleteCarWarehousingById(id);
    }
}
