package com.ruoyi.coborrower_info.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 担保人/共借人对象 coborrower_info
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public class CoborrowerInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyNo;

    /** 客户编号 */
    @Excel(name = "客户编号")
    private String customerId;

    /** 客户姓名 */
    @Excel(name = "客户姓名")
    private String customerName;

    /** 证件类型 */
    @Excel(name = "证件类型")
    private String certType;

    /** 证件号码 */
    @Excel(name = "证件号码")
    private String certId;

    /** 签发机关 */
    @Excel(name = "签发机关")
    private String creditissueOrg;

    /** 证件地址 */
    @Excel(name = "证件地址")
    private String address;

    /** 证件起始日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "证件起始日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date certBeginDate;

    /** 证件到期日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "证件到期日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date certEndDate;

    /** 性别 */
    @Excel(name = "性别")
    private String sex;

    /** 年龄 */
    @Excel(name = "年龄")
    private Long age;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String mobileNo;

    /** 与主贷人关系 */
    @Excel(name = "与主贷人关系")
    private String principalRelation;

    /** 担保方式 */
    @Excel(name = "担保方式")
    private String guaranteeType;

    /** 提供资料 */
    @Excel(name = "提供资料")
    private String infoType;

    /** 担保类型 */
    @Excel(name = "担保类型")
    private String borrowerType;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 删除标记 */
    private String delFlag;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setApplyNo(String applyNo) 
    {
        this.applyNo = applyNo;
    }

    public String getApplyNo() 
    {
        return applyNo;
    }

    public void setCustomerId(String customerId) 
    {
        this.customerId = customerId;
    }

    public String getCustomerId() 
    {
        return customerId;
    }

    public void setCustomerName(String customerName) 
    {
        this.customerName = customerName;
    }

    public String getCustomerName() 
    {
        return customerName;
    }

    public void setCertType(String certType) 
    {
        this.certType = certType;
    }

    public String getCertType() 
    {
        return certType;
    }

    public void setCertId(String certId) 
    {
        this.certId = certId;
    }

    public String getCertId() 
    {
        return certId;
    }

    public void setCreditissueOrg(String creditissueOrg) 
    {
        this.creditissueOrg = creditissueOrg;
    }

    public String getCreditissueOrg() 
    {
        return creditissueOrg;
    }

    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }

    public void setCertBeginDate(Date certBeginDate) 
    {
        this.certBeginDate = certBeginDate;
    }

    public Date getCertBeginDate() 
    {
        return certBeginDate;
    }

    public void setCertEndDate(Date certEndDate) 
    {
        this.certEndDate = certEndDate;
    }

    public Date getCertEndDate() 
    {
        return certEndDate;
    }

    public void setSex(String sex) 
    {
        this.sex = sex;
    }

    public String getSex() 
    {
        return sex;
    }

    public void setAge(Long age) 
    {
        this.age = age;
    }

    public Long getAge() 
    {
        return age;
    }

    public void setMobileNo(String mobileNo) 
    {
        this.mobileNo = mobileNo;
    }

    public String getMobileNo() 
    {
        return mobileNo;
    }

    public void setPrincipalRelation(String principalRelation) 
    {
        this.principalRelation = principalRelation;
    }

    public String getPrincipalRelation() 
    {
        return principalRelation;
    }

    public void setGuaranteeType(String guaranteeType) 
    {
        this.guaranteeType = guaranteeType;
    }

    public String getGuaranteeType() 
    {
        return guaranteeType;
    }

    public void setInfoType(String infoType) 
    {
        this.infoType = infoType;
    }

    public String getInfoType() 
    {
        return infoType;
    }

    public void setBorrowerType(String borrowerType) 
    {
        this.borrowerType = borrowerType;
    }

    public String getBorrowerType() 
    {
        return borrowerType;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("applyNo", getApplyNo())
            .append("customerId", getCustomerId())
            .append("customerName", getCustomerName())
            .append("certType", getCertType())
            .append("certId", getCertId())
            .append("creditissueOrg", getCreditissueOrg())
            .append("address", getAddress())
            .append("certBeginDate", getCertBeginDate())
            .append("certEndDate", getCertEndDate())
            .append("sex", getSex())
            .append("age", getAge())
            .append("mobileNo", getMobileNo())
            .append("principalRelation", getPrincipalRelation())
            .append("guaranteeType", getGuaranteeType())
            .append("infoType", getInfoType())
            .append("borrowerType", getBorrowerType())
            .append("createBy", getCreateBy())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
