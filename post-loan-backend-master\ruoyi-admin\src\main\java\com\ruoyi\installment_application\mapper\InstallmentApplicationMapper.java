package com.ruoyi.installment_application.mapper;

import java.util.List;
import com.ruoyi.installment_application.domain.InstallmentApplication;

/**
 * 分期申请Mapper接口
 * 字段变更说明：2024-07-11 repayDay由String改为Date，新增actualPaymentAmount字段（BigDecimal）
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface InstallmentApplicationMapper {
    /**
     * 查询分期申请
     * 
     * @param id 分期申请主键
     * @return 分期申请
     */
    public InstallmentApplication selectInstallmentApplicationById(Long id);

    /**
     * 查询分期申请列表
     * 
     * @param installmentApplication 分期申请
     * @return 分期申请集合
     */
    public List<InstallmentApplication> selectInstallmentApplicationList(InstallmentApplication installmentApplication);

    /**
     * 新增分期申请
     * 
     * @param installmentApplication 分期申请
     * @return 结果
     */
    public int insertInstallmentApplication(InstallmentApplication installmentApplication);

    /**
     * 修改分期申请
     * 
     * @param installmentApplication 分期申请
     * @return 结果
     */
    public int updateInstallmentApplication(InstallmentApplication installmentApplication);

    /**
     * 删除分期申请
     * 
     * @param id 分期申请主键
     * @return 结果
     */
    public int deleteInstallmentApplicationById(Long id);

    /**
     * 批量删除分期申请
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstallmentApplicationByIds(Long[] ids);
}
