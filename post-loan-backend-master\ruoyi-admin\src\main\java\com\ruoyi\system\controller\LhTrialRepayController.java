package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.LhTrialRepay;
import com.ruoyi.system.service.ILhTrialRepayService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 蓝海还款试算Controller
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@RestController
@RequestMapping("/system/lh")
public class LhTrialRepayController extends BaseController
{
    @Autowired
    private ILhTrialRepayService lhTrialRepayService;

    /**
     * 查询蓝海还款试算列表
     */
//    @PreAuthorize("@ss.hasPermi('system:repay:list')")
    @GetMapping("/list")
    public TableDataInfo list(LhTrialRepay lhTrialRepay)
    {
        startPage();
        List<LhTrialRepay> list = lhTrialRepayService.selectLhTrialRepayList(lhTrialRepay);
        return getDataTable(list);
    }

    /**
     * 导出蓝海还款试算列表
     */
    @PreAuthorize("@ss.hasPermi('system:repay:export')")
    @Log(title = "蓝海还款试算", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LhTrialRepay lhTrialRepay)
    {
        List<LhTrialRepay> list = lhTrialRepayService.selectLhTrialRepayList(lhTrialRepay);
        ExcelUtil<LhTrialRepay> util = new ExcelUtil<LhTrialRepay>(LhTrialRepay.class);
        util.exportExcel(response, list, "蓝海还款试算数据");
    }

    /**
     * 获取蓝海还款试算详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:repay:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(lhTrialRepayService.selectLhTrialRepayById(id));
    }

    /**
     * 新增蓝海还款试算
     */
    @PreAuthorize("@ss.hasPermi('system:repay:add')")
    @Log(title = "蓝海还款试算", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LhTrialRepay lhTrialRepay)
    {
        return toAjax(lhTrialRepayService.insertLhTrialRepay(lhTrialRepay));
    }

    /**
     * 修改蓝海还款试算
     */
    @PreAuthorize("@ss.hasPermi('system:repay:edit')")
    @Log(title = "蓝海还款试算", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LhTrialRepay lhTrialRepay)
    {
        return toAjax(lhTrialRepayService.updateLhTrialRepay(lhTrialRepay));
    }

    /**
     * 删除蓝海还款试算
     */
    @PreAuthorize("@ss.hasPermi('system:repay:remove')")
    @Log(title = "蓝海还款试算", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(lhTrialRepayService.deleteLhTrialRepayByIds(ids));
    }
}
