package com.ruoyi.assign_table.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.assign_table.mapper.AssignTableMapper;
import com.ruoyi.assign_table.domain.AssignTable;
import com.ruoyi.assign_table.service.IAssignTableService;

/**
 * 指派Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
public class AssignTableServiceImpl implements IAssignTableService 
{
    @Autowired
    private AssignTableMapper assignTableMapper;

    /**
     * 查询指派
     * 
     * @param id 指派主键
     * @return 指派
     */
    @Override
    public AssignTable selectAssignTableById(Long id)
    {
        return assignTableMapper.selectAssignTableById(id);
    }

    /**
     * 查询指派列表
     * 
     * @param assignTable 指派
     * @return 指派
     */
    @Override
    public List<AssignTable> selectAssignTableList(AssignTable assignTable)
    {
        return assignTableMapper.selectAssignTableList(assignTable);
    }

    /**
     * 新增指派
     * 
     * @param assignTable 指派
     * @return 结果
     */
    @Override
    public int insertAssignTable(AssignTable assignTable)
    {
        assignTable.setCreateTime(DateUtils.getNowDate());
        assignTable.setAssignTime(DateUtils.getNowDate());
        assignTable.setStatus("assigned");
        return assignTableMapper.insertAssignTable(assignTable);
    }

    /**
     * 修改指派
     * 
     * @param assignTable 指派
     * @return 结果
     */
    @Override
    public int updateAssignTable(AssignTable assignTable)
    {
        assignTable.setUpdateTime(DateUtils.getNowDate());
        return assignTableMapper.updateAssignTable(assignTable);
    }

    /**
     * 批量删除指派
     * 
     * @param ids 需要删除的指派主键
     * @return 结果
     */
    @Override
    public int deleteAssignTableByIds(Long[] ids)
    {
        return assignTableMapper.deleteAssignTableByIds(ids);
    }

    /**
     * 删除指派信息
     *
     * @param id 指派主键
     * @return 结果
     */
    @Override
    public int deleteAssignTableById(Long id)
    {
        return assignTableMapper.deleteAssignTableById(id);
    }

    /**
     * 撤销指派
     *
     * @param id 指派主键
     * @return 结果
     */
    @Override
    public int revokeAssignTable(Long id, AssignTable assignTable) {
        AssignTable table = selectAssignTableById(id);
        if (table == null) {
            return 0;
        }
        // 更新状态为手动撤销
        table.setStatus("manual_revoked");
        table.setRevokeTime(DateUtils.getNowDate());
        table.setUpdateTime(DateUtils.getNowDate());
        return assignTableMapper.updateAssignTable(table);
    }
}
