<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.litigation_cost.mapper.LitigationCostMapper">
    
    <resultMap type="LitigationCost" id="LitigationCostResult">
        <result property="id"    column="id"    />
        <result property="litigationCaseId"    column="litigation_case_id"    />
        <result property="lawyerFee"    column="lawyer_fee"    />
        <result property="litigationFee"    column="litigation_fee"    />
        <result property="preservationFee"    column="preservation_fee"    />
        <result property="surveillanceFee"    column="surveillance_fee"    />
        <result property="announcementFee"    column="announcement_fee"    />
        <result property="appraisalFee"    column="appraisal_fee"    />
        <result property="executionFee"    column="execution_fee"    />
        <result property="penalty"    column="penalty"    />
        <result property="guaranteeFee"    column="guarantee_fee"    />
        <result property="intermediaryFee"    column="intermediary_fee"    />
        <result property="compensity"    column="compensity"    />
        <result property="judgmentAmount"    column="judgment_amount"    />
        <result property="interest"    column="interest"    />
        <result property="otherAmountsOwed"    column="other_amounts_owed"    />
        <result property="insurance"    column="insurance"    />
        <result property="totalMoney"    column="total_money"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="reasons"    column="reasons"    />
        <result property="approveBy"    column="approve_by"    />
        <result property="approveRole"    column="approve_role"    />
        <result property="applicationTime"    column="application_time"    />
        <result property="applicationBy"    column="application_by"    />
    </resultMap>

    <sql id="selectLitigationCostVo">
        select id, litigation_case_id, lawyer_fee, litigation_fee, preservation_fee, surveillance_fee, announcement_fee, appraisal_fee, execution_fee, penalty, guarantee_fee, intermediary_fee, compensity, judgment_amount, interest, other_amounts_owed, insurance, total_money, approval_status, reasons, approve_by, approve_role, application_time, application_by from litigation_cost
    </sql>

    <select id="selectLitigationCostList" parameterType="LitigationCost" resultMap="LitigationCostResult">
        <include refid="selectLitigationCostVo"/>
        <where>  
            <if test="litigationCaseId != null "> and litigation_case_id = #{litigationCaseId}</if>
            <if test="lawyerFee != null "> and lawyer_fee = #{lawyerFee}</if>
            <if test="litigationFee != null "> and litigation_fee = #{litigationFee}</if>
            <if test="preservationFee != null "> and preservation_fee = #{preservationFee}</if>
            <if test="surveillanceFee != null "> and surveillance_fee = #{surveillanceFee}</if>
            <if test="announcementFee != null "> and announcement_fee = #{announcementFee}</if>
            <if test="appraisalFee != null "> and appraisal_fee = #{appraisalFee}</if>
            <if test="executionFee != null "> and execution_fee = #{executionFee}</if>
            <if test="penalty != null "> and penalty = #{penalty}</if>
            <if test="guaranteeFee != null "> and guarantee_fee = #{guaranteeFee}</if>
            <if test="intermediaryFee != null "> and intermediary_fee = #{intermediaryFee}</if>
            <if test="compensity != null "> and compensity = #{compensity}</if>
            <if test="judgmentAmount != null "> and judgment_amount = #{judgmentAmount}</if>
            <if test="interest != null "> and interest = #{interest}</if>
            <if test="otherAmountsOwed != null "> and other_amounts_owed = #{otherAmountsOwed}</if>
            <if test="insurance != null "> and insurance = #{insurance}</if>
            <if test="totalMoney != null"> and total_money = #{totalMoney}</if>
            <if test="approvalStatus != null  and approvalStatus != ''"> and approval_status = #{approvalStatus}</if>
            <if test="reasons != null  and reasons != ''"> and reasons = #{reasons}</if>
            <if test="approveBy != null  and approveBy != ''"> and approve_by = #{approveBy}</if>
            <if test="approveRole != null  and approveRole != ''"> and approve_role = #{approveRole}</if>
            <if test="applicationTime != null "> and application_time = #{applicationTime}</if>
            <if test="applicationBy != null  and applicationBy != ''"> and application_by = #{applicationBy}</if>
        </where>
    </select>
    
    <select id="selectLitigationCostById" parameterType="Long" resultMap="LitigationCostResult">
        <include refid="selectLitigationCostVo"/>
        where id = #{id}
    </select>

    <insert id="insertLitigationCost" parameterType="LitigationCost" useGeneratedKeys="true" keyProperty="id">
        insert into litigation_cost
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="litigationCaseId != null">litigation_case_id,</if>
            <if test="lawyerFee != null">lawyer_fee,</if>
            <if test="litigationFee != null">litigation_fee,</if>
            <if test="preservationFee != null">preservation_fee,</if>
            <if test="surveillanceFee != null">surveillance_fee,</if>
            <if test="announcementFee != null">announcement_fee,</if>
            <if test="appraisalFee != null">appraisal_fee,</if>
            <if test="executionFee != null">execution_fee,</if>
            <if test="penalty != null">penalty,</if>
            <if test="guaranteeFee != null">guarantee_fee,</if>
            <if test="intermediaryFee != null">intermediary_fee,</if>
            <if test="compensity != null">compensity,</if>
            <if test="judgmentAmount != null">judgment_amount,</if>
            <if test="interest != null">interest,</if>
            <if test="otherAmountsOwed != null">other_amounts_owed,</if>
            <if test="insurance != null">insurance,</if>
            <if test="totalMoney != null">total_money,</if>
            <if test="approvalStatus != null">approval_status,</if>
            <if test="reasons != null">reasons,</if>
            <if test="approveBy != null">approve_by,</if>
            <if test="approveRole != null">approve_role,</if>
            <if test="applicationTime != null">application_time,</if>
            <if test="applicationBy != null">application_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="litigationCaseId != null">#{litigationCaseId},</if>
            <if test="lawyerFee != null">#{lawyerFee},</if>
            <if test="litigationFee != null">#{litigationFee},</if>
            <if test="preservationFee != null">#{preservationFee},</if>
            <if test="surveillanceFee != null">#{surveillanceFee},</if>
            <if test="announcementFee != null">#{announcementFee},</if>
            <if test="appraisalFee != null">#{appraisalFee},</if>
            <if test="executionFee != null">#{executionFee},</if>
            <if test="penalty != null">#{penalty},</if>
            <if test="guaranteeFee != null">#{guaranteeFee},</if>
            <if test="intermediaryFee != null">#{intermediaryFee},</if>
            <if test="compensity != null">#{compensity},</if>
            <if test="judgmentAmount != null">#{judgmentAmount},</if>
            <if test="interest != null">#{interest},</if>
            <if test="otherAmountsOwed != null">#{otherAmountsOwed},</if>
            <if test="insurance != null">#{insurance},</if>
            <if test="totalMoney != null">#{totalMoney},</if>
            <if test="approvalStatus != null">#{approvalStatus},</if>
            <if test="reasons != null">#{reasons},</if>
            <if test="approveBy != null">#{approveBy},</if>
            <if test="approveRole != null">#{approveRole},</if>
            <if test="applicationTime != null">#{applicationTime},</if>
            <if test="applicationBy != null">#{applicationBy},</if>
         </trim>
    </insert>

    <update id="updateLitigationCost" parameterType="LitigationCost">
        update litigation_cost
        <trim prefix="SET" suffixOverrides=",">
            <if test="litigationCaseId != null">litigation_case_id = #{litigationCaseId},</if>
            <if test="lawyerFee != null">lawyer_fee = #{lawyerFee},</if>
            <if test="litigationFee != null">litigation_fee = #{litigationFee},</if>
            <if test="preservationFee != null">preservation_fee = #{preservationFee},</if>
            <if test="surveillanceFee != null">surveillance_fee = #{surveillanceFee},</if>
            <if test="announcementFee != null">announcement_fee = #{announcementFee},</if>
            <if test="appraisalFee != null">appraisal_fee = #{appraisalFee},</if>
            <if test="executionFee != null">execution_fee = #{executionFee},</if>
            <if test="penalty != null">penalty = #{penalty},</if>
            <if test="guaranteeFee != null">guarantee_fee = #{guaranteeFee},</if>
            <if test="intermediaryFee != null">intermediary_fee = #{intermediaryFee},</if>
            <if test="compensity != null">compensity = #{compensity},</if>
            <if test="judgmentAmount != null">judgment_amount = #{judgmentAmount},</if>
            <if test="interest != null">interest = #{interest},</if>
            <if test="otherAmountsOwed != null">other_amounts_owed = #{otherAmountsOwed},</if>
            <if test="insurance != null">insurance = #{insurance},</if>
            <if test="totalMoney != null">total_money = #{totalMoney},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            <if test="reasons != null">reasons = #{reasons},</if>
            <if test="approveBy != null">approve_by = #{approveBy},</if>
            <if test="approveRole != null">approve_role = #{approveRole},</if>
            <if test="applicationTime != null">application_time = #{applicationTime},</if>
            <if test="applicationBy != null">application_by = #{applicationBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLitigationCostById" parameterType="Long">
        delete from litigation_cost where id = #{id}
    </delete>

    <delete id="deleteLitigationCostByIds" parameterType="String">
        delete from litigation_cost where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>