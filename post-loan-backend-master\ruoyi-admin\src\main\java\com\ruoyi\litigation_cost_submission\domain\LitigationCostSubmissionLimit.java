package com.ruoyi.litigation_cost_submission.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 法诉费用提交限制记录对象 litigation_cost_submission_limit
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class LitigationCostSubmissionLimit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 法诉案件ID */
    @Excel(name = "法诉案件ID")
    private Long litigationCaseId;

    /** 费用类型（judgment_amount-判决金额，interest-利息） */
    @Excel(name = "费用类型", readConverterExp = "j=udgment_amount-判决金额，interest-利息")
    private String costType;

    /** 是否已提交（0-未提交，1-已提交） */
    @Excel(name = "是否已提交", readConverterExp = "0=-未提交，1-已提交")
    private String isSubmitted;

    /** 提交时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date submissionTime;

    /** 提交人 */
    @Excel(name = "提交人")
    private String submittedBy;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLitigationCaseId(Long litigationCaseId) 
    {
        this.litigationCaseId = litigationCaseId;
    }

    public Long getLitigationCaseId() 
    {
        return litigationCaseId;
    }

    public void setCostType(String costType) 
    {
        this.costType = costType;
    }

    public String getCostType() 
    {
        return costType;
    }

    public void setIsSubmitted(String isSubmitted) 
    {
        this.isSubmitted = isSubmitted;
    }

    public String getIsSubmitted() 
    {
        return isSubmitted;
    }

    public void setSubmissionTime(Date submissionTime) 
    {
        this.submissionTime = submissionTime;
    }

    public Date getSubmissionTime() 
    {
        return submissionTime;
    }

    public void setSubmittedBy(String submittedBy) 
    {
        this.submittedBy = submittedBy;
    }

    public String getSubmittedBy() 
    {
        return submittedBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("litigationCaseId", getLitigationCaseId())
            .append("costType", getCostType())
            .append("isSubmitted", getIsSubmitted())
            .append("submissionTime", getSubmissionTime())
            .append("submittedBy", getSubmittedBy())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
