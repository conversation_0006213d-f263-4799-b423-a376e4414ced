package com.ruoyi.reimburse.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 报销记录对象 reimburse
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
public class Reimburse extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 用户id */
    @Excel(name = "用户id")
    private String memberId;

    /** 报销内容 */
    @Excel(name = "报销内容")
    private String content;

    /** 报销金额 */
    @Excel(name = "报销金额")
    private BigDecimal money;

    /** 报销凭据 */
    @Excel(name = "报销凭据")
    private String img;

    /** 审核状态（0-未审核，1-通过，2-拒绝） */
    @Excel(name = "审核状态", readConverterExp = "0=-未审核，1-通过，2-拒绝")
    private String status;

    /** 1-油费，2-路费，3-其他 */
    @Excel(name = "1-油费，2-路费，3-其他")
    private String state;

    /** 1-有钥匙，2-无钥匙 */
    @Excel(name = "1-有钥匙，2-无钥匙")
    private String keyStatus;

    /** 开始地址 */
    @Excel(name = "开始地址")
    private String startAddress;

    /** 结束地址 */
    @Excel(name = "结束地址")
    private String endAddress;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 是否删除(0-否，2是) */
    private String delFlag;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setMemberId(String memberId) 
    {
        this.memberId = memberId;
    }

    public String getMemberId() 
    {
        return memberId;
    }

    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    public void setMoney(BigDecimal money) 
    {
        this.money = money;
    }

    public BigDecimal getMoney() 
    {
        return money;
    }

    public void setImg(String img) 
    {
        this.img = img;
    }

    public String getImg() 
    {
        return img;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setState(String state) 
    {
        this.state = state;
    }

    public String getState() 
    {
        return state;
    }

    public void setKeyStatus(String keyStatus) 
    {
        this.keyStatus = keyStatus;
    }

    public String getKeyStatus() 
    {
        return keyStatus;
    }

    public void setStartAddress(String startAddress) 
    {
        this.startAddress = startAddress;
    }

    public String getStartAddress() 
    {
        return startAddress;
    }

    public void setEndAddress(String endAddress) 
    {
        this.endAddress = endAddress;
    }

    public String getEndAddress() 
    {
        return endAddress;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("memberId", getMemberId())
            .append("content", getContent())
            .append("money", getMoney())
            .append("img", getImg())
            .append("status", getStatus())
            .append("state", getState())
            .append("keyStatus", getKeyStatus())
            .append("remark", getRemark())
            .append("startAddress", getStartAddress())
            .append("endAddress", getEndAddress())
            .append("createBy", getCreateBy())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
