<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.sy_repay_plan.mapper.SyRepayPlanMapper">
    
    <resultMap type="SyRepayPlan" id="SyRepayPlanResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="syApplyId"    column="sy_apply_id"    />
        <result property="stageNum"    column="stage_num"    />
        <result property="loanDate"    column="loan_date"    />
        <result property="repayDate"    column="repay_date"    />
        <result property="actualRepayDate"    column="actual_repay_date"    />
        <result property="repayAmount"    column="repay_amount"    />
        <result property="capital"    column="capital"    />
        <result property="interest"    column="interest"    />
        <result property="lastCapital"    column="last_capital"    />
        <result property="bondAmount"    column="bond_amount"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="sareDate"    column="sare_date"    />
        <result property="shareAmount"    column="share_amount"    />
        <result property="lateCharge"    column="late_charge"    />
        <result property="realCharge"    column="real_charge"    />
        <result property="derateCharge"    column="derate_charge"    />
        <result property="status"    column="status"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
    </resultMap>

    <sql id="selectSyRepayPlanVo">
        select id, apply_id, sy_apply_id, stage_num, loan_date, repay_date, actual_repay_date, repay_amount, capital, interest, last_capital, bond_amount, refund_amount, sare_date, share_amount, late_charge, real_charge, derate_charge, status, create_date, update_date from sy_repay_plan
    </sql>

    <select id="selectSyRepayPlanList" parameterType="SyRepayPlan" resultMap="SyRepayPlanResult">
        <include refid="selectSyRepayPlanVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="syApplyId != null  and syApplyId != ''"> and sy_apply_id = #{syApplyId}</if>
            <if test="stageNum != null  and stageNum != ''"> and stage_num = #{stageNum}</if>
            <if test="loanDate != null  and loanDate != ''"> and loan_date = #{loanDate}</if>
            <if test="repayDate != null  and repayDate != ''"> and repay_date = #{repayDate}</if>
            <if test="actualRepayDate != null  and actualRepayDate != ''"> and actual_repay_date = #{actualRepayDate}</if>
            <if test="repayAmount != null  and repayAmount != ''"> and repay_amount = #{repayAmount}</if>
            <if test="capital != null  and capital != ''"> and capital = #{capital}</if>
            <if test="interest != null  and interest != ''"> and interest = #{interest}</if>
            <if test="lastCapital != null  and lastCapital != ''"> and last_capital = #{lastCapital}</if>
            <if test="bondAmount != null  and bondAmount != ''"> and bond_amount = #{bondAmount}</if>
            <if test="refundAmount != null  and refundAmount != ''"> and refund_amount = #{refundAmount}</if>
            <if test="sareDate != null  and sareDate != ''"> and sare_date = #{sareDate}</if>
            <if test="shareAmount != null  and shareAmount != ''"> and share_amount = #{shareAmount}</if>
            <if test="lateCharge != null  and lateCharge != ''"> and late_charge = #{lateCharge}</if>
            <if test="realCharge != null  and realCharge != ''"> and real_charge = #{realCharge}</if>
            <if test="derateCharge != null  and derateCharge != ''"> and derate_charge = #{derateCharge}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
    </select>
    
    <select id="selectSyRepayPlanById" parameterType="String" resultMap="SyRepayPlanResult">
        <include refid="selectSyRepayPlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertSyRepayPlan" parameterType="SyRepayPlan">
        insert into sy_repay_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applyId != null">apply_id,</if>
            <if test="syApplyId != null">sy_apply_id,</if>
            <if test="stageNum != null">stage_num,</if>
            <if test="loanDate != null">loan_date,</if>
            <if test="repayDate != null">repay_date,</if>
            <if test="actualRepayDate != null">actual_repay_date,</if>
            <if test="repayAmount != null">repay_amount,</if>
            <if test="capital != null">capital,</if>
            <if test="interest != null">interest,</if>
            <if test="lastCapital != null">last_capital,</if>
            <if test="bondAmount != null">bond_amount,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="sareDate != null">sare_date,</if>
            <if test="shareAmount != null">share_amount,</if>
            <if test="lateCharge != null">late_charge,</if>
            <if test="realCharge != null">real_charge,</if>
            <if test="derateCharge != null">derate_charge,</if>
            <if test="status != null">status,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateDate != null">update_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applyId != null">#{applyId},</if>
            <if test="syApplyId != null">#{syApplyId},</if>
            <if test="stageNum != null">#{stageNum},</if>
            <if test="loanDate != null">#{loanDate},</if>
            <if test="repayDate != null">#{repayDate},</if>
            <if test="actualRepayDate != null">#{actualRepayDate},</if>
            <if test="repayAmount != null">#{repayAmount},</if>
            <if test="capital != null">#{capital},</if>
            <if test="interest != null">#{interest},</if>
            <if test="lastCapital != null">#{lastCapital},</if>
            <if test="bondAmount != null">#{bondAmount},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="sareDate != null">#{sareDate},</if>
            <if test="shareAmount != null">#{shareAmount},</if>
            <if test="lateCharge != null">#{lateCharge},</if>
            <if test="realCharge != null">#{realCharge},</if>
            <if test="derateCharge != null">#{derateCharge},</if>
            <if test="status != null">#{status},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateDate != null">#{updateDate},</if>
         </trim>
    </insert>

    <update id="updateSyRepayPlan" parameterType="SyRepayPlan">
        update sy_repay_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyId != null">apply_id = #{applyId},</if>
            <if test="syApplyId != null">sy_apply_id = #{syApplyId},</if>
            <if test="stageNum != null">stage_num = #{stageNum},</if>
            <if test="loanDate != null">loan_date = #{loanDate},</if>
            <if test="repayDate != null">repay_date = #{repayDate},</if>
            <if test="actualRepayDate != null">actual_repay_date = #{actualRepayDate},</if>
            <if test="repayAmount != null">repay_amount = #{repayAmount},</if>
            <if test="capital != null">capital = #{capital},</if>
            <if test="interest != null">interest = #{interest},</if>
            <if test="lastCapital != null">last_capital = #{lastCapital},</if>
            <if test="bondAmount != null">bond_amount = #{bondAmount},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="sareDate != null">sare_date = #{sareDate},</if>
            <if test="shareAmount != null">share_amount = #{shareAmount},</if>
            <if test="lateCharge != null">late_charge = #{lateCharge},</if>
            <if test="realCharge != null">real_charge = #{realCharge},</if>
            <if test="derateCharge != null">derate_charge = #{derateCharge},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSyRepayPlanById" parameterType="String">
        delete from sy_repay_plan where id = #{id}
    </delete>

    <delete id="deleteSyRepayPlanByIds" parameterType="String">
        delete from sy_repay_plan where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>