package com.ruoyi.partner_info.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.partner_info.mapper.PartnerInfoMapper;
import com.ruoyi.partner_info.domain.PartnerInfo;
import com.ruoyi.partner_info.service.IPartnerInfoService;

/**
 * 资金方管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
@Service
public class PartnerInfoServiceImpl implements IPartnerInfoService 
{
    @Autowired
    private PartnerInfoMapper partnerInfoMapper;

    /**
     * 查询资金方管理
     * 
     * @param id 资金方管理主键
     * @return 资金方管理
     */
    @Override
    public PartnerInfo selectPartnerInfoById(String id)
    {
        return partnerInfoMapper.selectPartnerInfoById(id);
    }

    /**
     * 查询资金方管理列表
     * 
     * @param partnerInfo 资金方管理
     * @return 资金方管理
     */
    @Override
    public List<PartnerInfo> selectPartnerInfoList(PartnerInfo partnerInfo)
    {
        return partnerInfoMapper.selectPartnerInfoList(partnerInfo);
    }

    /**
     * 新增资金方管理
     * 
     * @param partnerInfo 资金方管理
     * @return 结果
     */
    @Override
    public int insertPartnerInfo(PartnerInfo partnerInfo)
    {
        return partnerInfoMapper.insertPartnerInfo(partnerInfo);
    }

    /**
     * 修改资金方管理
     * 
     * @param partnerInfo 资金方管理
     * @return 结果
     */
    @Override
    public int updatePartnerInfo(PartnerInfo partnerInfo)
    {
        return partnerInfoMapper.updatePartnerInfo(partnerInfo);
    }

    /**
     * 批量删除资金方管理
     * 
     * @param ids 需要删除的资金方管理主键
     * @return 结果
     */
    @Override
    public int deletePartnerInfoByIds(String[] ids)
    {
        return partnerInfoMapper.deletePartnerInfoByIds(ids);
    }

    /**
     * 删除资金方管理信息
     * 
     * @param id 资金方管理主键
     * @return 结果
     */
    @Override
    public int deletePartnerInfoById(String id)
    {
        return partnerInfoMapper.deletePartnerInfoById(id);
    }

    @Override
    public List<java.util.Map<String, Object>> selectPartnerInfoIdAndNameList() {
        return partnerInfoMapper.selectPartnerInfoIdAndNameList();
    }
}
