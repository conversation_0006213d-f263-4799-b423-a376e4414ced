package com.ruoyi.hr_repay_plan.service;

import java.util.List;
import com.ruoyi.hr_repay_plan.domain.HrRepayPlan;

/**
 * 华瑞还款计划Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface IHrRepayPlanService 
{
    /**
     * 查询华瑞还款计划
     * 
     * @param id 华瑞还款计划主键
     * @return 华瑞还款计划
     */
    public HrRepayPlan selectHrRepayPlanById(String id);

    /**
     * 查询华瑞还款计划列表
     * 
     * @param hrRepayPlan 华瑞还款计划
     * @return 华瑞还款计划集合
     */
    public List<HrRepayPlan> selectHrRepayPlanList(HrRepayPlan hrRepayPlan);

    /**
     * 新增华瑞还款计划
     * 
     * @param hrRepayPlan 华瑞还款计划
     * @return 结果
     */
    public int insertHrRepayPlan(HrRepayPlan hrRepayPlan);

    /**
     * 修改华瑞还款计划
     * 
     * @param hrRepayPlan 华瑞还款计划
     * @return 结果
     */
    public int updateHrRepayPlan(HrRepayPlan hrRepayPlan);

    /**
     * 批量删除华瑞还款计划
     * 
     * @param ids 需要删除的华瑞还款计划主键集合
     * @return 结果
     */
    public int deleteHrRepayPlanByIds(String[] ids);

    /**
     * 删除华瑞还款计划信息
     * 
     * @param id 华瑞还款计划主键
     * @return 结果
     */
    public int deleteHrRepayPlanById(String id);
}
