package com.ruoyi.lh_repay_plan.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lh_repay_plan.domain.LhRepayPlan;
import com.ruoyi.lh_repay_plan.service.ILhRepayPlanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 蓝海还款计划Controller
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/lh_repay_plan/lh_repay_plan")
public class LhRepayPlanController extends BaseController
{
    @Autowired
    private ILhRepayPlanService lhRepayPlanService;

    /**
     * 查询蓝海还款计划列表
     */
    @PreAuthorize("@ss.hasPermi('lh_repay_plan:lh_repay_plan:list')")
    @GetMapping("/list")
    public TableDataInfo list(LhRepayPlan lhRepayPlan)
    {
        startPage();
        List<LhRepayPlan> list = lhRepayPlanService.selectLhRepayPlanList(lhRepayPlan);
        return getDataTable(list);
    }

    /**
     * 导出蓝海还款计划列表
     */
    @PreAuthorize("@ss.hasPermi('lh_repay_plan:lh_repay_plan:export')")
    @Log(title = "蓝海还款计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LhRepayPlan lhRepayPlan)
    {
        List<LhRepayPlan> list = lhRepayPlanService.selectLhRepayPlanList(lhRepayPlan);
        ExcelUtil<LhRepayPlan> util = new ExcelUtil<LhRepayPlan>(LhRepayPlan.class);
        util.exportExcel(response, list, "蓝海还款计划数据");
    }

    /**
     * 获取蓝海还款计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('lh_repay_plan:lh_repay_plan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(lhRepayPlanService.selectLhRepayPlanById(id));
    }

    /**
     * 新增蓝海还款计划
     */
    @PreAuthorize("@ss.hasPermi('lh_repay_plan:lh_repay_plan:add')")
    @Log(title = "蓝海还款计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LhRepayPlan lhRepayPlan)
    {
        return toAjax(lhRepayPlanService.insertLhRepayPlan(lhRepayPlan));
    }

    /**
     * 修改蓝海还款计划
     */
    @PreAuthorize("@ss.hasPermi('lh_repay_plan:lh_repay_plan:edit')")
    @Log(title = "蓝海还款计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LhRepayPlan lhRepayPlan)
    {
        return toAjax(lhRepayPlanService.updateLhRepayPlan(lhRepayPlan));
    }

    /**
     * 删除蓝海还款计划
     */
    @PreAuthorize("@ss.hasPermi('lh_repay_plan:lh_repay_plan:remove')")
    @Log(title = "蓝海还款计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(lhRepayPlanService.deleteLhRepayPlanByIds(ids));
    }
}
