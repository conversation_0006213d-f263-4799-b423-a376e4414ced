package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 中关村还款试算对象 zgc_trial_repay
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
public class ZgcTrialRepay extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyId;

    /** 状态: 0-待发起 1-已发起 */
    @Excel(name = "状态: 0-待发起 1-已发起")
    private String repayStatus;

    /** 提前还款总金额 */
    @Excel(name = "提前还款总金额")
    private BigDecimal preTotalAmt;

    /** 提前还款本金 */
    @Excel(name = "提前还款本金")
    private BigDecimal prePrincipalAmt;

    /** 提前还款利息 */
    @Excel(name = "提前还款利息")
    private BigDecimal preInterestAmt;

    /** 实还本金 */
    @Excel(name = "实还本金")
    private BigDecimal actualPrincipalAmt;

    /** 实还利息 */
    @Excel(name = "实还利息")
    private BigDecimal actualInterestAmt;

    /** 实还罚息 */
    @Excel(name = "实还罚息")
    private BigDecimal actualPrincipalPenaltyAmt;

    /** 实还复利 */
    @Excel(name = "实还复利")
    private BigDecimal actualInterestPenaltyAmt;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 还款流水号 */
    @Excel(name = "还款流水号")
    private String repaySerialNo;

    /** 还款状态:0-处理中 1-成功 2-失败 */
    @Excel(name = "还款状态:0-处理中 1-成功 2-失败")
    private String transStatus;

    /** 还款日期 */
    @Excel(name = "还款日期")
    private String transDate;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setApplyId(String applyId) 
    {
        this.applyId = applyId;
    }

    public String getApplyId() 
    {
        return applyId;
    }

    public void setRepayStatus(String repayStatus) 
    {
        this.repayStatus = repayStatus;
    }

    public String getRepayStatus() 
    {
        return repayStatus;
    }

    public void setPreTotalAmt(BigDecimal preTotalAmt) 
    {
        this.preTotalAmt = preTotalAmt;
    }

    public BigDecimal getPreTotalAmt() 
    {
        return preTotalAmt;
    }

    public void setPrePrincipalAmt(BigDecimal prePrincipalAmt) 
    {
        this.prePrincipalAmt = prePrincipalAmt;
    }

    public BigDecimal getPrePrincipalAmt() 
    {
        return prePrincipalAmt;
    }

    public void setPreInterestAmt(BigDecimal preInterestAmt) 
    {
        this.preInterestAmt = preInterestAmt;
    }

    public BigDecimal getPreInterestAmt() 
    {
        return preInterestAmt;
    }

    public void setActualPrincipalAmt(BigDecimal actualPrincipalAmt) 
    {
        this.actualPrincipalAmt = actualPrincipalAmt;
    }

    public BigDecimal getActualPrincipalAmt() 
    {
        return actualPrincipalAmt;
    }

    public void setActualInterestAmt(BigDecimal actualInterestAmt) 
    {
        this.actualInterestAmt = actualInterestAmt;
    }

    public BigDecimal getActualInterestAmt() 
    {
        return actualInterestAmt;
    }

    public void setActualPrincipalPenaltyAmt(BigDecimal actualPrincipalPenaltyAmt) 
    {
        this.actualPrincipalPenaltyAmt = actualPrincipalPenaltyAmt;
    }

    public BigDecimal getActualPrincipalPenaltyAmt() 
    {
        return actualPrincipalPenaltyAmt;
    }

    public void setActualInterestPenaltyAmt(BigDecimal actualInterestPenaltyAmt) 
    {
        this.actualInterestPenaltyAmt = actualInterestPenaltyAmt;
    }

    public BigDecimal getActualInterestPenaltyAmt() 
    {
        return actualInterestPenaltyAmt;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setRepaySerialNo(String repaySerialNo) 
    {
        this.repaySerialNo = repaySerialNo;
    }

    public String getRepaySerialNo() 
    {
        return repaySerialNo;
    }

    public void setTransStatus(String transStatus) 
    {
        this.transStatus = transStatus;
    }

    public String getTransStatus() 
    {
        return transStatus;
    }

    public void setTransDate(String transDate) 
    {
        this.transDate = transDate;
    }

    public String getTransDate() 
    {
        return transDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("applyId", getApplyId())
            .append("repayStatus", getRepayStatus())
            .append("preTotalAmt", getPreTotalAmt())
            .append("prePrincipalAmt", getPrePrincipalAmt())
            .append("preInterestAmt", getPreInterestAmt())
            .append("actualPrincipalAmt", getActualPrincipalAmt())
            .append("actualInterestAmt", getActualInterestAmt())
            .append("actualPrincipalPenaltyAmt", getActualPrincipalPenaltyAmt())
            .append("actualInterestPenaltyAmt", getActualInterestPenaltyAmt())
            .append("createDate", getCreateDate())
            .append("repaySerialNo", getRepaySerialNo())
            .append("transStatus", getTransStatus())
            .append("transDate", getTransDate())
            .toString();
    }
}
