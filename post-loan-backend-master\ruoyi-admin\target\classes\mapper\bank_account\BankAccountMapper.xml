<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bank_account.mapper.BankAccountMapper">
    
    <resultMap type="BankAccount" id="BankAccountResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="card"    column="card"    />
        <result property="isOff"    column="isOff"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectBankAccountVo">
        select id, name, card, isOff, create_by, create_date, update_by, update_date, del_flag from bank_account
    </sql>

    <select id="selectBankAccountList" parameterType="BankAccount" resultMap="BankAccountResult">
        <include refid="selectBankAccountVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="card != null  and card != ''"> and card = #{card}</if>
            <if test="isOff != null "> and isOff = #{isOff}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
    </select>
    
    <select id="selectBankAccountById" parameterType="String" resultMap="BankAccountResult">
        <include refid="selectBankAccountVo"/>
        where id = #{id}
    </select>

    <insert id="insertBankAccount" parameterType="BankAccount" useGeneratedKeys="true" keyProperty="id">
        insert into bank_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="card != null">card,</if>
            <if test="isOff != null">isOff,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="card != null">#{card},</if>
            <if test="isOff != null">#{isOff},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateBankAccount" parameterType="BankAccount">
        update bank_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="card != null">card = #{card},</if>
            <if test="isOff != null">isOff = #{isOff},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBankAccountById" parameterType="String">
        delete from bank_account where id = #{id}
    </delete>

    <delete id="deleteBankAccountByIds" parameterType="String">
        delete from bank_account where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>