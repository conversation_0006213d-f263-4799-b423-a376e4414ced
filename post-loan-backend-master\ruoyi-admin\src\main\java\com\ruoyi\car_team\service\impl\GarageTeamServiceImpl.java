package com.ruoyi.car_team.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.car_team.mapper.GarageTeamMapper;
import com.ruoyi.car_team.domain.GarageTeam;
import com.ruoyi.car_team.service.IGarageTeamService;

/**
 * 团队管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
public class GarageTeamServiceImpl implements IGarageTeamService 
{
    @Autowired
    private GarageTeamMapper garageTeamMapper;

    /**
     * 查询团队管理
     * 
     * @param id 团队管理主键
     * @return 团队管理
     */
    @Override
    public GarageTeam selectGarageTeamById(Long id)
    {
        return garageTeamMapper.selectGarageTeamById(id);
    }

    /**
     * 查询团队管理列表
     * 
     * @param garageTeam 团队管理
     * @return 团队管理
     */
    @Override
    public List<GarageTeam> selectGarageTeamList(GarageTeam garageTeam)
    {
        return garageTeamMapper.selectGarageTeamList(garageTeam);
    }

    /**
     * 新增团队管理
     * 
     * @param garageTeam 团队管理
     * @return 结果
     */
    @Override
    public int insertGarageTeam(GarageTeam garageTeam)
    {
        return garageTeamMapper.insertGarageTeam(garageTeam);
    }

    /**
     * 修改团队管理
     * 
     * @param garageTeam 团队管理
     * @return 结果
     */
    @Override
    public int updateGarageTeam(GarageTeam garageTeam)
    {
        return garageTeamMapper.updateGarageTeam(garageTeam);
    }

    /**
     * 批量删除团队管理
     * 
     * @param ids 需要删除的团队管理主键
     * @return 结果
     */
    @Override
    public int deleteGarageTeamByIds(Long[] ids)
    {
        return garageTeamMapper.deleteGarageTeamByIds(ids);
    }

    /**
     * 删除团队管理信息
     * 
     * @param id 团队管理主键
     * @return 结果
     */
    @Override
    public int deleteGarageTeamById(Long id)
    {
        return garageTeamMapper.deleteGarageTeamById(id);
    }
}
