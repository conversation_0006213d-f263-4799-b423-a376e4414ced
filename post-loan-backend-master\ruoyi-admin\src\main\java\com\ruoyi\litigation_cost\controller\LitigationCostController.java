package com.ruoyi.litigation_cost.controller;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.litigation_cost.domain.LitigationCost;
import com.ruoyi.litigation_cost.service.ILitigationCostService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 法诉费用明细Controller
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
@RestController
@RequestMapping("/litigation_cost/litigation_cost")
public class LitigationCostController extends BaseController
{
    @Autowired
    private ILitigationCostService litigationCostService;

    /**
     * 查询法诉费用明细列表
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost:litigation_cost:list')")
    @GetMapping("/list")
    public TableDataInfo list(LitigationCost litigationCost)
    {
        startPage();
        List<LitigationCost> list = litigationCostService.selectLitigationCostList(litigationCost);
        return getDataTable(list);
    }

    /**
     * 导出法诉费用明细列表
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost:litigation_cost:export')")
    @Log(title = "法诉费用明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LitigationCost litigationCost)
    {
        List<LitigationCost> list = litigationCostService.selectLitigationCostList(litigationCost);
        ExcelUtil<LitigationCost> util = new ExcelUtil<LitigationCost>(LitigationCost.class);
        util.exportExcel(response, list, "法诉费用明细数据");
    }

    /**
     * 获取法诉费用明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost:litigation_cost:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(litigationCostService.selectLitigationCostById(id));
    }

    /**
     * 新增法诉费用明细
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost:litigation_cost:add')")
    @Log(title = "法诉费用明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LitigationCost litigationCost)
    {
        return toAjax(litigationCostService.insertLitigationCost(litigationCost));
    }

    /**
     * 修改法诉费用明细
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost:litigation_cost:edit')")
    @Log(title = "法诉费用明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LitigationCost litigationCost)
    {
        return toAjax(litigationCostService.updateLitigationCost(litigationCost));
    }

    /**
     * 删除法诉费用明细
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost:litigation_cost:remove')")
    @Log(title = "法诉费用明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(litigationCostService.deleteLitigationCostByIds(ids));
    }

    /**
     * 检查已提交的限制性费用类型（判决金额和利息）
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost:litigation_cost:query')")
    @GetMapping("/checkLimitedFees/{litigationCaseId}")
    public AjaxResult checkLimitedFees(@PathVariable("litigationCaseId") Long litigationCaseId)
    {
        List<String> submittedTypes = litigationCostService.getSubmittedLimitedFeeTypes(litigationCaseId);
        return success(submittedTypes);
    }

    /**
     * 获取法诉费用汇总数据
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost:litigation_cost:query')")
    @PostMapping("/summary")
    public AjaxResult getCostSummary(@RequestBody Map<String, Object> params)
    {
        try {
            @SuppressWarnings("unchecked")
            List<Object> caseIdObjects = (List<Object>) params.get("caseIds");

            // 将Object转换为Long
            List<Long> caseIds = new ArrayList<>();
            for (Object obj : caseIdObjects) {
                if (obj instanceof Integer) {
                    caseIds.add(((Integer) obj).longValue());
                } else if (obj instanceof Long) {
                    caseIds.add((Long) obj);
                } else if (obj instanceof String) {
                    caseIds.add(Long.parseLong((String) obj));
                }
            }

            Map<Long, Map<String, Object>> summary = litigationCostService.getCostSummaryByCaseIds(caseIds);
            return success(summary);
        } catch (Exception e) {
            return error("获取费用汇总数据失败: " + e.getMessage());
        }
    }
}
