<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.litigation_case.mapper.LitigationCaseMapper">
    
    <resultMap type="LitigationCase" id="LitigationCaseResult">
        <result property="id"    column="id"    />
        <result property="litigationClerk"    column="litigation_clerk"    />
        <result property="litigationStartDate"    column="litigation_start_date"    />
        <result property="litigationStartDay"    column="litigation_start_day"    />
        <result property="statusUpdateDate"    column="status_update_date"    />
        <result property="litigationStatus"    column="litigation_status"    />
        <result property="litigationSubStatus"    column="litigation_sub_status"    />
        <result property="courtJurisdiction"    column="court_jurisdiction"    />
        <result property="lawsuitCourt"    column="lawsuit_court"    />
        <result property="preLitigationNo"    column="pre_litigation_no"    />
        <result property="preLitigationTime"    column="pre_litigation_time"    />
        <result property="civilCaseNo"    column="civil_case_no"    />
        <result property="civilCaseTime"    column="civil_case_time"    />
        <result property="courtHearingTime"    column="court_hearing_time"    />
        <result property="enforcementNo"    column="enforcement_no"    />
        <result property="enforcementPreservationNo"    column="enforcement_preservation_no"    />
        <result property="repaymentStatus"    column="repayment_status"    />
        <result property="loanId"    column="loan_id"    />
        <result property="urgeUser"    column="urge_user"    />
        <result property="bankAmount"    column="bank_amount"    />
        <result property="bankAmountBack"    column="bank_amount_back"    />
        <result property="bankCompensation"    column="bank_compensation"    />
        <result property="withholdingAmount"    column="withholding_amount"    />
        <result property="withholdingAmountBack"    column="withholding_amount_back"    />
        <result property="withholdingCompensation"    column="withholding_compensation"    />
        <result property="liquidatedAmount"    column="liquidated_amount"    />
        <result property="liquidatedAmountBack"    column="liquidated_amount_back"    />
        <result property="liquidatedCompensation"    column="liquidated_compensation"    />
        <result property="otherAmount"    column="other_amount"    />
        <result property="otherAmountBack"    column="other_amount_back"    />
        <result property="otherCompensation"    column="other_compensation"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="totalCompensation"    column="total_compensation"    />
        <result property="totalAmountBack"    column="total_amount_back"    />
        <result property="litigationType"    column="litigation_type"    />
        <result property="prosecutionType"    column="prosecution_type"    />
        <result property="prosecutionContent"    column="prosecution_content"    />
        <result property="prosecutionAmount"    column="prosecution_amount"    />
    </resultMap>

    <sql id="selectLitigationCaseVo">
        select * from litigation_case
    </sql>

    <select id="selectLitigationCaseList" parameterType="LitigationCase" resultMap="LitigationCaseResult">
        <include refid="selectLitigationCaseVo"/>
        <where>  
            <if test="litigationClerk != null  and litigationClerk != ''"> and litigation_clerk = #{litigationClerk}</if>
            <if test="litigationStartDate != null "> and litigation_start_date = #{litigationStartDate}</if>
            <if test="litigationStartDay != null "> and litigation_start_day = #{litigationStartDay}</if>
            <if test="statusUpdateDate != null "> and status_update_date = #{statusUpdateDate}</if>
            <if test="litigationStatus != null  and litigationStatus != ''"> and litigation_status = #{litigationStatus}</if>
            <if test="litigationSubStatus != null  and litigationSubStatus != ''"> and litigation_sub_status = #{litigationSubStatus}</if>
            <if test="courtJurisdiction != null  and courtJurisdiction != ''"> and court_jurisdiction = #{courtJurisdiction}</if>
            <if test="lawsuitCourt != null  and lawsuitCourt != ''"> and lawsuit_court = #{lawsuitCourt}</if>
            <if test="preLitigationNo != null  and preLitigationNo != ''"> and pre_litigation_no = #{preLitigationNo}</if>
            <if test="preLitigationTime != null "> and pre_litigation_time = #{preLitigationTime}</if>
            <if test="civilCaseNo != null  and civilCaseNo != ''"> and civil_case_no = #{civilCaseNo}</if>
            <if test="civilCaseTime != null "> and civil_case_time = #{civilCaseTime}</if>
            <if test="courtHearingTime != null "> and court_hearing_time = #{courtHearingTime}</if>
            <if test="enforcementNo != null  and enforcementNo != ''"> and enforcement_no = #{enforcementNo}</if>
            <if test="enforcementPreservationNo != null  and enforcementPreservationNo != ''"> and enforcement_preservation_no = #{enforcementPreservationNo}</if>
            <if test="repaymentStatus != null  and repaymentStatus != ''"> and repayment_status = #{repaymentStatus}</if>
            <if test="loanId != null "> and loan_id = #{loanId}</if>
            <if test="urgeUser != null  and urgeUser != ''"> and urge_user = #{urgeUser}</if>
            <if test="bankAmount != null "> and bank_amount = #{bankAmount}</if>
            <if test="bankAmountBack != null "> and bank_amount_back = #{bankAmountBack}</if>
            <if test="bankCompensation != null "> and bank_compensation = #{bankCompensation}</if>
            <if test="withholdingAmount != null "> and withholding_amount = #{withholdingAmount}</if>
            <if test="withholdingAmountBack != null "> and withholding_amount_back = #{withholdingAmountBack}</if>
            <if test="withholdingCompensation != null "> and withholding_compensation = #{withholdingCompensation}</if>
            <if test="liquidatedAmount != null "> and liquidated_amount = #{liquidatedAmount}</if>
            <if test="liquidatedAmountBack != null "> and liquidated_amount_back = #{liquidatedAmountBack}</if>
            <if test="liquidatedCompensation != null "> and liquidated_compensation = #{liquidatedCompensation}</if>
            <if test="otherAmount != null "> and other_amount = #{otherAmount}</if>
            <if test="otherAmountBack != null "> and other_amount_back = #{otherAmountBack}</if>
            <if test="otherCompensation != null "> and other_compensation = #{otherCompensation}</if>
            <if test="totalAmount != null "> and total_amount = #{totalAmount}</if>
            <if test="totalCompensation != null "> and total_compensation = #{totalCompensation}</if>
            <if test="totalAmountBack != null "> and total_amount_back = #{totalAmountBack}</if>
            <if test="litigationType != null "> and litigation_type = #{litigationType}</if>
            <if test="prosecutionType != null  and prosecutionType != ''"> and prosecution_type = #{prosecutionType}</if>
            <if test="prosecutionContent != null  and prosecutionContent != ''"> and prosecution_content = #{prosecutionContent}</if>
            <if test="prosecutionAmount != null "> and prosecution_amount = #{prosecutionAmount}</if>
        </where>
    </select>
    
    <select id="selectLitigationCaseById" parameterType="Long" resultMap="LitigationCaseResult">
        <include refid="selectLitigationCaseVo"/>
        where id = #{id}
    </select>

    <insert id="insertLitigationCase" parameterType="LitigationCase" useGeneratedKeys="true" keyProperty="id">
        insert into litigation_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="litigationClerk != null">litigation_clerk,</if>
            <if test="litigationStartDate != null">litigation_start_date,</if>
            <if test="litigationStartDay != null">litigation_start_day,</if>
            <if test="statusUpdateDate != null">status_update_date,</if>
            <if test="litigationStatus != null">litigation_status,</if>
            <if test="litigationSubStatus != null">litigation_sub_status,</if>
            <if test="courtJurisdiction != null">court_jurisdiction,</if>
            <if test="lawsuitCourt != null">lawsuit_court,</if>
            <if test="preLitigationNo != null">pre_litigation_no,</if>
            <if test="preLitigationTime != null">pre_litigation_time,</if>
            <if test="civilCaseNo != null">civil_case_no,</if>
            <if test="civilCaseTime != null">civil_case_time,</if>
            <if test="courtHearingTime != null">court_hearing_time,</if>
            <if test="enforcementNo != null">enforcement_no,</if>
            <if test="enforcementPreservationNo != null">enforcement_preservation_no,</if>
            <if test="repaymentStatus != null">repayment_status,</if>
            <if test="loanId != null">loan_id,</if>
            <if test="urgeUser != null">urge_user,</if>
            <if test="bankAmount != null">bank_amount,</if>
            <if test="bankAmountBack != null">bank_amount_back,</if>
            <if test="bankCompensation != null">bank_compensation,</if>
            <if test="withholdingAmount != null">withholding_amount,</if>
            <if test="withholdingAmountBack != null">withholding_amount_back,</if>
            <if test="withholdingCompensation != null">withholding_compensation,</if>
            <if test="liquidatedAmount != null">liquidated_amount,</if>
            <if test="liquidatedAmountBack != null">liquidated_amount_back,</if>
            <if test="liquidatedCompensation != null">liquidated_compensation,</if>
            <if test="otherAmount != null">other_amount,</if>
            <if test="otherAmountBack != null">other_amount_back,</if>
            <if test="otherCompensation != null">other_compensation,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="totalCompensation != null">total_compensation,</if>
            <if test="totalAmountBack != null">total_amount_back,</if>
            <if test="litigationType != null">litigation_type,</if>
            <if test="prosecutionType != null">prosecution_type,</if>
            <if test="prosecutionContent != null">prosecution_content,</if>
            <if test="prosecutionAmount != null">prosecution_amount,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="litigationClerk != null">#{litigationClerk},</if>
            <if test="litigationStartDate != null">#{litigationStartDate},</if>
            <if test="litigationStartDay != null">#{litigationStartDay},</if>
            <if test="statusUpdateDate != null">#{statusUpdateDate},</if>
            <if test="litigationStatus != null">#{litigationStatus},</if>
            <if test="litigationSubStatus != null">#{litigationSubStatus},</if>
            <if test="courtJurisdiction != null">#{courtJurisdiction},</if>
            <if test="lawsuitCourt != null">#{lawsuitCourt},</if>
            <if test="preLitigationNo != null">#{preLitigationNo},</if>
            <if test="preLitigationTime != null">#{preLitigationTime},</if>
            <if test="civilCaseNo != null">#{civilCaseNo},</if>
            <if test="civilCaseTime != null">#{civilCaseTime},</if>
            <if test="courtHearingTime != null">#{courtHearingTime},</if>
            <if test="enforcementNo != null">#{enforcementNo},</if>
            <if test="enforcementPreservationNo != null">#{enforcementPreservationNo},</if>
            <if test="repaymentStatus != null">#{repaymentStatus},</if>
            <if test="loanId != null">#{loanId},</if>
            <if test="urgeUser != null">#{urgeUser},</if>
            <if test="bankAmount != null">#{bankAmount},</if>
            <if test="bankAmountBack != null">#{bankAmountBack},</if>
            <if test="bankCompensation != null">#{bankCompensation},</if>
            <if test="withholdingAmount != null">#{withholdingAmount},</if>
            <if test="withholdingAmountBack != null">#{withholdingAmountBack},</if>
            <if test="withholdingCompensation != null">#{withholdingCompensation},</if>
            <if test="liquidatedAmount != null">#{liquidatedAmount},</if>
            <if test="liquidatedAmountBack != null">#{liquidatedAmountBack},</if>
            <if test="liquidatedCompensation != null">#{liquidatedCompensation},</if>
            <if test="otherAmount != null">#{otherAmount},</if>
            <if test="otherAmountBack != null">#{otherAmountBack},</if>
            <if test="otherCompensation != null">#{otherCompensation},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="totalCompensation != null">#{totalCompensation},</if>
            <if test="totalAmountBack != null">#{totalAmountBack},</if>
            <if test="litigationType != null">#{litigationType},</if>
            <if test="prosecutionType != null">#{prosecutionType},</if>
            <if test="prosecutionContent != null">#{prosecutionContent},</if>
            <if test="prosecutionAmount != null">#{prosecutionAmount},</if>
         </trim>
    </insert>

    <update id="updateLitigationCase" parameterType="LitigationCase">
        update litigation_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="litigationClerk != null">litigation_clerk = #{litigationClerk},</if>
            <if test="litigationStartDate != null">litigation_start_date = #{litigationStartDate},</if>
            <if test="litigationStartDay != null">litigation_start_day = #{litigationStartDay},</if>
            <if test="statusUpdateDate != null">status_update_date = #{statusUpdateDate},</if>
            <if test="litigationStatus != null">litigation_status = #{litigationStatus},</if>
            <if test="litigationSubStatus != null">litigation_sub_status = #{litigationSubStatus},</if>
            <if test="courtJurisdiction != null">court_jurisdiction = #{courtJurisdiction},</if>
            <if test="lawsuitCourt != null">lawsuit_court = #{lawsuitCourt},</if>
            <if test="preLitigationNo != null">pre_litigation_no = #{preLitigationNo},</if>
            <if test="preLitigationTime != null">pre_litigation_time = #{preLitigationTime},</if>
            <if test="civilCaseNo != null">civil_case_no = #{civilCaseNo},</if>
            <if test="civilCaseTime != null">civil_case_time = #{civilCaseTime},</if>
            <if test="courtHearingTime != null">court_hearing_time = #{courtHearingTime},</if>
            <if test="enforcementNo != null">enforcement_no = #{enforcementNo},</if>
            <if test="enforcementPreservationNo != null">enforcement_preservation_no = #{enforcementPreservationNo},</if>
            <if test="repaymentStatus != null">repayment_status = #{repaymentStatus},</if>
            <if test="loanId != null">loan_id = #{loanId},</if>
            <if test="urgeUser != null">urge_user = #{urgeUser},</if>
            <if test="bankAmount != null">bank_amount = #{bankAmount},</if>
            <if test="bankAmountBack != null">bank_amount_back = #{bankAmountBack},</if>
            <if test="bankCompensation != null">bank_compensation = #{bankCompensation},</if>
            <if test="withholdingAmount != null">withholding_amount = #{withholdingAmount},</if>
            <if test="withholdingAmountBack != null">withholding_amount_back = #{withholdingAmountBack},</if>
            <if test="withholdingCompensation != null">withholding_compensation = #{withholdingCompensation},</if>
            <if test="liquidatedAmount != null">liquidated_amount = #{liquidatedAmount},</if>
            <if test="liquidatedAmountBack != null">liquidated_amount_back = #{liquidatedAmountBack},</if>
            <if test="liquidatedCompensation != null">liquidated_compensation = #{liquidatedCompensation},</if>
            <if test="otherAmount != null">other_amount = #{otherAmount},</if>
            <if test="otherAmountBack != null">other_amount_back = #{otherAmountBack},</if>
            <if test="otherCompensation != null">other_compensation = #{otherCompensation},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="totalCompensation != null">total_compensation = #{totalCompensation},</if>
            <if test="totalAmountBack != null">total_amount_back = #{totalAmountBack},</if>
            <if test="litigationType != null">litigation_type = #{litigationType},</if>
            <if test="prosecutionType != null">prosecution_type = #{prosecutionType},</if>
            <if test="prosecutionContent != null">prosecution_content = #{prosecutionContent},</if>
            <if test="prosecutionAmount != null">prosecution_amount = #{prosecutionAmount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLitigationCaseById" parameterType="Long">
        delete from litigation_case where id = #{id}
    </delete>

    <delete id="deleteLitigationCaseByIds" parameterType="String">
        delete from litigation_case where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>