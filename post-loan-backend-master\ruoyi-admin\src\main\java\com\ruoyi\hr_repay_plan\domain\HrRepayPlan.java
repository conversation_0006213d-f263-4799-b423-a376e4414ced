package com.ruoyi.hr_repay_plan.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 华瑞还款计划对象 hr_repay_plan
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public class HrRepayPlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyId;

    /** 分期序号 */
    @Excel(name = "分期序号")
    private String period;

    /** 应还款日 */
    @Excel(name = "应还款日")
    private String repayDate;

    /** 实际还款日 */
    @Excel(name = "实际还款日")
    private String actualRepayDate;

    /** 总月供 */
    @Excel(name = "总月供")
    private String repayAmount;

    /** 华瑞月供 */
    @Excel(name = "华瑞月供")
    private String hrRepayAmount;

    /** 应还本金 */
    @Excel(name = "应还本金")
    private String capital;

    /** 实还本金 */
    @Excel(name = "实还本金")
    private String actualCapital;

    /** 应还利息 */
    @Excel(name = "应还利息")
    private String interest;

    /** 实还利息 */
    @Excel(name = "实还利息")
    private String actualInterest;

    /** 应还罚息 */
    @Excel(name = "应还罚息")
    private String defInterest;

    /** 实还罚息 */
    @Excel(name = "实还罚息")
    private String actualDefInterest;

    /** 担保费 */
    @Excel(name = "担保费")
    private String guaranteeFee;

    /** 本金余额 */
    @Excel(name = "本金余额")
    private String balance;

    /** 逾期天数 */
    @Excel(name = "逾期天数")
    private String overdueDays;

    /** 状态:1-待还款 2-部分还款 3-已还款 4-逾期 5-逾期部分还款 6-逾期已还款 7-已结清 8-已冲正 9-代偿 */
    @Excel(name = "状态:1-待还款 2-部分还款 3-已还款 4-逾期 5-逾期部分还款 6-逾期已还款 7-已结清 8-已冲正 9-代偿")
    private String status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setApplyId(String applyId) 
    {
        this.applyId = applyId;
    }

    public String getApplyId() 
    {
        return applyId;
    }

    public void setPeriod(String period) 
    {
        this.period = period;
    }

    public String getPeriod() 
    {
        return period;
    }

    public void setRepayDate(String repayDate) 
    {
        this.repayDate = repayDate;
    }

    public String getRepayDate() 
    {
        return repayDate;
    }

    public void setActualRepayDate(String actualRepayDate) 
    {
        this.actualRepayDate = actualRepayDate;
    }

    public String getActualRepayDate() 
    {
        return actualRepayDate;
    }

    public void setRepayAmount(String repayAmount) 
    {
        this.repayAmount = repayAmount;
    }

    public String getRepayAmount() 
    {
        return repayAmount;
    }

    public void setHrRepayAmount(String hrRepayAmount) 
    {
        this.hrRepayAmount = hrRepayAmount;
    }

    public String getHrRepayAmount() 
    {
        return hrRepayAmount;
    }

    public void setCapital(String capital) 
    {
        this.capital = capital;
    }

    public String getCapital() 
    {
        return capital;
    }

    public void setActualCapital(String actualCapital) 
    {
        this.actualCapital = actualCapital;
    }

    public String getActualCapital() 
    {
        return actualCapital;
    }

    public void setInterest(String interest) 
    {
        this.interest = interest;
    }

    public String getInterest() 
    {
        return interest;
    }

    public void setActualInterest(String actualInterest) 
    {
        this.actualInterest = actualInterest;
    }

    public String getActualInterest() 
    {
        return actualInterest;
    }

    public void setDefInterest(String defInterest) 
    {
        this.defInterest = defInterest;
    }

    public String getDefInterest() 
    {
        return defInterest;
    }

    public void setActualDefInterest(String actualDefInterest) 
    {
        this.actualDefInterest = actualDefInterest;
    }

    public String getActualDefInterest() 
    {
        return actualDefInterest;
    }

    public void setGuaranteeFee(String guaranteeFee) 
    {
        this.guaranteeFee = guaranteeFee;
    }

    public String getGuaranteeFee() 
    {
        return guaranteeFee;
    }

    public void setBalance(String balance) 
    {
        this.balance = balance;
    }

    public String getBalance() 
    {
        return balance;
    }

    public void setOverdueDays(String overdueDays) 
    {
        this.overdueDays = overdueDays;
    }

    public String getOverdueDays() 
    {
        return overdueDays;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("applyId", getApplyId())
            .append("period", getPeriod())
            .append("repayDate", getRepayDate())
            .append("actualRepayDate", getActualRepayDate())
            .append("repayAmount", getRepayAmount())
            .append("hrRepayAmount", getHrRepayAmount())
            .append("capital", getCapital())
            .append("actualCapital", getActualCapital())
            .append("interest", getInterest())
            .append("actualInterest", getActualInterest())
            .append("defInterest", getDefInterest())
            .append("actualDefInterest", getActualDefInterest())
            .append("guaranteeFee", getGuaranteeFee())
            .append("balance", getBalance())
            .append("overdueDays", getOverdueDays())
            .append("status", getStatus())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .toString();
    }
}
