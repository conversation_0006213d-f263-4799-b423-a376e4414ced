<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.customer_info.mapper.CustomerInfoMapper">
    
    <resultMap type="CustomerInfo" id="CustomerInfoResult">
        <result property="id"    column="id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="sex"    column="sex"    />
        <result property="age"    column="age"    />
        <result property="birthday"    column="birthday"    />
        <result property="nation"    column="nation"    />
        <result property="certId"    column="cert_id"    />
        <result property="certType"    column="cert_type"    />
        <result property="creditissueOrg"    column="creditIssue_org"    />
        <result property="address"    column="address"    />
        <result property="effecteddate"    column="effecteddate"    />
        <result property="expireddate"    column="expireddate"    />
        <result property="customerType"    column="customer_type"    />
        <result property="personalType"    column="personal_type"    />
        <result property="customerStatus"    column="customer_status"    />
        <result property="validStatus"    column="valid_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectCustomerInfoVo">
        select id, customer_name, sex, age, birthday, nation, cert_id, cert_type, creditIssue_org, address, effecteddate, expireddate, customer_type, personal_type, customer_status, valid_status, create_by, create_date, update_by, update_date, del_flag from customer_info
    </sql>

    <select id="selectCustomerInfoList" parameterType="CustomerInfo" resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        <where>  
            <if test="id != null  and id != ''"> and id = #{id}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
        </where>
    </select>
    
    <select id="selectCustomerInfoById" parameterType="String" resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertCustomerInfo" parameterType="CustomerInfo">
        insert into customer_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="sex != null">sex,</if>
            <if test="age != null">age,</if>
            <if test="birthday != null">birthday,</if>
            <if test="nation != null">nation,</if>
            <if test="certId != null">cert_id,</if>
            <if test="certType != null">cert_type,</if>
            <if test="creditissueOrg != null">creditIssue_org,</if>
            <if test="address != null">address,</if>
            <if test="effecteddate != null">effecteddate,</if>
            <if test="expireddate != null">expireddate,</if>
            <if test="customerType != null">customer_type,</if>
            <if test="personalType != null">personal_type,</if>
            <if test="customerStatus != null">customer_status,</if>
            <if test="validStatus != null">valid_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="sex != null">#{sex},</if>
            <if test="age != null">#{age},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="nation != null">#{nation},</if>
            <if test="certId != null">#{certId},</if>
            <if test="certType != null">#{certType},</if>
            <if test="creditissueOrg != null">#{creditissueOrg},</if>
            <if test="address != null">#{address},</if>
            <if test="effecteddate != null">#{effecteddate},</if>
            <if test="expireddate != null">#{expireddate},</if>
            <if test="customerType != null">#{customerType},</if>
            <if test="personalType != null">#{personalType},</if>
            <if test="customerStatus != null">#{customerStatus},</if>
            <if test="validStatus != null">#{validStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateCustomerInfo" parameterType="CustomerInfo">
        update customer_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="age != null">age = #{age},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="nation != null">nation = #{nation},</if>
            <if test="certId != null">cert_id = #{certId},</if>
            <if test="certType != null">cert_type = #{certType},</if>
            <if test="creditissueOrg != null">creditIssue_org = #{creditissueOrg},</if>
            <if test="address != null">address = #{address},</if>
            <if test="effecteddate != null">effecteddate = #{effecteddate},</if>
            <if test="expireddate != null">expireddate = #{expireddate},</if>
            <if test="customerType != null">customer_type = #{customerType},</if>
            <if test="personalType != null">personal_type = #{personalType},</if>
            <if test="customerStatus != null">customer_status = #{customerStatus},</if>
            <if test="validStatus != null">valid_status = #{validStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerInfoById" parameterType="String">
        delete from customer_info where id = #{id}
    </delete>

    <delete id="deleteCustomerInfoByIds" parameterType="String">
        delete from customer_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


<!--    <resultMap type="VwCustomerInfo" id="VwCustomerInfoResult">-->
<!--        <result property="id" column="id"/>-->
<!--        <result property="customerName" column="customer_name"/>-->
<!--        <result property="sex" column="sex"/>-->
<!--        <result property="age" column="age"/>-->
<!--        <result property="birthday" column="birthday"/>-->
<!--        <result property="nation" column="nation"/>-->
<!--        <result property="certId" column="cert_id"/>-->
<!--        <result property="certType" column="cert_type"/>-->
<!--        <result property="creditissueOrg" column="creditIssue_org"/>-->
<!--        <result property="address" column="address"/>-->
<!--        <result property="effecteddate" column="effecteddate"/>-->
<!--        <result property="expireddate" column="expireddate"/>-->
<!--        <result property="customerType" column="customer_type"/>-->
<!--        <result property="personalType" column="personal_type"/>-->
<!--        <result property="customerStatus" column="customer_status"/>-->
<!--        <result property="validStatus" column="valid_status"/>-->
<!--        <result property="createBy" column="create_by"/>-->
<!--        <result property="createDate" column="create_date"/>-->
<!--        <result property="updateBy" column="update_by"/>-->
<!--        <result property="updateDate" column="update_date"/>-->
<!--        <result property="delFlag" column="del_flag"/>-->
<!--        <result property="docProvince" column="doc_province"/>-->
<!--        <result property="docCity" column="doc_city"/>-->
<!--        <result property="docBorough" column="doc_borough"/>-->
<!--        <result property="docAddress" column="doc_address"/>-->
<!--        <result property="mobileNo" column="mobile_no"/> &lt;!&ndash; 若存在 &ndash;&gt;-->
<!--    </resultMap>-->

<!--    <sql id="selectVwCustomerInfoVo">-->
<!--        select * from vw_customer_info-->
<!--    </sql>-->

<!--    <select id="selectVWCustomerInfoList" parameterType="VwCustomerInfo" resultMap="VwCustomerInfoResult">-->
<!--        <include refid="selectVwCustomerInfoVo"/>-->
<!--        <where>-->
<!--            <if test="id != null and id != ''"> and id = #{id} </if>-->
<!--            <if test="customerName != null and customerName != ''"> and customer_name like concat('%', #{customerName}, '%') </if>-->
<!--            <if test="mobileNo != null and mobileNo != ''"> and mobile_no = #{mobileNo} </if>-->
<!--        </where>-->
<!--    </select>-->
</mapper>