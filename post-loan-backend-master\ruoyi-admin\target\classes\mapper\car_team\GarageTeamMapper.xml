<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.car_team.mapper.GarageTeamMapper">
    
    <resultMap type="GarageTeam" id="GarageTeamResult">
        <result property="id"    column="id"    />
        <result property="teamName"    column="team_name"    />
        <result property="account"    column="account"    />
        <result property="status"    column="status"    />
        <result property="teamType"    column="team_type"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="phone"    column="phone"    />
        <result property="settlementAccount"    column="settlement_account"    />
        <result property="enabledDate"    column="enabled_date"    />
        <result property="createdDate"    column="created_date"    />
        <result property="updatedDate"    column="updated_date"    />
    </resultMap>

    <sql id="selectGarageTeamVo">
        select id, team_name, account, status, team_type, contact_person, phone, settlement_account, enabled_date, created_date, updated_date from garage_team
    </sql>

    <select id="selectGarageTeamList" parameterType="GarageTeam" resultMap="GarageTeamResult">
        <include refid="selectGarageTeamVo"/>
        <where>  
            <if test="teamName != null  and teamName != ''"> and team_name like concat('%', #{teamName}, '%')</if>
            <if test="account != null  and account != ''"> and account = #{account}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="teamType != null "> and team_type = #{teamType}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person = #{contactPerson}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="settlementAccount != null  and settlementAccount != ''"> and settlement_account = #{settlementAccount}</if>
        </where>
    </select>
    
    <select id="selectGarageTeamById" parameterType="Long" resultMap="GarageTeamResult">
        <include refid="selectGarageTeamVo"/>
        where id = #{id}
    </select>

    <insert id="insertGarageTeam" parameterType="GarageTeam" useGeneratedKeys="true" keyProperty="id">
        insert into garage_team
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">team_name,</if>
            <if test="account != null and account != ''">account,</if>
            <if test="status != null">status,</if>
            <if test="teamType != null">team_type,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="phone != null">phone,</if>
            <if test="settlementAccount != null">settlement_account,</if>
            <if test="enabledDate != null">enabled_date,</if>
            <if test="createdDate != null">created_date,</if>
            <if test="updatedDate != null">updated_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">#{teamName},</if>
            <if test="account != null and account != ''">#{account},</if>
            <if test="status != null">#{status},</if>
            <if test="teamType != null">#{teamType},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="phone != null">#{phone},</if>
            <if test="settlementAccount != null">#{settlementAccount},</if>
            <if test="enabledDate != null">#{enabledDate},</if>
            <if test="createdDate != null">#{createdDate},</if>
            <if test="updatedDate != null">#{updatedDate},</if>
         </trim>
    </insert>

    <update id="updateGarageTeam" parameterType="GarageTeam">
        update garage_team
        <trim prefix="SET" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">team_name = #{teamName},</if>
            <if test="account != null and account != ''">account = #{account},</if>
            <if test="status != null">status = #{status},</if>
            <if test="teamType != null">team_type = #{teamType},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="settlementAccount != null">settlement_account = #{settlementAccount},</if>
            <if test="enabledDate != null">enabled_date = #{enabledDate},</if>
            <if test="createdDate != null">created_date = #{createdDate},</if>
            <if test="updatedDate != null">updated_date = #{updatedDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGarageTeamById" parameterType="Long">
        delete from garage_team where id = #{id}
    </delete>

    <delete id="deleteGarageTeamByIds" parameterType="String">
        delete from garage_team where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>