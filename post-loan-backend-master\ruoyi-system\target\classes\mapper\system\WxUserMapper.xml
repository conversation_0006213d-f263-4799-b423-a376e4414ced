<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.WxUserMapper">

<!--    &lt;!&ndash; 插入微信用户手机号 &ndash;&gt;-->
<!--    <insert id="savePhoneNumber">-->
<!--        INSERT INTO wx_user (phone_number)-->
<!--        VALUES (#{phoneNumber})-->
<!--    </insert>-->

<!--    &lt;!&ndash; 根据手机号查询用户信息 &ndash;&gt;-->
<!--    <select id="getByPhoneNumber" resultType="com.ruoyi.system.domain.WxUser">-->
<!--        SELECT *-->
<!--        FROM wx_user-->
<!--        WHERE phone_number = #{phoneNumber}-->
<!--    </select>-->
<!--    <select id="selectByOpenId" resultType="com.ruoyi.system.domain.WxUser">-->
<!--         SELECT *-->
<!--         FROM wx_user-->
<!--         WHERE openid = #{openid}-->
<!--    </select>-->

<!--    <insert id="insert" parameterType="WxUser">-->
<!--        INSERT INTO wx_user (id, openid, phone_number, create_time, update_time)-->
<!--        VALUES (#{id}, #{openid}, #{phoneNumber}, #{createTime}, #{updateTime})-->
<!--    </insert>-->

</mapper>
