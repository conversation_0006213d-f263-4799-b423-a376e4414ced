package com.ruoyi.installment_application_audit.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.installment_application_audit.domain.InstallmentApplicationAudit;
import com.ruoyi.installment_application_audit.service.IInstallmentApplicationAuditService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.installment_application.domain.InstallmentApplication;
import com.ruoyi.installment_application.service.IInstallmentApplicationService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

/**
 * 分期申请审核中间Controller
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/installment_application_audit/installment_application_audit")
public class InstallmentApplicationAuditController extends BaseController {
    @Autowired
    private IInstallmentApplicationAuditService installmentApplicationAuditService;
    @Autowired
    private IInstallmentApplicationService installmentApplicationService;

    /**
     * 查询分期申请审核中间列表
     */
    @PreAuthorize("@ss.hasPermi('installment_application_audit:installment_application_audit:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstallmentApplicationAudit installmentApplicationAudit,
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        // 直接赋值，无需parse
        if (startTime != null) {
            installmentApplicationAudit.setTailPayTimeStart(startTime);
        }
        if (endTime != null) {
            installmentApplicationAudit.setTailPayTimeEnd(endTime);
        }
        startPage();
        List<InstallmentApplicationAudit> list = installmentApplicationAuditService
                .selectInstallmentApplicationAuditList(installmentApplicationAudit);
        return getDataTable(list);
    }

    /**
     * 导出分期申请审核中间列表
     */
    @PreAuthorize("@ss.hasPermi('installment_application_audit:installment_application_audit:export')")
    @Log(title = "分期申请审核中间", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstallmentApplicationAudit installmentApplicationAudit) {
        List<InstallmentApplicationAudit> list = installmentApplicationAuditService
                .selectInstallmentApplicationAuditList(installmentApplicationAudit);
        ExcelUtil<InstallmentApplicationAudit> util = new ExcelUtil<InstallmentApplicationAudit>(
                InstallmentApplicationAudit.class);
        util.exportExcel(response, list, "分期申请审核中间数据");
    }

    /**
     * 获取分期申请审核中间详细信息
     */
    @PreAuthorize("@ss.hasPermi('installment_application_audit:installment_application_audit:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(installmentApplicationAuditService.selectInstallmentApplicationAuditById(id));
    }

    /**
     * 新增分期申请审核中间
     */
    @PreAuthorize("@ss.hasPermi('installment_application_audit:installment_application_audit:add')")
    @Log(title = "分期申请审核中间", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstallmentApplicationAudit installmentApplicationAudit) {
        installmentApplicationAudit.setCreateBy(getUsername());
        installmentApplicationAudit.setCreateDate(DateUtils.getNowDate());
        return toAjax(
                installmentApplicationAuditService.insertInstallmentApplicationAudit(installmentApplicationAudit));
    }

    /**
     * 修改分期申请审核中间
     */
    @PreAuthorize("@ss.hasPermi('installment_application_audit:installment_application_audit:edit')")
    @Log(title = "分期申请审核中间", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstallmentApplicationAudit temp) {
        int result = installmentApplicationAuditService.updateInstallmentApplicationAudit(temp);
        // status==2时，将审核表数据插入分期申请表
        InstallmentApplicationAudit installmentApplicationAudit = installmentApplicationAuditService.selectInstallmentApplicationAuditById(temp.getId());
        logger.info("修改审核表数据，审核结果为：{}", installmentApplicationAudit.getStatus());
        if (temp.getStatus() != null && temp.getStatus() == 2) {
            // 直接用传入的对象，不再查一次
            java.math.BigDecimal tailAmount = installmentApplicationAudit.getTailAmount();
            java.util.Date tailPayTime = installmentApplicationAudit.getTailPayTime();
            Integer repayDayOfMonth = installmentApplicationAudit.getRepayDay();
            if (repayDayOfMonth == null) {
                repayDayOfMonth = 1;
            }
            java.util.Date now = DateUtils.getNowDate();
            Long periodCount = installmentApplicationAudit.getPeriodCount();
            int loopCount = periodCount != null ? periodCount.intValue() : 0;
            boolean hasTail = tailAmount != null && tailAmount.compareTo(java.math.BigDecimal.ZERO) > 0;
            if (hasTail) {
                loopCount += 1;
            }
            int total = 0;
            for (int i = 1; i <= loopCount; i++) {
                InstallmentApplication item = new InstallmentApplication();
                item.setPeriodCount((long) i);
                item.setActualPaymentAmount(installmentApplicationAudit.getApplyAmount());
                item.setAccountType(installmentApplicationAudit.getAccountType());
                item.setLoanId(installmentApplicationAudit.getLoanId());
                item.setCreateDate(now);
                item.setUpdateDate(now);
                item.setCreateBy(getUsername());
                item.setInstallmentApplicationId(installmentApplicationAudit.getId());
                // 账单金额
                if (hasTail && i == loopCount) {
                    item.setBillAmount(tailAmount);
                } else {
                    item.setBillAmount(installmentApplicationAudit.getBillAmount());
                }
                // 还款日
                if (hasTail && i == loopCount) {
                    if (tailPayTime != null) {
                        item.setRepayDay(tailPayTime);
                    } else {
                        java.util.Calendar cal = java.util.Calendar.getInstance();
                        cal.setTime(now);
                        cal.add(java.util.Calendar.MONTH, i - 1);
                        cal.set(java.util.Calendar.DAY_OF_MONTH, repayDayOfMonth);
                        item.setRepayDay(cal.getTime());
                    }
                } else {
                    java.util.Calendar cal = java.util.Calendar.getInstance();
                    cal.setTime(now);
                    cal.add(java.util.Calendar.MONTH, i - 1);
                    cal.set(java.util.Calendar.DAY_OF_MONTH, repayDayOfMonth);
                    item.setRepayDay(cal.getTime());
                }
                total += installmentApplicationService.insertInstallmentApplication(item);
            }
        }
        return toAjax(result);
    }

    /**
     * 删除分期申请审核中间
     */
    @PreAuthorize("@ss.hasPermi('installment_application_audit:installment_application_audit:remove')")
    @Log(title = "分期申请审核中间", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(installmentApplicationAuditService.deleteInstallmentApplicationAuditByIds(ids));
    }
}
