<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.quartz.mapper.QuartzCronMapper">

<!--    延期视图-->
    <resultMap type="QuartzCronExtension" id="quartzCron1">
        <id property="id" column="id"/>
        <result property="loanId" column="loan_id"/>
        <result property="extensionDate" column="extension_date"/>
        <result property="status" column="status"/>
        <result property="reason" column="reason"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="customerName" column="customer_name"/>
        <result property="gzhOpenid" column="gzh_openid"/>
    </resultMap>

    <sql id="selectvw_loan_extensionVo">
        select * from time_loan_extension
    </sql>

    <select id="selectvw_loan_extension" parameterType="QuartzCronExtension" resultMap="quartzCron1">
        <include refid="selectvw_loan_extensionVo"/>
        <where>

            <if test="loanId != null">and loan_id = #{loanId}</if>
            <if test="extensionDate != null">and extension_date = #{extensionDate}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="reason != null">and reason = #{reason}</if>
            <if test="createBy != null">and create_by = #{createBy}</if>
            <if test="createTime != null">and create_time = #{createTime}</if>
            <if test="updateBy != null">and update_by = #{updateBy}</if>
            <if test="updateTime != null">and update_time = #{updateTime}</if>
            <if test="customerName != null">and customer_name = #{customerName}</if>
            <if test="gzhOpenid != null">and gzh_openid = #{gzhOpenid}</if>
        </where>
    </select>


<!--催记视图-->
    <resultMap type="QuartzCronReminder" id="quartzCron2">
        <id property="id" column="id"/>
        <result property="id" column="id"/>
        <result property="loanId" column="loan_id"/>
        <result property="userId" column="user_id"/>
        <result property="identity" column="identity"/>
        <result property="customerName" column="customer_name"/>
        <result property="customerMobile" column="customer_mobile"/>
        <result property="carStatus" column="car_status"/>
        <result property="repaymentStatus" column="repayment_status"/>
        <result property="examineStatus" column="examine_status"/>
        <result property="examineReason" column="examine_reason"/>
        <result property="BMoney" column="B_money"/>
        <result property="DMoney" column="D_money"/>
        <result property="OMoney" column="O_money"/>
        <result property="CMoney" column="C_money"/>
        <result property="BRepaymentImg" column="B_repayment_img"/>
        <result property="DRepaymentImg" column="D_repayment_img"/>
        <result property="ORepaymentImg" column="O_repayment_img"/>
        <result property="CRepaymentImg" column="C_repayment_img"/>
        <result property="BAccount" column="B_account"/>
        <result property="DAccount" column="D_account"/>
        <result property="OAccount" column="O_account"/>
        <result property="CAccount" column="C_account"/>
        <result property="urgeStatus" column="urge_status"/>
        <result property="urgeDescribe" column="urge_describe"/>
        <result property="urgeMoney" column="urge_money"/>
        <result property="appointedTime" column="appointed_time"/>
        <result property="trackingTime" column="tracking_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="PMoney" column="P_money"/>
        <result property="PRepaymentImg" column="P_repayment_img"/>
        <result property="PAccount" column="P_account"/>
        <result property="gzhOpenid" column="gzh_openid"/>
    </resultMap>

    <sql id="selectvw_loan_reminderVo">
        select * from time_loan_reminder
    </sql>

    <select id="selectvw_loan_reminder" parameterType="QuartzCronExtension" resultMap="quartzCron2">
        <include refid="selectvw_loan_reminderVo"/>
        <where>
            <if test="id != null">and `id` = #{id}</if>
            <if test="loanId != null">and `loan_id` = #{loanId}</if>
            <if test="userId != null">and `user_id` = #{userId}</if>
            <if test="identity != null">and `identity` = #{identity}</if>
            <if test="customerName != null">and `customer_name` = #{customerName}</if>
            <if test="customerMobile != null">and `customer_mobile` = #{customerMobile}</if>
            <if test="carStatus != null">and `car_status` = #{carStatus}</if>
            <if test="repaymentStatus != null">and `repayment_status` = #{repaymentStatus}</if>
            <if test="examineStatus != null">and `examine_status` = #{examineStatus}</if>
            <if test="examineReason != null">and `examine_reason` = #{examineReason}</if>
            <if test="BMoney != null">and `B_money` = #{BMoney}</if>
            <if test="DMoney != null">and `D_money` = #{DMoney}</if>
            <if test="OMoney != null">and `O_money` = #{OMoney}</if>
            <if test="CMoney != null">and `C_money` = #{CMoney}</if>
            <if test="BRepaymentImg != null">and `B_repayment_img` = #{BRepaymentImg}</if>
            <if test="DRepaymentImg != null">and `D_repayment_img` = #{DRepaymentImg}</if>
            <if test="ORepaymentImg != null">and `O_repayment_img` = #{ORepaymentImg}</if>
            <if test="CRepaymentImg != null">and `C_repayment_img` = #{CRepaymentImg}</if>
            <if test="BAccount != null">and `B_account` = #{BAccount}</if>
            <if test="DAccount != null">and `D_account` = #{DAccount}</if>
            <if test="OAccount != null">and `O_account` = #{OAccount}</if>
            <if test="CAccount != null">and `C_account` = #{CAccount}</if>
            <if test="urgeStatus != null">and `urge_status` = #{urgeStatus}</if>
            <if test="urgeDescribe != null">and `urge_describe` = #{urgeDescribe}</if>
            <if test="urgeMoney != null">and `urge_money` = #{urgeMoney}</if>
            <if test="appointedTime != null">and `appointed_time` = #{appointedTime}</if>
            <if test="trackingTime != null">and `tracking_time` = #{trackingTime}</if>
            <if test="createBy != null">and `create_by` = #{createBy}</if>
            <if test="createTime != null">and `create_time` = #{createTime}</if>
            <if test="updateBy != null">and `update_by` = #{updateBy}</if>
            <if test="updateTime != null">and `update_time` = #{updateTime}</if>
            <if test="status != null">and `status` = #{status}</if>
            <if test="PMoney != null">and `P_money` = #{PMoney}</if>
            <if test="PRepaymentImg != null">and `P_repayment_img` = #{PRepaymentImg}</if>
            <if test="PAccount != null">and `P_account` = #{PAccount}</if>
            <if test="gzhOpenid != null">and `gzh_openid` = #{gzhOpenid}</if>
        </where>
    </select>

</mapper>

