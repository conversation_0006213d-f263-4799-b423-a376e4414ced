package com.ruoyi.hr_repay_plan.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.hr_repay_plan.domain.HrRepayPlan;
import com.ruoyi.hr_repay_plan.service.IHrRepayPlanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 华瑞还款计划Controller
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/hr_repay_plan/hr_repay_plan")
public class HrRepayPlanController extends BaseController
{
    @Autowired
    private IHrRepayPlanService hrRepayPlanService;

    /**
     * 查询华瑞还款计划列表
     */
    @PreAuthorize("@ss.hasPermi('hr_repay_plan:hr_repay_plan:list')")
    @GetMapping("/list")
    public TableDataInfo list(HrRepayPlan hrRepayPlan)
    {
        startPage();
        List<HrRepayPlan> list = hrRepayPlanService.selectHrRepayPlanList(hrRepayPlan);
        return getDataTable(list);
    }

    /**
     * 导出华瑞还款计划列表
     */
    @PreAuthorize("@ss.hasPermi('hr_repay_plan:hr_repay_plan:export')")
    @Log(title = "华瑞还款计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HrRepayPlan hrRepayPlan)
    {
        List<HrRepayPlan> list = hrRepayPlanService.selectHrRepayPlanList(hrRepayPlan);
        ExcelUtil<HrRepayPlan> util = new ExcelUtil<HrRepayPlan>(HrRepayPlan.class);
        util.exportExcel(response, list, "华瑞还款计划数据");
    }

    /**
     * 获取华瑞还款计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('hr_repay_plan:hr_repay_plan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(hrRepayPlanService.selectHrRepayPlanById(id));
    }

    /**
     * 新增华瑞还款计划
     */
    @PreAuthorize("@ss.hasPermi('hr_repay_plan:hr_repay_plan:add')")
    @Log(title = "华瑞还款计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HrRepayPlan hrRepayPlan)
    {
        return toAjax(hrRepayPlanService.insertHrRepayPlan(hrRepayPlan));
    }

    /**
     * 修改华瑞还款计划
     */
    @PreAuthorize("@ss.hasPermi('hr_repay_plan:hr_repay_plan:edit')")
    @Log(title = "华瑞还款计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HrRepayPlan hrRepayPlan)
    {
        return toAjax(hrRepayPlanService.updateHrRepayPlan(hrRepayPlan));
    }

    /**
     * 删除华瑞还款计划
     */
    @PreAuthorize("@ss.hasPermi('hr_repay_plan:hr_repay_plan:remove')")
    @Log(title = "华瑞还款计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(hrRepayPlanService.deleteHrRepayPlanByIds(ids));
    }
}
