package com.ruoyi.litigation_cost_approval.domain.dto;

import java.util.List;

/**
 * 批量审批请求DTO
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class BatchApprovalRequest {
    
    /** 记录ID列表 */
    private List<Long> ids;
    
    /** 审批状态(1-通过 2-拒绝) */
    private String status;
    
    /** 拒绝原因 */
    private String rejectReason;

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    @Override
    public String toString() {
        return "BatchApprovalRequest{" +
                "ids=" + ids +
                ", status='" + status + '\'' +
                ", rejectReason='" + rejectReason + '\'' +
                '}';
    }
}
