package com.ruoyi.card.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 行驶证对象 car_card
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
//@Data

public class CarCard extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyNo;

    /** 客户编号 */
    @Excel(name = "客户编号")
    private String customerId;

    /** 车架号 */
    @Excel(name = "车架号")
    private String identityNo;

    /** 品牌型号 */
    @Excel(name = "品牌型号")
    private String shape;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String plateNo;

    /** 车辆类型 */
    @Excel(name = "车辆类型")
    private String carType;

    /** 车辆所有人 */
    @Excel(name = "车辆所有人")
    private String carOwner;

    /** 住址 */
    @Excel(name = "住址")
    private String address;

    /** 使用性质 */
    @Excel(name = "使用性质")
    private String useNature;

    /** 发动机号 */
    @Excel(name = "发动机号")
    private String engineNumber;

    /** 注册日期 */
    @Excel(name = "注册日期")
    private String registerDate;

    /** 发证日期 */
    @Excel(name = "发证日期")
    private String issueDate;

    /** 登记机关 */
    @Excel(name = "登记机关")
    private String registerOrg;

    /** 创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 修改日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 删除标记 */
    private String delFlag;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }

    public void setApplyNo(String applyNo)
    {
        this.applyNo = applyNo;
    }

    public String getApplyNo()
    {
        return applyNo;
    }

    public void setCustomerId(String customerId)
    {
        this.customerId = customerId;
    }

    public String getCustomerId()
    {
        return customerId;
    }

    public void setIdentityNo(String identityNo)
    {
        this.identityNo = identityNo;
    }

    public String getIdentityNo()
    {
        return identityNo;
    }

    public void setShape(String shape)
    {
        this.shape = shape;
    }

    public String getShape()
    {
        return shape;
    }

    public void setPlateNo(String plateNo)
    {
        this.plateNo = plateNo;
    }

    public String getPlateNo()
    {
        return plateNo;
    }

    public void setCarType(String carType)
    {
        this.carType = carType;
    }

    public String getCarType()
    {
        return carType;
    }

    public void setCarOwner(String carOwner)
    {
        this.carOwner = carOwner;
    }

    public String getCarOwner()
    {
        return carOwner;
    }

    public void setAddress(String address)
    {
        this.address = address;
    }

    public String getAddress()
    {
        return address;
    }

    public void setUseNature(String useNature)
    {
        this.useNature = useNature;
    }

    public String getUseNature()
    {
        return useNature;
    }

    public void setEngineNumber(String engineNumber)
    {
        this.engineNumber = engineNumber;
    }

    public String getEngineNumber()
    {
        return engineNumber;
    }

    public void setRegisterDate(String registerDate)
    {
        this.registerDate = registerDate;
    }

    public String getRegisterDate()
    {
        return registerDate;
    }

    public void setIssueDate(String issueDate)
    {
        this.issueDate = issueDate;
    }

    public String getIssueDate()
    {
        return issueDate;
    }

    public void setRegisterOrg(String registerOrg)
    {
        this.registerOrg = registerOrg;
    }

    public String getRegisterOrg()
    {
        return registerOrg;
    }

    public void setCreateDate(Date createDate)
    {
        this.createDate = createDate;
    }

    public Date getCreateDate()
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate)
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate()
    {
        return updateDate;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("applyNo", getApplyNo())
            .append("customerId", getCustomerId())
            .append("identityNo", getIdentityNo())
            .append("shape", getShape())
            .append("plateNo", getPlateNo())
            .append("carType", getCarType())
            .append("carOwner", getCarOwner())
            .append("address", getAddress())
            .append("useNature", getUseNature())
            .append("engineNumber", getEngineNumber())
            .append("registerDate", getRegisterDate())
            .append("issueDate", getIssueDate())
            .append("registerOrg", getRegisterOrg())
            .append("createBy", getCreateBy())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
