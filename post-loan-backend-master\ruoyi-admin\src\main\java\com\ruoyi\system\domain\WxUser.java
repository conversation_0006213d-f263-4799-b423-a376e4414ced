package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 微信用户对象 wx_user
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public class WxUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 头像 */
    @Excel(name = "头像")
    private String avatar;

    /** 昵称 */
    @Excel(name = "昵称")
    private String monicker;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String mobile;

    /** 密码 */
    @Excel(name = "密码")
    private String password;

    /** 角色（1、出单员工 2、直营渠道的上访员工 3、外部的强制上访会员 4、外部的找车员） */
    @Excel(name = "角色", readConverterExp = "1=、出单员工,2=、直营渠道的上访员工,3=、外部的强制上访会员,4=、外部的找车员")
    private Long auth;

    /** 盐 */
    @Excel(name = "盐")
    private String salt;

    /** 所属企业id */
    @Excel(name = "所属企业id")
    private Long gid;

    /** 微信unioinid */
    @Excel(name = "微信unioinid")
    private String unionid;

    /** 小程序openid */
    @Excel(name = "小程序openid")
    private String openidMiniapp;

    /** 服务号openid */
    @Excel(name = "服务号openid")
    private String openidService;

    /** 公众号openid */
    @Excel(name = "公众号openid")
    private String openidPublic;

    /** appopenid */
    @Excel(name = "appopenid")
    private String openidApp;

    /** h5openid */
    @Excel(name = "h5openid")
    private String openidWeb;

    /** 性别 */
    @Excel(name = "性别")
    private Integer sex;

    /** 部门id */
    @Excel(name = "部门id")
    private Long deptId;

    /** 员工编号 */
    @Excel(name = "员工编号")
    private String no;

    /** 注册ip */
    @Excel(name = "注册ip")
    private String addip;

    /** 登入时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "登入时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date logintime;

    /** 登入ip */
    @Excel(name = "登入ip")
    private String loginip;

    /** 登入次数 */
    @Excel(name = "登入次数")
    private Long loginnum;

    /** 1已关注公众号 0未关注 */
    @Excel(name = "1已关注公众号 0未关注")
    private Integer attention;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date jointime;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long province;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long city;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long area;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String provinceCn;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String cityCn;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String areaCn;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private Long createUid;

    /** 最后修改人ID */
    @Excel(name = "最后修改人ID")
    private Long lastEditUid;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date addtime;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date edittime;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Integer status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setAvatar(String avatar) 
    {
        this.avatar = avatar;
    }

    public String getAvatar() 
    {
        return avatar;
    }

    public void setMonicker(String monicker) 
    {
        this.monicker = monicker;
    }

    public String getMonicker() 
    {
        return monicker;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setMobile(String mobile) 
    {
        this.mobile = mobile;
    }

    public String getMobile() 
    {
        return mobile;
    }

    public void setPassword(String password) 
    {
        this.password = password;
    }

    public String getPassword() 
    {
        return password;
    }

    public void setAuth(Long auth) 
    {
        this.auth = auth;
    }

    public Long getAuth() 
    {
        return auth;
    }

    public void setSalt(String salt) 
    {
        this.salt = salt;
    }

    public String getSalt() 
    {
        return salt;
    }

    public void setGid(Long gid) 
    {
        this.gid = gid;
    }

    public Long getGid() 
    {
        return gid;
    }

    public void setUnionid(String unionid) 
    {
        this.unionid = unionid;
    }

    public String getUnionid() 
    {
        return unionid;
    }

    public void setOpenidMiniapp(String openidMiniapp) 
    {
        this.openidMiniapp = openidMiniapp;
    }

    public String getOpenidMiniapp() 
    {
        return openidMiniapp;
    }

    public void setOpenidService(String openidService) 
    {
        this.openidService = openidService;
    }

    public String getOpenidService() 
    {
        return openidService;
    }

    public void setOpenidPublic(String openidPublic) 
    {
        this.openidPublic = openidPublic;
    }

    public String getOpenidPublic() 
    {
        return openidPublic;
    }

    public void setOpenidApp(String openidApp) 
    {
        this.openidApp = openidApp;
    }

    public String getOpenidApp() 
    {
        return openidApp;
    }

    public void setOpenidWeb(String openidWeb) 
    {
        this.openidWeb = openidWeb;
    }

    public String getOpenidWeb() 
    {
        return openidWeb;
    }

    public void setSex(Integer sex) 
    {
        this.sex = sex;
    }

    public Integer getSex() 
    {
        return sex;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    public void setNo(String no) 
    {
        this.no = no;
    }

    public String getNo() 
    {
        return no;
    }

    public void setAddip(String addip) 
    {
        this.addip = addip;
    }

    public String getAddip() 
    {
        return addip;
    }

    public void setLogintime(Date logintime) 
    {
        this.logintime = logintime;
    }

    public Date getLogintime() 
    {
        return logintime;
    }

    public void setLoginip(String loginip) 
    {
        this.loginip = loginip;
    }

    public String getLoginip() 
    {
        return loginip;
    }

    public void setLoginnum(Long loginnum) 
    {
        this.loginnum = loginnum;
    }

    public Long getLoginnum() 
    {
        return loginnum;
    }

    public void setAttention(Integer attention) 
    {
        this.attention = attention;
    }

    public Integer getAttention() 
    {
        return attention;
    }

    public void setJointime(Date jointime) 
    {
        this.jointime = jointime;
    }

    public Date getJointime() 
    {
        return jointime;
    }

    public void setProvince(Long province) 
    {
        this.province = province;
    }

    public Long getProvince() 
    {
        return province;
    }

    public void setCity(Long city) 
    {
        this.city = city;
    }

    public Long getCity() 
    {
        return city;
    }

    public void setArea(Long area) 
    {
        this.area = area;
    }

    public Long getArea() 
    {
        return area;
    }

    public void setProvinceCn(String provinceCn) 
    {
        this.provinceCn = provinceCn;
    }

    public String getProvinceCn() 
    {
        return provinceCn;
    }

    public void setCityCn(String cityCn) 
    {
        this.cityCn = cityCn;
    }

    public String getCityCn() 
    {
        return cityCn;
    }

    public void setAreaCn(String areaCn) 
    {
        this.areaCn = areaCn;
    }

    public String getAreaCn() 
    {
        return areaCn;
    }

    public void setCreateUid(Long createUid) 
    {
        this.createUid = createUid;
    }

    public Long getCreateUid() 
    {
        return createUid;
    }

    public void setLastEditUid(Long lastEditUid) 
    {
        this.lastEditUid = lastEditUid;
    }

    public Long getLastEditUid() 
    {
        return lastEditUid;
    }

    public void setAddtime(Date addtime) 
    {
        this.addtime = addtime;
    }

    public Date getAddtime() 
    {
        return addtime;
    }

    public void setEdittime(Date edittime) 
    {
        this.edittime = edittime;
    }

    public Date getEdittime() 
    {
        return edittime;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("avatar", getAvatar())
            .append("monicker", getMonicker())
            .append("name", getName())
            .append("mobile", getMobile())
            .append("password", getPassword())
            .append("auth", getAuth())
            .append("salt", getSalt())
            .append("gid", getGid())
            .append("unionid", getUnionid())
            .append("openidMiniapp", getOpenidMiniapp())
            .append("openidService", getOpenidService())
            .append("openidPublic", getOpenidPublic())
            .append("openidApp", getOpenidApp())
            .append("openidWeb", getOpenidWeb())
            .append("sex", getSex())
            .append("deptId", getDeptId())
            .append("no", getNo())
            .append("addip", getAddip())
            .append("logintime", getLogintime())
            .append("loginip", getLoginip())
            .append("loginnum", getLoginnum())
            .append("attention", getAttention())
            .append("jointime", getJointime())
            .append("province", getProvince())
            .append("city", getCity())
            .append("area", getArea())
            .append("provinceCn", getProvinceCn())
            .append("cityCn", getCityCn())
            .append("areaCn", getAreaCn())
            .append("createUid", getCreateUid())
            .append("lastEditUid", getLastEditUid())
            .append("addtime", getAddtime())
            .append("edittime", getEdittime())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
