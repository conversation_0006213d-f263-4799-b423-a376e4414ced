<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vm_car_order.mapper.VwCarOrderMapper">
    
    <resultMap type="VwCarOrder" id="VwCarOrderResult">
        <result property="id"    column="id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="teamId"    column="team_id"    />
        <result property="garageId"    column="garage_id"    />
        <result property="libraryStatus"    column="library_status"    />
        <result property="inboundTime"    column="inbound_time"    />
        <result property="outboundTime"    column="outbound_time"    />
        <result property="locatingCommission"    column="locating_commission"    />
        <result property="keyStatus"    column="key_status"    />
        <result property="keyTime"    column="key_time"    />
        <result property="collectionMethod"    column="collection_method"    />
        <result property="status"    column="status"    />
        <result property="allocationTime"    column="allocation_time"    />
        <result property="keyProvince"    column="key_province"    />
        <result property="keyCity"    column="key_city"    />
        <result property="keyBorough"    column="key_borough"    />
        <result property="keyAddress"    column="key_address"    />
        <result property="customerName"    column="customer_name"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="nickName"    column="nick_name"    />
        <result property="plateNo"    column="plate_no"    />
        <result property="jgName"    column="jg_name"    />
        <result property="jgStatus"    column="jg_status"    />
        <result property="carDetailAddress"    column="car_detail_address"    />
        <result property="carStatus"    column="car_status"    />
        <result property="gpsStatus"    column="gps_status"    />
        <result property="teamName"    column="team_name"    />
        <result property="garageName"    column="garage_name"    />
        <result property="slippageStatus"    column="slippage_status"    />
        <result property="overdueAmt"    column="overdue_amt"    />
        <result property="dispatcher"    column="dispatcher"    />
    </resultMap>

    <sql id="selectVwCarOrderVo">
        select a.id, a.create_by, a.create_date, a.update_by, a.update_date, a.apply_no, a.team_id, a.garage_id, f.library_status, f.inbound_time, f.outbound_time, a.locating_commission, a.key_status, a.key_time, a.collection_method, a.status, a.allocation_time, a.key_province, a.key_city, a.key_borough, a.key_address, b.customer_name, b.mobile_phone, b.nick_name, b.plate_no, b.jg_name, b.jg_status, c.car_detail_address, c.car_status, c.gps_status, d.team_name, e.name as garage_name, g.slippage_status, h.overdue_amt, a.dispatcher from car_order a left join vw_account_loan b on a.apply_no = b.apply_id left join ind_car_info c on a.apply_no = c.apply_no left join garage_team d on a.team_id = d.id left join garage e on a.garage_id = e.id left join car_warehousing f on a.apply_no = f.apply_no left join loan_list g on a.apply_no = g.apply_id left join account_loan h on a.apply_no = h.apply_id
    </sql>

    <select id="selectVwCarOrderList" parameterType="VwCarOrder" resultMap="VwCarOrderResult">
        <include refid="selectVwCarOrderVo"/>
        <where>  
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="garageId != null "> and garage_id = #{garageId}</if>
            <if test="libraryStatus != null "> and library_status = #{libraryStatus}</if>
            <if test="inboundTime != null "> and inbound_time = #{inboundTime}</if>
            <if test="outboundTime != null "> and outbound_time = #{outboundTime}</if>
            <if test="locatingCommission != null "> and locating_commission = #{locatingCommission}</if>
            <if test="keyStatus != null "> and key_status = #{keyStatus}</if>
            <if test="keyTime != null "> and key_time = #{keyTime}</if>
            <if test="collectionMethod != null "> and collection_method = #{collectionMethod}</if>
            <if test="status != null "> and a.status = #{status}</if>
            <if test="startTime != null and endTime != null"> AND allocation_time BETWEEN #{startTime} AND #{endTime}</if>
            <if test="keyProvince != null  and keyProvince != ''"> and key_province = #{keyProvince}</if>
            <if test="keyCity != null  and keyCity != ''"> and key_city = #{keyCity}</if>
            <if test="keyBorough != null  and keyBorough != ''"> and key_borough = #{keyBorough}</if>
            <if test="keyAddress != null  and keyAddress != ''"> and key_address = #{keyAddress}</if>
            <if test="customerName != null  and customerName != ''"> and b.customer_name like concat('%', #{customerName}, '%')</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and b.mobile_phone = #{mobilePhone}</if>
            <if test="nickName != null  and nickName != ''"> and b.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="plateNo != null  and plateNo != ''"> and b.plate_no = #{plateNo}</if>
            <if test="jgName != null  and jgName != ''"> and b.jg_name like concat('%', #{jgName}, '%')</if>
            <if test="jgStatus != null  and jgStatus != ''"> and b.jg_status = #{jgStatus}</if>
            <if test="carDetailAddress != null  and carDetailAddress != ''"> and c.car_detail_address = #{carDetailAddress}</if>
            <if test="carStatus != null  and carStatus != ''"> and c.car_status = #{carStatus}</if>
            <if test="gpsStatus != null  and gpsStatus != ''"> and c.gps_status = #{gpsStatus}</if>
            <if test="teamName != null  and teamName != ''"> and d.team_name like concat('%', #{teamName}, '%')</if>
            <if test="garageName != null  and garageName != ''"> and e.name like concat('%', #{garageName}, '%')</if>
            <if test="dispatcher != null"> and a.dispatcher = #{dispatcher}</if>
        </where>
    </select>
    
    <select id="selectVwCarOrderById" parameterType="String" resultMap="VwCarOrderResult">
        <include refid="selectVwCarOrderVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertVwCarOrder" parameterType="VwCarOrder">
        insert into vw_car_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="applyNo != null">apply_no,</if>
            <if test="teamId != null">team_id,</if>
            <if test="garageId != null">garage_id,</if>
            <if test="libraryStatus != null">library_status,</if>
            <if test="inboundTime != null">inbound_time,</if>
            <if test="outboundTime != null">outbound_time,</if>
            <if test="locatingCommission != null">locating_commission,</if>
            <if test="keyStatus != null">key_status,</if>
            <if test="keyTime != null">key_time,</if>
            <if test="collectionMethod != null">collection_method,</if>
            <if test="status != null">status,</if>
            <if test="allocationTime != null">allocation_time,</if>
            <if test="keyProvince != null">key_province,</if>
            <if test="keyCity != null">key_city,</if>
            <if test="keyBorough != null">key_borough,</if>
            <if test="keyAddress != null">key_address,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="mobilePhone != null">mobile_phone,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="plateNo != null">plate_no,</if>
            <if test="jgName != null">jg_name,</if>
            <if test="jgStatus != null">jg_status,</if>
            <if test="carDetailAddress != null">car_detail_address,</if>
            <if test="carStatus != null">car_status,</if>
            <if test="gpsStatus != null">gps_status,</if>
            <if test="teamName != null">team_name,</if>
            <if test="garageName != null">garage_name,</if>
            <if test="dispatcher != null">dispatcher,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="garageId != null">#{garageId},</if>
            <if test="libraryStatus != null">#{libraryStatus},</if>
            <if test="inboundTime != null">#{inboundTime},</if>
            <if test="outboundTime != null">#{outboundTime},</if>
            <if test="locatingCommission != null">#{locatingCommission},</if>
            <if test="keyStatus != null">#{keyStatus},</if>
            <if test="keyTime != null">#{keyTime},</if>
            <if test="collectionMethod != null">#{collectionMethod},</if>
            <if test="status != null">#{status},</if>
            <if test="allocationTime != null">#{allocationTime},</if>
            <if test="keyProvince != null">#{keyProvince},</if>
            <if test="keyCity != null">#{keyCity},</if>
            <if test="keyBorough != null">#{keyBorough},</if>
            <if test="keyAddress != null">#{keyAddress},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="mobilePhone != null">#{mobilePhone},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="plateNo != null">#{plateNo},</if>
            <if test="jgName != null">#{jgName},</if>
            <if test="jgStatus != null">#{jgStatus},</if>
            <if test="carDetailAddress != null">#{carDetailAddress},</if>
            <if test="carStatus != null">#{carStatus},</if>
            <if test="gpsStatus != null">#{gpsStatus},</if>
            <if test="teamName != null">#{teamName},</if>
            <if test="garageName != null">#{garageName},</if>
            <if test="dispatcher != null">#{dispatcher},</if>
         </trim>
    </insert>

    <update id="updateVwCarOrder" parameterType="VwCarOrder">
        update vw_car_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="applyNo != null">apply_no = #{applyNo},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="garageId != null">garage_id = #{garageId},</if>
            <if test="libraryStatus != null">library_status = #{libraryStatus},</if>
            <if test="inboundTime != null">inbound_time = #{inboundTime},</if>
            <if test="outboundTime != null">outbound_time = #{outboundTime},</if>
            <if test="locatingCommission != null">locating_commission = #{locatingCommission},</if>
            <if test="keyStatus != null">key_status = #{keyStatus},</if>
            <if test="keyTime != null">key_time = #{keyTime},</if>
            <if test="collectionMethod != null">collection_method = #{collectionMethod},</if>
            <if test="status != null">status = #{status},</if>
            <if test="allocationTime != null">allocation_time = #{allocationTime},</if>
            <if test="keyProvince != null">key_province = #{keyProvince},</if>
            <if test="keyCity != null">key_city = #{keyCity},</if>
            <if test="keyBorough != null">key_borough = #{keyBorough},</if>
            <if test="keyAddress != null">key_address = #{keyAddress},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="mobilePhone != null">mobile_phone = #{mobilePhone},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="plateNo != null">plate_no = #{plateNo},</if>
            <if test="jgName != null">jg_name = #{jgName},</if>
            <if test="jgStatus != null">jg_status = #{jgStatus},</if>
            <if test="carDetailAddress != null">car_detail_address = #{carDetailAddress},</if>
            <if test="carStatus != null">car_status = #{carStatus},</if>
            <if test="gpsStatus != null">gps_status = #{gpsStatus},</if>
            <if test="teamName != null">team_name = #{teamName},</if>
            <if test="garageName != null">garage_name = #{garageName},</if>
            <if test="dispatcher != null">dispatcher = #{dispatcher},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVwCarOrderById" parameterType="String">
        delete from vw_car_order where id = #{id}
    </delete>

    <delete id="deleteVwCarOrderByIds" parameterType="String">
        delete from vw_car_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>