<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.JJHrRepayPlanMapper">
    
    <resultMap type="JJHrRepayPlan" id="HrRepayPlanResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="period"    column="period"    />
        <result property="repayDate"    column="repay_date"    />
        <result property="actualRepayDate"    column="actual_repay_date"    />
        <result property="repayAmount"    column="repay_amount"    />
        <result property="hrRepayAmount"    column="hr_repay_amount"    />
        <result property="capital"    column="capital"    />
        <result property="actualCapital"    column="actual_capital"    />
        <result property="interest"    column="interest"    />
        <result property="actualInterest"    column="actual_interest"    />
        <result property="defInterest"    column="def_interest"    />
        <result property="actualDefInterest"    column="actual_def_interest"    />
        <result property="guaranteeFee"    column="guarantee_fee"    />
        <result property="balance"    column="balance"    />
        <result property="overdueDays"    column="overdue_days"    />
        <result property="status"    column="status"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
    </resultMap>

    <sql id="selectHrRepayPlanVo">
        select * from hr_repay_plan
    </sql>

    <select id="selectHrRepayPlanList" parameterType="JJHrRepayPlan" resultMap="HrRepayPlanResult">
        <include refid="selectHrRepayPlanVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="period != null  and period != ''"> and period = #{period}</if>
            <if test="repayDate != null  and repayDate != ''"> and repay_date = #{repayDate}</if>
            <if test="actualRepayDate != null  and actualRepayDate != ''"> and actual_repay_date = #{actualRepayDate}</if>
            <if test="repayAmount != null  and repayAmount != ''"> and repay_amount = #{repayAmount}</if>
            <if test="hrRepayAmount != null  and hrRepayAmount != ''"> and hr_repay_amount = #{hrRepayAmount}</if>
            <if test="capital != null  and capital != ''"> and capital = #{capital}</if>
            <if test="actualCapital != null  and actualCapital != ''"> and actual_capital = #{actualCapital}</if>
            <if test="interest != null  and interest != ''"> and interest = #{interest}</if>
            <if test="actualInterest != null  and actualInterest != ''"> and actual_interest = #{actualInterest}</if>
            <if test="defInterest != null  and defInterest != ''"> and def_interest = #{defInterest}</if>
            <if test="actualDefInterest != null  and actualDefInterest != ''"> and actual_def_interest = #{actualDefInterest}</if>
            <if test="guaranteeFee != null  and guaranteeFee != ''"> and guarantee_fee = #{guaranteeFee}</if>
            <if test="balance != null  and balance != ''"> and balance = #{balance}</if>
            <if test="overdueDays != null  and overdueDays != ''"> and overdue_days = #{overdueDays}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
    </select>

    <select id="selectHrRepayPlanListGroup" parameterType="JJHrRepayPlan" resultMap="HrRepayPlanResult">
        <include refid="selectHrRepayPlanVo"/>
        <where>
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="period != null  and period != ''"> and period = #{period}</if>
            <if test="repayDate != null  and repayDate != ''"> and repay_date = #{repayDate}</if>
            <if test="actualRepayDate != null  and actualRepayDate != ''"> and actual_repay_date = #{actualRepayDate}</if>
            <if test="repayAmount != null  and repayAmount != ''"> and repay_amount = #{repayAmount}</if>
            <if test="hrRepayAmount != null  and hrRepayAmount != ''"> and hr_repay_amount = #{hrRepayAmount}</if>
            <if test="capital != null  and capital != ''"> and capital = #{capital}</if>
            <if test="actualCapital != null  and actualCapital != ''"> and actual_capital = #{actualCapital}</if>
            <if test="interest != null  and interest != ''"> and interest = #{interest}</if>
            <if test="actualInterest != null  and actualInterest != ''"> and actual_interest = #{actualInterest}</if>
            <if test="defInterest != null  and defInterest != ''"> and def_interest = #{defInterest}</if>
            <if test="actualDefInterest != null  and actualDefInterest != ''"> and actual_def_interest = #{actualDefInterest}</if>
            <if test="guaranteeFee != null  and guaranteeFee != ''"> and guarantee_fee = #{guaranteeFee}</if>
            <if test="balance != null  and balance != ''"> and balance = #{balance}</if>
            <if test="overdueDays != null  and overdueDays != ''"> and overdue_days = #{overdueDays}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
        group by apply_id
    </select>

    <select id="selectHrRepayPlanListNow" parameterType="String" resultMap="HrRepayPlanResult">
        select * from hr_repay_plan where apply_id = #{applyId} and  YEAR(repay_date) = YEAR(CURDATE())  and MONTH(repay_date) = MONTH(CURDATE()) limit 1
    </select>


</mapper>