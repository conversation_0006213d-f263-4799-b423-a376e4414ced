package com.ruoyi.guaranty_info.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.guaranty_info.mapper.GuarantyInfoMapper;
import com.ruoyi.guaranty_info.domain.GuarantyInfo;
import com.ruoyi.guaranty_info.service.IGuarantyInfoService;

/**
 * 抵押登记Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class GuarantyInfoServiceImpl implements IGuarantyInfoService 
{
    @Autowired
    private GuarantyInfoMapper guarantyInfoMapper;

    /**
     * 查询抵押登记
     * 
     * @param id 抵押登记主键
     * @return 抵押登记
     */
    @Override
    public GuarantyInfo selectGuarantyInfoById(String id)
    {
        return guarantyInfoMapper.selectGuarantyInfoById(id);
    }

    /**
     * 查询抵押登记列表
     * 
     * @param guarantyInfo 抵押登记
     * @return 抵押登记
     */
    @Override
    public List<GuarantyInfo> selectGuarantyInfoList(GuarantyInfo guarantyInfo)
    {
        return guarantyInfoMapper.selectGuarantyInfoList(guarantyInfo);
    }

    /**
     * 新增抵押登记
     * 
     * @param guarantyInfo 抵押登记
     * @return 结果
     */
    @Override
    public int insertGuarantyInfo(GuarantyInfo guarantyInfo)
    {
        return guarantyInfoMapper.insertGuarantyInfo(guarantyInfo);
    }

    /**
     * 修改抵押登记
     * 
     * @param guarantyInfo 抵押登记
     * @return 结果
     */
    @Override
    public int updateGuarantyInfo(GuarantyInfo guarantyInfo)
    {
        return guarantyInfoMapper.updateGuarantyInfo(guarantyInfo);
    }

    /**
     * 批量删除抵押登记
     * 
     * @param ids 需要删除的抵押登记主键
     * @return 结果
     */
    @Override
    public int deleteGuarantyInfoByIds(String[] ids)
    {
        return guarantyInfoMapper.deleteGuarantyInfoByIds(ids);
    }

    /**
     * 删除抵押登记信息
     * 
     * @param id 抵押登记主键
     * @return 结果
     */
    @Override
    public int deleteGuarantyInfoById(String id)
    {
        return guarantyInfoMapper.deleteGuarantyInfoById(id);
    }
}
