package com.ruoyi.account_entry.service;

import java.util.List;
import com.ruoyi.account_entry.domain.AccountEntry;

/**
 * 入账登记Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IAccountEntryService {
    /**
     * 查询入账登记
     * 
     * @param id 入账登记主键
     * @return 入账登记
     */
    public AccountEntry selectAccountEntryById(Long id);

    /**
     * 查询入账登记列表
     * 
     * @param accountEntry 入账登记
     * @return 入账登记集合
     */
    public List<AccountEntry> selectAccountEntryList(AccountEntry accountEntry);

    /**
     * 新增入账登记
     * 
     * @param accountEntry 入账登记
     * @return 结果
     */
    public int insertAccountEntry(AccountEntry accountEntry);

    /**
     * 修改入账登记
     * 
     * @param accountEntry 入账登记
     * @return 结果
     */
    public int updateAccountEntry(AccountEntry accountEntry);

    /**
     * 批量删除入账登记
     * 
     * @param ids 需要删除的入账登记主键集合
     * @return 结果
     */
    public int deleteAccountEntryByIds(Long[] ids);

    /**
     * 删除入账登记信息
     * 
     * @param id 入账登记主键
     * @return 结果
     */
    public int deleteAccountEntryById(Long id);

    /**
     * 批量新增入账登记
     * 
     * @param accountEntryList 入账登记列表
     * @return 结果
     */
    int batchInsertAccountEntry(List<AccountEntry> accountEntryList);

    /**
     * 检查是否已存在相同代偿ID和入账类型的记录
     * 
     * @param loanCompensationId 代偿ID
     * @param amountType         入账类型
     * @return 如果存在记录返回true，否则返回false
     */
    boolean existsByLoanCompensationIdAndAmountType(Long loanCompensationId, String amountType);
}
