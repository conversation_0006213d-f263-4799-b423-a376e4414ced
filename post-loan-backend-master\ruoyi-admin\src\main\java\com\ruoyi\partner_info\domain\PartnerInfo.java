package com.ruoyi.partner_info.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 资金方管理对象 partner_info
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
public class PartnerInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id编号 */
    private String id;

    /** 上级机构编号 */
    @Excel(name = "上级机构编号")
    private String parentOrg;

    /** 机构名称 */
    @Excel(name = "机构名称")
    private String orgName;

    /** 机构类型 */
    @Excel(name = "机构类型")
    private String orgType;

    /** 满额天数 */
    @Excel(name = "满额天数")
    private Long fullDays;

    /** 联系人名称 */
    @Excel(name = "联系人名称")
    private String orgContact;

    /** 联系人电话 */
    @Excel(name = "联系人电话")
    private String contactNumber;

    /** 联系人身份证 */
    @Excel(name = "联系人身份证")
    private String contactCertId;

    /** 联系人手机 */
    @Excel(name = "联系人手机")
    private String contactPhone;

    /** 法人代表名称 */
    @Excel(name = "法人代表名称")
    private String legalRepresentName;

    /** 法人代表电话 */
    @Excel(name = "法人代表电话")
    private String legalRepresentNumber;

    /** 地址 */
    @Excel(name = "地址")
    private String address;

    /** 是否有效 */
    @Excel(name = "是否有效")
    private String orgStatus;

    /** 合作状态 */
    @Excel(name = "合作状态")
    private String orgCorpsta;

    /** 营业执照编号 */
    @Excel(name = "营业执照编号")
    private String businessLicenseNo;

    /** 注册资本 */
    @Excel(name = "注册资本")
    private BigDecimal registerAmt;

    /** 营业执照注册号 */
    @Excel(name = "营业执照注册号")
    private String businessLicenseRegistno;

    /** 注册地址 */
    @Excel(name = "注册地址")
    private String registerAddress;

    /** 组织机构代码证 */
    @Excel(name = "组织机构代码证")
    private String organizationCertid;

    /** 合作开始日期 */
    @Excel(name = "合作开始日期")
    private String beginDate;

    /** 合作结束日期 */
    @Excel(name = "合作结束日期")
    private String endDate;

    /** 总授信额度 */
    @Excel(name = "总授信额度")
    private BigDecimal limitAmt;

    /** 额度状态 */
    @Excel(name = "额度状态")
    private String limitStatus;

    /** 额度有效期 */
    @Excel(name = "额度有效期")
    private String limitPeriod;

    /** 是否支持提额 */
    @Excel(name = "是否支持提额")
    private String supportLimitFlag;

    /** 可提额额度 */
    @Excel(name = "可提额额度")
    private BigDecimal supportLimitAmt;

    /** 提额额度有效期 */
    @Excel(name = "提额额度有效期")
    private String supportLimitPeriod;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 备注信息 */
    @Excel(name = "备注信息")
    private String remarks;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setParentOrg(String parentOrg) 
    {
        this.parentOrg = parentOrg;
    }

    public String getParentOrg() 
    {
        return parentOrg;
    }

    public void setOrgName(String orgName) 
    {
        this.orgName = orgName;
    }

    public String getOrgName() 
    {
        return orgName;
    }

    public void setOrgType(String orgType) 
    {
        this.orgType = orgType;
    }

    public String getOrgType() 
    {
        return orgType;
    }

    public void setFullDays(Long fullDays) 
    {
        this.fullDays = fullDays;
    }

    public Long getFullDays() 
    {
        return fullDays;
    }

    public void setOrgContact(String orgContact) 
    {
        this.orgContact = orgContact;
    }

    public String getOrgContact() 
    {
        return orgContact;
    }

    public void setContactNumber(String contactNumber) 
    {
        this.contactNumber = contactNumber;
    }

    public String getContactNumber() 
    {
        return contactNumber;
    }

    public void setContactCertId(String contactCertId) 
    {
        this.contactCertId = contactCertId;
    }

    public String getContactCertId() 
    {
        return contactCertId;
    }

    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }

    public void setLegalRepresentName(String legalRepresentName) 
    {
        this.legalRepresentName = legalRepresentName;
    }

    public String getLegalRepresentName() 
    {
        return legalRepresentName;
    }

    public void setLegalRepresentNumber(String legalRepresentNumber) 
    {
        this.legalRepresentNumber = legalRepresentNumber;
    }

    public String getLegalRepresentNumber() 
    {
        return legalRepresentNumber;
    }

    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }

    public void setOrgStatus(String orgStatus) 
    {
        this.orgStatus = orgStatus;
    }

    public String getOrgStatus() 
    {
        return orgStatus;
    }

    public void setOrgCorpsta(String orgCorpsta) 
    {
        this.orgCorpsta = orgCorpsta;
    }

    public String getOrgCorpsta() 
    {
        return orgCorpsta;
    }

    public void setBusinessLicenseNo(String businessLicenseNo) 
    {
        this.businessLicenseNo = businessLicenseNo;
    }

    public String getBusinessLicenseNo() 
    {
        return businessLicenseNo;
    }

    public void setRegisterAmt(BigDecimal registerAmt) 
    {
        this.registerAmt = registerAmt;
    }

    public BigDecimal getRegisterAmt() 
    {
        return registerAmt;
    }

    public void setBusinessLicenseRegistno(String businessLicenseRegistno) 
    {
        this.businessLicenseRegistno = businessLicenseRegistno;
    }

    public String getBusinessLicenseRegistno() 
    {
        return businessLicenseRegistno;
    }

    public void setRegisterAddress(String registerAddress) 
    {
        this.registerAddress = registerAddress;
    }

    public String getRegisterAddress() 
    {
        return registerAddress;
    }

    public void setOrganizationCertid(String organizationCertid) 
    {
        this.organizationCertid = organizationCertid;
    }

    public String getOrganizationCertid() 
    {
        return organizationCertid;
    }

    public void setBeginDate(String beginDate) 
    {
        this.beginDate = beginDate;
    }

    public String getBeginDate() 
    {
        return beginDate;
    }

    public void setEndDate(String endDate) 
    {
        this.endDate = endDate;
    }

    public String getEndDate() 
    {
        return endDate;
    }

    public void setLimitAmt(BigDecimal limitAmt) 
    {
        this.limitAmt = limitAmt;
    }

    public BigDecimal getLimitAmt() 
    {
        return limitAmt;
    }

    public void setLimitStatus(String limitStatus) 
    {
        this.limitStatus = limitStatus;
    }

    public String getLimitStatus() 
    {
        return limitStatus;
    }

    public void setLimitPeriod(String limitPeriod) 
    {
        this.limitPeriod = limitPeriod;
    }

    public String getLimitPeriod() 
    {
        return limitPeriod;
    }

    public void setSupportLimitFlag(String supportLimitFlag) 
    {
        this.supportLimitFlag = supportLimitFlag;
    }

    public String getSupportLimitFlag() 
    {
        return supportLimitFlag;
    }

    public void setSupportLimitAmt(BigDecimal supportLimitAmt) 
    {
        this.supportLimitAmt = supportLimitAmt;
    }

    public BigDecimal getSupportLimitAmt() 
    {
        return supportLimitAmt;
    }

    public void setSupportLimitPeriod(String supportLimitPeriod) 
    {
        this.supportLimitPeriod = supportLimitPeriod;
    }

    public String getSupportLimitPeriod() 
    {
        return supportLimitPeriod;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("parentOrg", getParentOrg())
            .append("orgName", getOrgName())
            .append("orgType", getOrgType())
            .append("fullDays", getFullDays())
            .append("orgContact", getOrgContact())
            .append("contactNumber", getContactNumber())
            .append("contactCertId", getContactCertId())
            .append("contactPhone", getContactPhone())
            .append("legalRepresentName", getLegalRepresentName())
            .append("legalRepresentNumber", getLegalRepresentNumber())
            .append("address", getAddress())
            .append("orgStatus", getOrgStatus())
            .append("orgCorpsta", getOrgCorpsta())
            .append("businessLicenseNo", getBusinessLicenseNo())
            .append("registerAmt", getRegisterAmt())
            .append("businessLicenseRegistno", getBusinessLicenseRegistno())
            .append("registerAddress", getRegisterAddress())
            .append("organizationCertid", getOrganizationCertid())
            .append("beginDate", getBeginDate())
            .append("endDate", getEndDate())
            .append("limitAmt", getLimitAmt())
            .append("limitStatus", getLimitStatus())
            .append("limitPeriod", getLimitPeriod())
            .append("supportLimitFlag", getSupportLimitFlag())
            .append("supportLimitAmt", getSupportLimitAmt())
            .append("supportLimitPeriod", getSupportLimitPeriod())
            .append("createBy", getCreateBy())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .append("remarks", getRemarks())
            .toString();
    }
}
