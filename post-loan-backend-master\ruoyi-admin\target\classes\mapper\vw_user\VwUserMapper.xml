<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vw_loan_office.mapper.VwUserMapper">
    
    <resultMap type="VwUser" id="VwUserResult">
        <id property="userName" column="user_name" />
        <result property="nickName"    column="nick_name"    />
        <result property="roleId"    column="role_id"    />
    </resultMap>

    <sql id="selectVwUserVo">
        select * from vw_user
    </sql>

    <select id="selectVwUserList" parameterType="VwUser" resultMap="VwUserResult">
        <include refid="selectVwUserVo"/>
        <where>  
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
             <if test="userName != null  and userName != ''"> and user_name = #{userName}</if>
            <if test="roleId != null "> and role_id = #{roleId}</if>
        </where>
    </select>

</mapper>