package com.ruoyi.installment_application_audit.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.installment_application_audit.mapper.InstallmentApplicationAuditMapper;
import com.ruoyi.installment_application_audit.domain.InstallmentApplicationAudit;
import com.ruoyi.installment_application_audit.service.IInstallmentApplicationAuditService;

/**
 * 分期申请审核中间Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class InstallmentApplicationAuditServiceImpl implements IInstallmentApplicationAuditService 
{
    @Autowired
    private InstallmentApplicationAuditMapper installmentApplicationAuditMapper;

    /**
     * 查询分期申请审核中间
     * 
     * @param id 分期申请审核中间主键
     * @return 分期申请审核中间
     */
    @Override
    public InstallmentApplicationAudit selectInstallmentApplicationAuditById(Long id)
    {
        return installmentApplicationAuditMapper.selectInstallmentApplicationAuditById(id);
    }

    /**
     * 查询分期申请审核中间列表
     * 
     * @param installmentApplicationAudit 分期申请审核中间
     * @return 分期申请审核中间
     */
    @Override
    public List<InstallmentApplicationAudit> selectInstallmentApplicationAuditList(InstallmentApplicationAudit installmentApplicationAudit)
    {
        return installmentApplicationAuditMapper.selectInstallmentApplicationAuditList(installmentApplicationAudit);
    }

    /**
     * 新增分期申请审核中间
     * 
     * @param installmentApplicationAudit 分期申请审核中间
     * @return 结果
     */
    @Override
    public int insertInstallmentApplicationAudit(InstallmentApplicationAudit installmentApplicationAudit)
    {
        return installmentApplicationAuditMapper.insertInstallmentApplicationAudit(installmentApplicationAudit);
    }

    /**
     * 修改分期申请审核中间
     * 
     * @param installmentApplicationAudit 分期申请审核中间
     * @return 结果
     */
    @Override
    public int updateInstallmentApplicationAudit(InstallmentApplicationAudit installmentApplicationAudit)
    {
        return installmentApplicationAuditMapper.updateInstallmentApplicationAudit(installmentApplicationAudit);
    }

    /**
     * 批量删除分期申请审核中间
     * 
     * @param ids 需要删除的分期申请审核中间主键
     * @return 结果
     */
    @Override
    public int deleteInstallmentApplicationAuditByIds(Long[] ids)
    {
        return installmentApplicationAuditMapper.deleteInstallmentApplicationAuditByIds(ids);
    }

    /**
     * 删除分期申请审核中间信息
     * 
     * @param id 分期申请审核中间主键
     * @return 结果
     */
    @Override
    public int deleteInstallmentApplicationAuditById(Long id)
    {
        return installmentApplicationAuditMapper.deleteInstallmentApplicationAuditById(id);
    }
}
