package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SyPrepaymentApply;
import com.ruoyi.system.service.ISyPrepaymentApplyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 苏银提前还款Controller
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/system/sy")
public class SyPrepaymentApplyController extends BaseController
{
    @Autowired
    private ISyPrepaymentApplyService syPrepaymentApplyService;

    /**
     * 查询苏银提前还款列表
     */
//    @PreAuthorize("@ss.hasPermi('system:apply:list')")
    @GetMapping("/list")
    public TableDataInfo list(SyPrepaymentApply syPrepaymentApply)
    {
        startPage();
        List<SyPrepaymentApply> list = syPrepaymentApplyService.selectSyPrepaymentApplyList(syPrepaymentApply);
        return getDataTable(list);
    }

    /**
     * 导出苏银提前还款列表
     */
//    @PreAuthorize("@ss.hasPermi('system:apply:export')")
    @Log(title = "苏银提前还款", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SyPrepaymentApply syPrepaymentApply)
    {
        List<SyPrepaymentApply> list = syPrepaymentApplyService.selectSyPrepaymentApplyList(syPrepaymentApply);
        ExcelUtil<SyPrepaymentApply> util = new ExcelUtil<SyPrepaymentApply>(SyPrepaymentApply.class);
        util.exportExcel(response, list, "苏银提前还款数据");
    }

    /**
     * 获取苏银提前还款详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:apply:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(syPrepaymentApplyService.selectSyPrepaymentApplyById(id));
    }

    /**
     * 新增苏银提前还款
     */
    @PreAuthorize("@ss.hasPermi('system:apply:add')")
    @Log(title = "苏银提前还款", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SyPrepaymentApply syPrepaymentApply)
    {
        return toAjax(syPrepaymentApplyService.insertSyPrepaymentApply(syPrepaymentApply));
    }

    /**
     * 修改苏银提前还款
     */
    @PreAuthorize("@ss.hasPermi('system:apply:edit')")
    @Log(title = "苏银提前还款", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SyPrepaymentApply syPrepaymentApply)
    {
        return toAjax(syPrepaymentApplyService.updateSyPrepaymentApply(syPrepaymentApply));
    }

    /**
     * 删除苏银提前还款
     */
    @PreAuthorize("@ss.hasPermi('system:apply:remove')")
    @Log(title = "苏银提前还款", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(syPrepaymentApplyService.deleteSyPrepaymentApplyByIds(ids));
    }
}
