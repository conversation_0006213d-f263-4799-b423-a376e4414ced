<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ind_work.mapper.IndWorkMapper">
    
    <resultMap type="IndWork" id="IndWorkResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="department"    column="department"    />
        <result property="occupationType"    column="occupation_type"    />
        <result property="headshipType"    column="headship_type"    />
        <result property="workStatus"    column="work_status"    />
        <result property="workCorp"    column="work_corp"    />
        <result property="companyType"    column="company_type"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="borough"    column="borough"    />
        <result property="address"    column="address"    />
        <result property="addressDetail"    column="address_detail"    />
        <result property="establishYear"    column="establish_year"    />
        <result property="workYear"    column="work_year"    />
        <result property="businessRegister"    column="business_register"    />
        <result property="dutyType"    column="duty_type"    />
        <result property="workZipCode"    column="work_zip_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="hrName"    column="hr_name"    />
        <result property="hrMobilePhone"    column="hr_mobile_phone"    />
        <result property="industryOwned"    column="industry_owned"    />
        <result property="workTrade"    column="work_trade"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="yhProvince"    column="yh_province"    />
        <result property="yhCity"    column="yh_city"    />
        <result property="yhBorough"    column="yh_borough"    />
        <result property="yhAddress"    column="yh_address"    />
        <result property="yhAddressDetail"    column="yh_address_detail"    />
        <result property="yhWorkCorp"    column="yh_work_corp"    />
        <result property="techTitle"    column="tech_title"    />
        <result property="businessCode"    column="business_code"    />
    </resultMap>

    <sql id="selectIndWorkVo">
        select id, customer_id, department, occupation_type, headship_type, work_status, work_corp, company_type, province, city, borough, address, address_detail, establish_year, work_year, business_register, duty_type, work_zip_code, create_by, create_date, update_by, update_date, hr_name, hr_mobile_phone, industry_owned, work_trade, remark, del_flag, yh_province, yh_city, yh_borough, yh_address, yh_address_detail, yh_work_corp, tech_title, business_code from ind_work
    </sql>

    <select id="selectIndWorkList" parameterType="IndWork" resultMap="IndWorkResult">
        <include refid="selectIndWorkVo"/>
        <where>  
            <if test="customerId != null  and customerId != ''"> and customer_id = #{customerId}</if>
            <if test="department != null  and department != ''"> and department = #{department}</if>
            <if test="occupationType != null  and occupationType != ''"> and occupation_type = #{occupationType}</if>
            <if test="headshipType != null  and headshipType != ''"> and headship_type = #{headshipType}</if>
            <if test="workStatus != null  and workStatus != ''"> and work_status = #{workStatus}</if>
            <if test="workCorp != null  and workCorp != ''"> and work_corp = #{workCorp}</if>
            <if test="companyType != null  and companyType != ''"> and company_type = #{companyType}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="borough != null  and borough != ''"> and borough = #{borough}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="addressDetail != null  and addressDetail != ''"> and address_detail = #{addressDetail}</if>
            <if test="establishYear != null "> and establish_year = #{establishYear}</if>
            <if test="workYear != null "> and work_year = #{workYear}</if>
            <if test="businessRegister != null  and businessRegister != ''"> and business_register = #{businessRegister}</if>
            <if test="dutyType != null  and dutyType != ''"> and duty_type = #{dutyType}</if>
            <if test="workZipCode != null  and workZipCode != ''"> and work_zip_code = #{workZipCode}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="hrName != null  and hrName != ''"> and hr_name like concat('%', #{hrName}, '%')</if>
            <if test="hrMobilePhone != null  and hrMobilePhone != ''"> and hr_mobile_phone = #{hrMobilePhone}</if>
            <if test="industryOwned != null  and industryOwned != ''"> and industry_owned = #{industryOwned}</if>
            <if test="workTrade != null  and workTrade != ''"> and work_trade = #{workTrade}</if>
            <if test="yhProvince != null  and yhProvince != ''"> and yh_province = #{yhProvince}</if>
            <if test="yhCity != null  and yhCity != ''"> and yh_city = #{yhCity}</if>
            <if test="yhBorough != null  and yhBorough != ''"> and yh_borough = #{yhBorough}</if>
            <if test="yhAddress != null  and yhAddress != ''"> and yh_address = #{yhAddress}</if>
            <if test="yhAddressDetail != null  and yhAddressDetail != ''"> and yh_address_detail = #{yhAddressDetail}</if>
            <if test="yhWorkCorp != null  and yhWorkCorp != ''"> and yh_work_corp = #{yhWorkCorp}</if>
            <if test="techTitle != null  and techTitle != ''"> and tech_title = #{techTitle}</if>
            <if test="businessCode != null  and businessCode != ''"> and business_code = #{businessCode}</if>
        </where>
    </select>
    
    <select id="selectIndWorkById" parameterType="String" resultMap="IndWorkResult">
        <include refid="selectIndWorkVo"/>
        where id = #{id}
    </select>

    <insert id="insertIndWork" parameterType="IndWork">
        insert into ind_work
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerId != null and customerId != ''">customer_id,</if>
            <if test="department != null">department,</if>
            <if test="occupationType != null">occupation_type,</if>
            <if test="headshipType != null">headship_type,</if>
            <if test="workStatus != null">work_status,</if>
            <if test="workCorp != null">work_corp,</if>
            <if test="companyType != null">company_type,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="borough != null">borough,</if>
            <if test="address != null">address,</if>
            <if test="addressDetail != null">address_detail,</if>
            <if test="establishYear != null">establish_year,</if>
            <if test="workYear != null">work_year,</if>
            <if test="businessRegister != null">business_register,</if>
            <if test="dutyType != null">duty_type,</if>
            <if test="workZipCode != null">work_zip_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="hrName != null">hr_name,</if>
            <if test="hrMobilePhone != null">hr_mobile_phone,</if>
            <if test="industryOwned != null">industry_owned,</if>
            <if test="workTrade != null">work_trade,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="yhProvince != null">yh_province,</if>
            <if test="yhCity != null">yh_city,</if>
            <if test="yhBorough != null">yh_borough,</if>
            <if test="yhAddress != null">yh_address,</if>
            <if test="yhAddressDetail != null">yh_address_detail,</if>
            <if test="yhWorkCorp != null">yh_work_corp,</if>
            <if test="techTitle != null">tech_title,</if>
            <if test="businessCode != null">business_code,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerId != null and customerId != ''">#{customerId},</if>
            <if test="department != null">#{department},</if>
            <if test="occupationType != null">#{occupationType},</if>
            <if test="headshipType != null">#{headshipType},</if>
            <if test="workStatus != null">#{workStatus},</if>
            <if test="workCorp != null">#{workCorp},</if>
            <if test="companyType != null">#{companyType},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="borough != null">#{borough},</if>
            <if test="address != null">#{address},</if>
            <if test="addressDetail != null">#{addressDetail},</if>
            <if test="establishYear != null">#{establishYear},</if>
            <if test="workYear != null">#{workYear},</if>
            <if test="businessRegister != null">#{businessRegister},</if>
            <if test="dutyType != null">#{dutyType},</if>
            <if test="workZipCode != null">#{workZipCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="hrName != null">#{hrName},</if>
            <if test="hrMobilePhone != null">#{hrMobilePhone},</if>
            <if test="industryOwned != null">#{industryOwned},</if>
            <if test="workTrade != null">#{workTrade},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="yhProvince != null">#{yhProvince},</if>
            <if test="yhCity != null">#{yhCity},</if>
            <if test="yhBorough != null">#{yhBorough},</if>
            <if test="yhAddress != null">#{yhAddress},</if>
            <if test="yhAddressDetail != null">#{yhAddressDetail},</if>
            <if test="yhWorkCorp != null">#{yhWorkCorp},</if>
            <if test="techTitle != null">#{techTitle},</if>
            <if test="businessCode != null">#{businessCode},</if>
         </trim>
    </insert>

    <update id="updateIndWork" parameterType="IndWork">
        update ind_work
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null and customerId != ''">customer_id = #{customerId},</if>
            <if test="department != null">department = #{department},</if>
            <if test="occupationType != null">occupation_type = #{occupationType},</if>
            <if test="headshipType != null">headship_type = #{headshipType},</if>
            <if test="workStatus != null">work_status = #{workStatus},</if>
            <if test="workCorp != null">work_corp = #{workCorp},</if>
            <if test="companyType != null">company_type = #{companyType},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="borough != null">borough = #{borough},</if>
            <if test="address != null">address = #{address},</if>
            <if test="addressDetail != null">address_detail = #{addressDetail},</if>
            <if test="establishYear != null">establish_year = #{establishYear},</if>
            <if test="workYear != null">work_year = #{workYear},</if>
            <if test="businessRegister != null">business_register = #{businessRegister},</if>
            <if test="dutyType != null">duty_type = #{dutyType},</if>
            <if test="workZipCode != null">work_zip_code = #{workZipCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="hrName != null">hr_name = #{hrName},</if>
            <if test="hrMobilePhone != null">hr_mobile_phone = #{hrMobilePhone},</if>
            <if test="industryOwned != null">industry_owned = #{industryOwned},</if>
            <if test="workTrade != null">work_trade = #{workTrade},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="yhProvince != null">yh_province = #{yhProvince},</if>
            <if test="yhCity != null">yh_city = #{yhCity},</if>
            <if test="yhBorough != null">yh_borough = #{yhBorough},</if>
            <if test="yhAddress != null">yh_address = #{yhAddress},</if>
            <if test="yhAddressDetail != null">yh_address_detail = #{yhAddressDetail},</if>
            <if test="yhWorkCorp != null">yh_work_corp = #{yhWorkCorp},</if>
            <if test="techTitle != null">tech_title = #{techTitle},</if>
            <if test="businessCode != null">business_code = #{businessCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIndWorkById" parameterType="String">
        delete from ind_work where id = #{id}
    </delete>

    <delete id="deleteIndWorkByIds" parameterType="String">
        delete from ind_work where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>