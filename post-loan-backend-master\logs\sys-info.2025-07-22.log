08:36:00.473 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
08:36:00.478 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 6244 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
08:36:00.482 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
08:36:05.445 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
08:36:05.447 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
08:36:05.448 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
08:36:05.515 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
08:36:07.288 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
08:36:08.232 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
08:36:11.341 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
08:36:11.353 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
08:36:11.354 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
08:36:11.355 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
08:36:11.356 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

08:36:11.356 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
08:36:11.356 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
08:36:11.356 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3788ec15
08:36:12.239 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
08:36:12.602 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
08:36:12.610 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 12.474 seconds (JVM running for 13.267)
08:38:01.643 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:38:04.137 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
08:40:33.286 [http-nio-8081-exec-6] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
08:40:42.634 [http-nio-8081-exec-10] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
08:41:46.790 [schedule-pool-1] INFO  sys-user - [run,55] - [************]内网IP[yidianadmin][Success][登录成功]
08:43:55.782 [schedule-pool-2] INFO  sys-user - [run,55] - [************]内网IP[yidianadmin][Success][登录成功]
08:44:32.815 [http-nio-8081-exec-40] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
08:44:48.686 [http-nio-8081-exec-43] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
08:53:16.702 [http-nio-8081-exec-61] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
08:57:56.085 [http-nio-8081-exec-82] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:00:28.322 [http-nio-8081-exec-6] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:01:52.495 [http-nio-8081-exec-20] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:05:23.022 [http-nio-8081-exec-33] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:06:05.269 [http-nio-8081-exec-41] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:06:13.958 [http-nio-8081-exec-50] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:06:14.976 [http-nio-8081-exec-54] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:06:20.877 [http-nio-8081-exec-58] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:07:39.881 [http-nio-8081-exec-76] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:08:07.072 [http-nio-8081-exec-90] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:08:20.253 [http-nio-8081-exec-3] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:08:44.648 [http-nio-8081-exec-17] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:09:09.889 [http-nio-8081-exec-32] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:09:22.428 [http-nio-8081-exec-46] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:15:04.678 [http-nio-8081-exec-72] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:15:07.287 [http-nio-8081-exec-86] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:17:45.696 [http-nio-8081-exec-92] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:17:52.840 [http-nio-8081-exec-97] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:23:00.322 [http-nio-8081-exec-27] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:23:00.489 [http-nio-8081-exec-28] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:25:21.372 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:25:21.376 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 22068 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
09:25:21.378 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:25:25.730 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
09:25:25.733 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:25:25.733 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:25:25.811 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:25:27.572 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:25:28.254 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:25:28.919 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
09:25:28.924 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
09:25:28.925 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
09:25:28.926 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
09:25:28.936 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
09:25:44.227 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 23700 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
09:25:44.231 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:25:44.232 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:25:46.665 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
09:25:46.667 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:25:46.668 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:25:46.741 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:25:48.462 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:25:49.162 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:25:49.623 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
09:25:49.638 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
09:25:49.639 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
09:25:49.640 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
09:25:49.673 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
09:29:07.453 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:29:07.452 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 22572 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
09:29:07.455 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:29:09.794 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
09:29:09.798 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:29:09.798 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:29:09.874 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:29:11.613 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:29:12.429 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:29:15.634 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:29:15.646 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:29:15.647 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:29:15.648 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:29:15.648 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:29:15.648 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:29:15.648 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:29:15.649 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@160d07db
09:29:16.499 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
09:29:16.832 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:29:16.850 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.74 seconds (JVM running for 10.223)
09:29:25.104 [http-nio-8081-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:29:27.321 [http-nio-8081-exec-3] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:29:35.629 [http-nio-8081-exec-6] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:30:02.614 [http-nio-8081-exec-7] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:30:28.248 [http-nio-8081-exec-10] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:31:16.179 [http-nio-8081-exec-12] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:32:04.579 [http-nio-8081-exec-15] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:34:49.965 [http-nio-8081-exec-19] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:46:19.760 [schedule-pool-1] INFO  sys-user - [run,55] - [************]内网IP[yidianadmin][Success][登录成功]
09:46:28.362 [http-nio-8081-exec-44] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
09:46:37.033 [http-nio-8081-exec-46] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
10:49:20.112 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 2868 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
10:49:20.115 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:49:20.115 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:49:22.564 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
10:49:22.567 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:49:22.567 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:49:22.669 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:49:24.421 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:49:25.170 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:49:28.176 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:49:28.184 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:49:28.184 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:49:28.185 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:49:28.185 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:49:28.185 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:49:28.185 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:49:28.186 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@35b254a0
10:49:29.077 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
10:49:29.380 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:49:29.392 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.622 seconds (JVM running for 10.08)
10:49:31.738 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:17:17.523 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25060 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
11:17:17.525 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:17:17.527 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
11:17:20.209 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
11:17:20.213 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
11:17:20.213 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
11:17:20.306 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
11:17:22.259 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
11:17:22.937 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
11:17:25.940 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:17:25.948 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:17:25.948 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:17:25.949 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
11:17:25.950 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:17:25.950 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:17:25.950 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:17:25.950 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@26db52c
11:17:26.940 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
11:17:27.250 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
11:17:27.260 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.138 seconds (JVM running for 10.885)
11:32:53.727 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:37:43.764 [http-nio-8081-exec-24] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
12:31:53.655 [http-nio-8081-exec-30] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
12:32:27.408 [http-nio-8081-exec-36] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
12:32:47.263 [http-nio-8081-exec-41] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
12:34:05.565 [http-nio-8081-exec-47] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
12:34:32.607 [http-nio-8081-exec-52] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
12:36:34.528 [schedule-pool-1] INFO  sys-user - [run,55] - [************]内网IP[yidianadmin][Success][登录成功]
12:36:54.125 [http-nio-8081-exec-66] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
12:39:03.884 [http-nio-8081-exec-82] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
12:39:30.899 [http-nio-8081-exec-86] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
12:40:06.311 [http-nio-8081-exec-94] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
12:40:38.737 [http-nio-8081-exec-3] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
12:44:00.324 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 19692 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
12:44:00.327 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
12:44:00.328 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:44:02.980 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
12:44:02.983 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
12:44:02.983 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
12:44:03.064 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
12:44:05.128 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
12:44:05.821 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
12:44:08.799 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:44:08.809 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:44:08.809 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:44:08.810 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
12:44:08.811 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

12:44:08.811 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
12:44:08.811 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:44:08.811 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@160d07db
12:44:09.824 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
12:44:09.832 [restartedMain] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
12:44:09.833 [restartedMain] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
12:44:09.833 [restartedMain] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
12:44:09.834 [restartedMain] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
12:44:09.845 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
12:44:09.852 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
12:44:09.853 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
12:44:09.854 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
12:44:09.986 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Pausing ProtocolHandler ["http-nio-8081"]
12:44:09.986 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
12:44:09.992 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Stopping ProtocolHandler ["http-nio-8081"]
12:44:09.992 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Destroying ProtocolHandler ["http-nio-8081"]
12:45:59.186 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 7924 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
12:45:59.190 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
12:45:59.192 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:46:01.498 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
12:46:01.502 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
12:46:01.502 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
12:46:01.579 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
12:46:03.254 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
12:46:03.957 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
12:46:06.825 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:46:06.842 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:46:06.842 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:46:06.843 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
12:46:06.843 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

12:46:06.844 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
12:46:06.844 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:46:06.844 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4ec6a579
12:46:07.750 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
12:46:08.156 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
12:46:08.167 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.305 seconds (JVM running for 9.788)
12:47:07.006 [http-nio-8081-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:54:15.525 [schedule-pool-1] INFO  sys-user - [run,55] - [************]内网IP[yidianadmin][Success][登录成功]
12:54:20.931 [http-nio-8081-exec-40] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
12:54:35.790 [http-nio-8081-exec-46] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
12:57:06.182 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 6796 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
12:57:06.189 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
12:57:06.189 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:57:08.590 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
12:57:08.593 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
12:57:08.593 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
12:57:08.673 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
12:57:10.548 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
12:57:11.233 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
12:57:14.096 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:57:14.104 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:57:14.104 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:57:14.105 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
12:57:14.106 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

12:57:14.106 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
12:57:14.106 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:57:14.107 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5b287551
12:57:15.039 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
12:57:15.388 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
12:57:15.396 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.562 seconds (JVM running for 10.016)
12:57:50.886 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:02:52.569 [http-nio-8081-exec-18] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:02:58.349 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 6200 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
13:02:58.353 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:02:58.354 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:03:00.681 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:03:00.683 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:03:00.684 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:03:00.759 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:03:02.541 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:03:03.260 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:03:06.084 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:03:06.092 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:03:06.092 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:03:06.093 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:03:06.093 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:03:06.094 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:03:06.094 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:03:06.094 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6d1b850e
13:03:06.960 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:03:07.307 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:03:07.316 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.301 seconds (JVM running for 9.748)
13:03:12.803 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:03:15.202 [http-nio-8081-exec-3] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:03:20.251 [http-nio-8081-exec-6] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:04:03.871 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 22612 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
13:04:03.873 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:04:03.874 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:04:06.293 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:04:06.295 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:04:06.295 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:04:06.371 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:04:08.029 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:04:08.729 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:04:11.521 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:04:11.532 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:04:11.533 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:04:11.535 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:04:11.536 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:04:11.537 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:04:11.537 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:04:11.537 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3a13bb06
13:04:12.385 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:04:12.686 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:04:12.695 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.15 seconds (JVM running for 9.598)
13:04:12.907 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:04:20.441 [http-nio-8081-exec-12] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:04:29.192 [http-nio-8081-exec-25] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:04:36.356 [http-nio-8081-exec-24] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:04:37.195 [http-nio-8081-exec-34] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:05:26.457 [http-nio-8081-exec-32] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:05:37.341 [http-nio-8081-exec-44] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:08:59.358 [http-nio-8081-exec-52] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:17:52.682 [http-nio-8081-exec-97] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:25:15.957 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 9948 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
13:25:15.961 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:25:15.965 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:25:18.245 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:25:18.248 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:25:18.248 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:25:18.324 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:25:19.999 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:25:20.702 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:25:23.493 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:25:23.502 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:25:23.502 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:25:23.503 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:25:23.504 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:25:23.504 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:25:23.504 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:25:23.504 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7af71dee
13:25:24.361 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:25:24.677 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:25:24.688 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.075 seconds (JVM running for 9.526)
13:25:28.735 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:25:28.927 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
13:26:15.186 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
13:27:21.395 [http-nio-8081-exec-31] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:29:00.161 [http-nio-8081-exec-35] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:29:20.490 [http-nio-8081-exec-39] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:34:57.278 [http-nio-8081-exec-42] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:35:57.523 [http-nio-8081-exec-43] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:36:15.232 [http-nio-8081-exec-44] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:37:11.662 [http-nio-8081-exec-49] INFO  c.r.f.d.DynamicDataSourceContextHolder - [setDataSourceType,26] - 切换到SLAVE数据源
13:37:15.447 [http-nio-8081-exec-52] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:37:18.189 [http-nio-8081-exec-53] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:37:23.939 [http-nio-8081-exec-54] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:37:29.046 [http-nio-8081-exec-55] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:37:31.720 [http-nio-8081-exec-56] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:37:36.173 [http-nio-8081-exec-57] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:37:43.862 [http-nio-8081-exec-58] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:37:44.601 [http-nio-8081-exec-59] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:37:45.074 [http-nio-8081-exec-60] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:37:45.814 [http-nio-8081-exec-61] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:37:54.985 [http-nio-8081-exec-62] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:40:08.484 [http-nio-8081-exec-70] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:40:09.427 [http-nio-8081-exec-71] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:40:11.358 [http-nio-8081-exec-74] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:40:15.649 [http-nio-8081-exec-75] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:40:17.017 [http-nio-8081-exec-76] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:41:44.710 [http-nio-8081-exec-92] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
13:42:42.435 [http-nio-8081-exec-2] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:42:47.585 [http-nio-8081-exec-5] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:46:02.403 [http-nio-8081-exec-8] INFO  c.r.l.c.LoanCompensationController - [detail,83] - 查询流程代偿详情:LoanCompensation(id=19, trialBalance=null, applyId=***************************, loanId=6, customerId=null, customerName=彭XX, salesman=出单员, orgName=衢州华沂分公司, partnerId=**********, bank=皖新租赁, loanAmount=48000.00, otherDebt=300.00, totalMoney=300.00, examineStatus=3, reason=null, createDate=Thu Jul 17 21:42:33 CST 2025, updateDate=Fri Jul 18 15:07:41 CST 2025, fxjProportion=15, qdProportion=25, gmjProportion=10, kjjProportion=12, kjczProportion=22, sbczProportion=16, fxjMoney=0.00, qdMoney=0.00, gmjMoney=0.00, kjjMoney=0.00, kjczMoney=0.00, sbczMoney=0.00, fxjAccount=345345, qdAccount=系统划扣, gmjAccount=345345, kjjAccount=系统划扣, kjczAccount=345345, sbczAccount=系统划扣, image=https://daihou-file.oss-cn-hangzhou.aliyuncs.com/2025/07/18/a35c9597-756b-4bb0-adb2-e77e54eaf0ac.png, payouts=null, payoutsTime=null)
13:46:06.816 [http-nio-8081-exec-12] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:46:24.972 [http-nio-8081-exec-15] INFO  c.r.f.d.DynamicDataSourceContextHolder - [setDataSourceType,26] - 切换到SLAVE数据源
13:46:35.406 [http-nio-8081-exec-18] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:46:36.790 [http-nio-8081-exec-19] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:46:39.490 [http-nio-8081-exec-20] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:46:40.015 [http-nio-8081-exec-22] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:46:42.633 [http-nio-8081-exec-21] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:46:47.983 [http-nio-8081-exec-23] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:46:54.138 [http-nio-8081-exec-24] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:46:59.030 [http-nio-8081-exec-25] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:47:01.084 [http-nio-8081-exec-26] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:47:01.654 [http-nio-8081-exec-27] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:47:06.852 [http-nio-8081-exec-28] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:47:07.626 [http-nio-8081-exec-29] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:47:10.031 [http-nio-8081-exec-30] INFO  c.r.l.c.LoanCompensationController - [detail,83] - 查询流程代偿详情:LoanCompensation(id=19, trialBalance=null, applyId=***************************, loanId=6, customerId=null, customerName=彭XX, salesman=出单员, orgName=衢州华沂分公司, partnerId=**********, bank=皖新租赁, loanAmount=48000.00, otherDebt=300.00, totalMoney=300.00, examineStatus=3, reason=null, createDate=Thu Jul 17 21:42:33 CST 2025, updateDate=Fri Jul 18 15:07:41 CST 2025, fxjProportion=15, qdProportion=25, gmjProportion=10, kjjProportion=12, kjczProportion=22, sbczProportion=16, fxjMoney=0.00, qdMoney=0.00, gmjMoney=0.00, kjjMoney=0.00, kjczMoney=0.00, sbczMoney=0.00, fxjAccount=345345, qdAccount=系统划扣, gmjAccount=345345, kjjAccount=系统划扣, kjczAccount=345345, sbczAccount=系统划扣, image=https://daihou-file.oss-cn-hangzhou.aliyuncs.com/2025/07/18/a35c9597-756b-4bb0-adb2-e77e54eaf0ac.png, payouts=null, payoutsTime=null)
13:47:17.950 [http-nio-8081-exec-32] INFO  c.r.f.d.DynamicDataSourceContextHolder - [setDataSourceType,26] - 切换到SLAVE数据源
13:47:58.356 [http-nio-8081-exec-35] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:48:03.038 [http-nio-8081-exec-40] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:49:14.038 [http-nio-8081-exec-55] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:52:01.628 [http-nio-8081-exec-76] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
13:57:46.957 [http-nio-8081-exec-89] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:00:06.830 [http-nio-8081-exec-95] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:03:32.413 [http-nio-8081-exec-96] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:06:16.003 [http-nio-8081-exec-3] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:06:21.231 [http-nio-8081-exec-6] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:06:23.484 [http-nio-8081-exec-9] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:06:25.773 [http-nio-8081-exec-7] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:06:29.067 [http-nio-8081-exec-8] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:06:32.368 [http-nio-8081-exec-10] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:06:38.748 [http-nio-8081-exec-11] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:06:50.041 [http-nio-8081-exec-13] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:06:55.956 [http-nio-8081-exec-14] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:07:14.733 [http-nio-8081-exec-12] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:08:06.195 [http-nio-8081-exec-17] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:08:09.068 [http-nio-8081-exec-20] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:08:21.268 [http-nio-8081-exec-22] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:08:28.434 [http-nio-8081-exec-21] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:09:56.761 [http-nio-8081-exec-26] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:10:02.938 [http-nio-8081-exec-28] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:10:06.733 [http-nio-8081-exec-29] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:10:26.817 [http-nio-8081-exec-30] INFO  c.r.l.c.LoanCompensationController - [detail,83] - 查询流程代偿详情:LoanCompensation(id=19, trialBalance=null, applyId=***************************, loanId=6, customerId=null, customerName=彭XX, salesman=出单员, orgName=衢州华沂分公司, partnerId=**********, bank=皖新租赁, loanAmount=48000.00, otherDebt=300.00, totalMoney=300.00, examineStatus=3, reason=null, createDate=Thu Jul 17 21:42:33 CST 2025, updateDate=Fri Jul 18 15:07:41 CST 2025, fxjProportion=15, qdProportion=25, gmjProportion=10, kjjProportion=12, kjczProportion=22, sbczProportion=16, fxjMoney=0.00, qdMoney=0.00, gmjMoney=0.00, kjjMoney=0.00, kjczMoney=0.00, sbczMoney=0.00, fxjAccount=345345, qdAccount=系统划扣, gmjAccount=345345, kjjAccount=系统划扣, kjczAccount=345345, sbczAccount=系统划扣, image=https://daihou-file.oss-cn-hangzhou.aliyuncs.com/2025/07/18/a35c9597-756b-4bb0-adb2-e77e54eaf0ac.png, payouts=null, payoutsTime=null)
14:10:45.073 [http-nio-8081-exec-32] INFO  c.r.f.d.DynamicDataSourceContextHolder - [setDataSourceType,26] - 切换到SLAVE数据源
14:11:34.555 [http-nio-8081-exec-42] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:11:37.892 [http-nio-8081-exec-43] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:11:39.324 [http-nio-8081-exec-44] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:11:45.723 [http-nio-8081-exec-46] INFO  c.r.s.c.SysOfficeController - [dh_assign,126] - 修改机构里面的贷后文员：SysOffice(id=2019123114153701099163648, loanUser=贷后文员, legalUser=null, loanName=null, legalName=null, loanTime=Tue Jul 22 14:11:45 CST 2025, legalTime=null, parentId=null, parentIds=null, name=null, status=null, sort=null, areaId=null, code=null, type=null, grade=null, address=null, zipCode=null, master=null, phone=null, fax=null, email=null, useable=null, primaryPerson=null, deputyPerson=null, remarks=null, createDate=null, updateDate=null, delFlag=null, fullName=null, courtName=null, areaName=null, signFlag=null, gpsScheme=null)
14:11:45.811 [http-nio-8081-exec-46] INFO  c.r.s.c.SysOfficeController - [dh_assign,126] - 修改机构里面的贷后文员：SysOffice(id=2019123114153907170103296, loanUser=贷后文员, legalUser=null, loanName=null, legalName=null, loanTime=Tue Jul 22 14:11:45 CST 2025, legalTime=null, parentId=null, parentIds=null, name=null, status=null, sort=null, areaId=null, code=null, type=null, grade=null, address=null, zipCode=null, master=null, phone=null, fax=null, email=null, useable=null, primaryPerson=null, deputyPerson=null, remarks=null, createDate=null, updateDate=null, delFlag=null, fullName=null, courtName=null, areaName=null, signFlag=null, gpsScheme=null)
14:11:45.928 [http-nio-8081-exec-47] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
14:16:13.845 [http-nio-8081-exec-54] INFO  c.r.f.d.DynamicDataSourceContextHolder - [setDataSourceType,26] - 切换到SLAVE数据源
14:19:51.684 [http-nio-8081-exec-67] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:20:50.970 [http-nio-8081-exec-82] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:24:38.490 [http-nio-8081-exec-94] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:25:00.880 [http-nio-8081-exec-100] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:27:05.783 [http-nio-8081-exec-5] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:27:47.461 [http-nio-8081-exec-10] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:28:31.138 [http-nio-8081-exec-20] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:31:58.345 [http-nio-8081-exec-30] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:33:25.725 [http-nio-8081-exec-36] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:35:57.284 [http-nio-8081-exec-48] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:36:47.664 [http-nio-8081-exec-53] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:38:00.216 [http-nio-8081-exec-64] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:38:06.046 [http-nio-8081-exec-71] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:43:36.885 [http-nio-8081-exec-82] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:45:39.674 [http-nio-8081-exec-90] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:45:51.061 [http-nio-8081-exec-92] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@10f56271
14:46:34.220 [http-nio-8081-exec-93] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@49c7b3df
14:46:38.505 [http-nio-8081-exec-98] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@1bfd2984
14:48:01.142 [http-nio-8081-exec-6] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:48:10.600 [http-nio-8081-exec-10] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@5e69a00c
14:48:13.402 [http-nio-8081-exec-11] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@17247896
14:50:41.264 [http-nio-8081-exec-21] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:50:51.921 [http-nio-8081-exec-25] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@10fcee37
14:53:48.035 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 16580 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
14:53:48.038 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:53:48.040 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:53:50.330 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:53:50.333 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:53:50.334 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:53:50.408 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:53:52.094 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:53:52.800 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:53:55.596 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:53:55.603 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:53:55.603 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:53:55.603 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:53:55.604 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:53:55.604 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:53:55.604 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:53:55.605 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2289c3e8
14:53:56.523 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:53:56.936 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:53:56.950 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.253 seconds (JVM running for 9.695)
14:54:04.419 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:54:45.412 [http-nio-8081-exec-4] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:57:38.192 [http-nio-8081-exec-8] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:57:50.694 [http-nio-8081-exec-11] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@74078cb4
14:58:27.245 [http-nio-8081-exec-17] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
14:58:50.456 [http-nio-8081-exec-19] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@36c8379a
14:59:02.468 [http-nio-8081-exec-20] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@619e4766
14:59:56.855 [http-nio-8081-exec-24] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:00:07.341 [http-nio-8081-exec-27] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@51467b15
15:03:42.895 [http-nio-8081-exec-37] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:03:53.628 [http-nio-8081-exec-40] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@9ba7062
15:06:15.438 [http-nio-8081-exec-45] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:06:23.480 [http-nio-8081-exec-48] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@2ad1cb4f
15:09:36.630 [http-nio-8081-exec-63] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:09:49.841 [http-nio-8081-exec-66] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@1911932d
15:09:54.321 [http-nio-8081-exec-67] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@6331d9ad
15:10:12.292 [http-nio-8081-exec-73] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:10:20.271 [http-nio-8081-exec-76] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@19c6cb09
15:10:22.845 [http-nio-8081-exec-77] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@1b8e252c
15:13:29.520 [http-nio-8081-exec-88] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:14:09.238 [http-nio-8081-exec-93] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:16:26.620 [http-nio-8081-exec-3] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:17:29.338 [http-nio-8081-exec-8] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:17:57.299 [http-nio-8081-exec-13] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:21:13.588 [http-nio-8081-exec-26] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:22:59.288 [http-nio-8081-exec-31] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:23:45.165 [http-nio-8081-exec-37] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:32:01.843 [http-nio-8081-exec-52] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:33:16.249 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 14848 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
15:33:16.252 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:33:16.253 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:33:18.573 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
15:33:18.575 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:33:18.576 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:33:18.653 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:33:20.288 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:33:20.965 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
15:33:23.729 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:33:23.738 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:33:23.738 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:33:23.739 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:33:23.739 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:33:23.739 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:33:23.740 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:33:23.740 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@160d07db
15:33:24.628 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
15:33:24.956 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:33:24.967 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.053 seconds (JVM running for 9.491)
15:33:29.174 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:33:30.246 [http-nio-8081-exec-4] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:34:30.381 [http-nio-8081-exec-15] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:34:47.646 [http-nio-8081-exec-18] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:36:39.705 [http-nio-8081-exec-21] INFO  c.r.l.c.LoanCompensationController - [detail,83] - 查询流程代偿详情:LoanCompensation(id=19, trialBalance=null, applyId=***************************, loanId=6, customerId=null, customerName=彭XX, salesman=出单员, orgName=衢州华沂分公司, partnerId=**********, bank=皖新租赁, loanAmount=48000.00, otherDebt=300.00, totalMoney=300.00, examineStatus=3, reason=null, createDate=Thu Jul 17 21:42:33 CST 2025, updateDate=Fri Jul 18 15:07:41 CST 2025, fxjProportion=15, qdProportion=25, gmjProportion=10, kjjProportion=12, kjczProportion=22, sbczProportion=16, fxjMoney=0.00, qdMoney=0.00, gmjMoney=0.00, kjjMoney=0.00, kjczMoney=0.00, sbczMoney=0.00, fxjAccount=345345, qdAccount=系统划扣, gmjAccount=345345, kjjAccount=系统划扣, kjczAccount=345345, sbczAccount=系统划扣, image=https://daihou-file.oss-cn-hangzhou.aliyuncs.com/2025/07/18/a35c9597-756b-4bb0-adb2-e77e54eaf0ac.png, payouts=null, payoutsTime=null)
15:36:48.677 [http-nio-8081-exec-22] INFO  c.r.l.c.LoanCompensationController - [detail,83] - 查询流程代偿详情:null
15:36:48.739 [http-nio-8081-exec-23] INFO  c.r.l.c.LoanCompensationController - [initiateCompensation,136] - 发起代偿申请，贷款ID: 4
15:36:48.771 [http-nio-8081-exec-23] INFO  c.r.l.s.i.LoanCompensationServiceImpl - [initiateCompensation,144] - 发起代偿前，贷款ID: 4, 当前贷后还款状态: 6
15:36:48.771 [http-nio-8081-exec-23] INFO  c.r.a.s.i.AccountLoanServiceImpl - [updateRepaymentStatus,145] - 开始更新贷后还款状态, 贷款ID: 4, 目标状态: 8
15:36:48.879 [http-nio-8081-exec-23] INFO  c.r.a.s.i.AccountLoanServiceImpl - [updateRepaymentStatus,171] - 更新贷后还款状态结果: 成功, 贷款ID: 4, 目标状态: 8
15:36:48.899 [http-nio-8081-exec-23] INFO  c.r.l.s.i.LoanCompensationServiceImpl - [initiateCompensation,167] - 状态更新结果，贷款ID: 4, 原状态: 6, 新状态: 8
15:36:48.950 [http-nio-8081-exec-23] INFO  c.r.l.s.i.LoanCompensationServiceImpl - [initiateCompensation,186] - 发起代偿成功，贷款ID: 4, 代偿ID: 23
15:37:09.186 [http-nio-8081-exec-26] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:37:18.227 [http-nio-8081-exec-29] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@144c5776
15:38:33.807 [http-nio-8081-exec-31] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:38:52.752 [http-nio-8081-exec-35] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:39:05.087 [http-nio-8081-exec-38] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 11
15:44:35.270 [http-nio-8081-exec-47] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:48:22.579 [http-nio-8081-exec-54] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:49:11.594 [http-nio-8081-exec-57] INFO  c.r.l.c.LoanCompensationController - [detail,83] - 查询流程代偿详情:LoanCompensation(id=20, trialBalance=null, applyId=BA2024052023227293803765760, loanId=2, customerId=null, customerName=袁XX, salesman=出单员, orgName=衢州华沂分公司, partnerId=IO00000006, bank=浙商银行, loanAmount=48000.00, otherDebt=null, totalMoney=null, examineStatus=0, reason=null, createDate=Thu Jul 17 22:06:00 CST 2025, updateDate=null, fxjProportion=null, qdProportion=null, gmjProportion=null, kjjProportion=null, kjczProportion=null, sbczProportion=null, fxjMoney=null, qdMoney=null, gmjMoney=null, kjjMoney=null, kjczMoney=null, sbczMoney=null, fxjAccount=null, qdAccount=null, gmjAccount=null, kjjAccount=null, kjczAccount=null, sbczAccount=null, image=null, payouts=null, payoutsTime=null)
15:49:16.960 [http-nio-8081-exec-58] INFO  c.r.l.c.LoanCompensationController - [detail,83] - 查询流程代偿详情:LoanCompensation(id=23, trialBalance=null, applyId=***************************, loanId=4, customerId=null, customerName=陈XX, salesman=出单员, orgName=衢州华沂分公司, partnerId=IO00000008, bank=蓝海银行, loanAmount=48000.00, otherDebt=300.00, totalMoney=300.00, examineStatus=1, reason=null, createDate=Tue Jul 22 15:36:49 CST 2025, updateDate=Tue Jul 22 15:37:27 CST 2025, fxjProportion=10, qdProportion=10, gmjProportion=10, kjjProportion=10, kjczProportion=10, sbczProportion=50, fxjMoney=0.00, qdMoney=0.00, gmjMoney=0.00, kjjMoney=0.00, kjczMoney=0.00, sbczMoney=0.00, fxjAccount=567567, qdAccount=567567, gmjAccount=系统划扣, kjjAccount=系统划扣, kjczAccount=345345, sbczAccount=567567, image=https://daihou-file.oss-cn-hangzhou.aliyuncs.com/2025/07/22/444a8462-3563-423c-8c52-448386f79940.png, payouts=null, payoutsTime=null)
15:49:43.952 [http-nio-8081-exec-62] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:50:34.180 [http-nio-8081-exec-66] INFO  c.r.l.c.LoanCompensationController - [detail,83] - 查询流程代偿详情:LoanCompensation(id=19, trialBalance=null, applyId=***************************, loanId=6, customerId=null, customerName=彭XX, salesman=出单员, orgName=衢州华沂分公司, partnerId=**********, bank=皖新租赁, loanAmount=48000.00, otherDebt=300.00, totalMoney=300.00, examineStatus=3, reason=null, createDate=Thu Jul 17 21:42:33 CST 2025, updateDate=Fri Jul 18 15:07:41 CST 2025, fxjProportion=15, qdProportion=25, gmjProportion=10, kjjProportion=12, kjczProportion=22, sbczProportion=16, fxjMoney=0.00, qdMoney=0.00, gmjMoney=0.00, kjjMoney=0.00, kjczMoney=0.00, sbczMoney=0.00, fxjAccount=345345, qdAccount=系统划扣, gmjAccount=345345, kjjAccount=系统划扣, kjczAccount=345345, sbczAccount=系统划扣, image=https://daihou-file.oss-cn-hangzhou.aliyuncs.com/2025/07/18/a35c9597-756b-4bb0-adb2-e77e54eaf0ac.png, payouts=null, payoutsTime=null)
15:50:59.490 [http-nio-8081-exec-67] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@684f3ba
15:53:34.035 [http-nio-8081-exec-74] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:54:38.977 [http-nio-8081-exec-81] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:54:40.361 [http-nio-8081-exec-85] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:54:43.937 [http-nio-8081-exec-92] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:56:01.391 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 18844 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
15:56:01.392 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:56:01.394 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:56:03.716 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
15:56:03.719 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:56:03.719 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:56:03.797 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:56:05.558 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:56:06.297 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
15:56:09.015 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:56:09.022 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:56:09.022 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:56:09.023 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:56:09.025 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:56:09.026 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:56:09.026 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:56:09.026 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5b287551
15:56:09.884 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
15:56:10.216 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:56:10.226 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.151 seconds (JVM running for 9.603)
15:56:11.122 [http-nio-8081-exec-7] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:56:12.279 [http-nio-8081-exec-6] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:56:17.243 [http-nio-8081-exec-14] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
15:59:40.894 [http-nio-8081-exec-16] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
16:00:05.731 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 10544 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
16:00:05.735 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
16:00:05.735 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:00:08.212 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
16:00:08.215 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
16:00:08.215 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
16:00:08.295 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
16:00:10.047 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
16:00:10.789 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
16:00:13.613 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
16:00:13.621 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
16:00:13.621 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
16:00:13.623 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
16:00:13.624 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

16:00:13.624 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
16:00:13.624 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
16:00:13.625 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7ee313a4
16:00:14.516 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
16:00:14.972 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
16:00:14.985 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.574 seconds (JVM running for 10.024)
16:00:20.167 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:00:22.804 [http-nio-8081-exec-3] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
16:03:57.501 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:03:57.501 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 4036 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
16:03:57.504 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
16:03:59.803 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
16:03:59.805 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
16:03:59.806 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
16:03:59.888 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
16:04:01.647 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
16:04:02.350 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
16:04:05.147 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
16:04:05.161 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
16:04:05.162 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
16:04:05.164 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
16:04:05.165 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

16:04:05.165 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
16:04:05.166 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
16:04:05.166 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3e46ae91
16:04:06.081 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
16:04:06.381 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
16:04:06.390 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.21 seconds (JVM running for 9.663)
16:04:10.621 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:04:11.464 [http-nio-8081-exec-4] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
16:04:15.629 [http-nio-8081-exec-8] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
16:04:43.071 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 27472 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
16:04:43.076 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
16:04:43.079 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:04:45.437 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
16:04:45.439 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
16:04:45.440 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
16:04:45.516 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
16:04:47.231 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
16:04:47.909 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
16:04:50.738 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
16:04:50.744 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
16:04:50.744 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
16:04:50.745 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
16:04:50.746 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

16:04:50.746 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
16:04:50.746 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
16:04:50.746 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4ec6a579
16:04:51.630 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
16:04:52.052 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
16:04:52.061 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.318 seconds (JVM running for 9.772)
16:05:01.992 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:05:02.825 [http-nio-8081-exec-3] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
16:07:30.065 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 15604 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
16:07:30.067 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:07:30.068 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
16:07:32.391 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
16:07:32.394 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
16:07:32.394 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
16:07:32.475 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
16:07:34.183 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
16:07:34.903 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
16:07:37.585 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
16:07:37.592 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
16:07:37.592 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
16:07:37.593 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
16:07:37.594 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

16:07:37.594 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
16:07:37.594 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
16:07:37.594 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2ce3f801
16:07:38.492 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
16:07:38.889 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
16:07:38.898 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.168 seconds (JVM running for 9.631)
16:07:47.469 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:07:49.870 [http-nio-8081-exec-3] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
16:08:59.885 [http-nio-8081-exec-8] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
16:16:01.788 [http-nio-8081-exec-13] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
16:18:18.901 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 8744 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
16:18:18.904 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:18:18.904 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
16:18:21.257 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
16:18:21.260 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
16:18:21.260 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
16:18:21.337 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
16:18:23.135 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
16:18:23.855 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
16:18:26.649 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
16:18:26.657 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
16:18:26.658 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
16:18:26.659 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
16:18:26.659 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

16:18:26.659 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
16:18:26.660 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
16:18:26.660 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5b287551
16:18:27.580 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
16:18:27.919 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
16:18:27.927 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.341 seconds (JVM running for 9.782)
16:18:35.242 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:18:39.804 [http-nio-8081-exec-3] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
16:21:55.019 [http-nio-8081-exec-8] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 1
16:23:02.824 [http-nio-8081-exec-14] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
16:46:45.211 [http-nio-8081-exec-59] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@6547e59e
16:55:34.771 [http-nio-8081-exec-95] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
16:56:32.093 [http-nio-8081-exec-6] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
17:15:06.566 [http-nio-8081-exec-58] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
17:15:19.257 [http-nio-8081-exec-65] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
17:15:38.976 [http-nio-8081-exec-81] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
17:17:59.832 [http-nio-8081-exec-88] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
17:18:35.909 [http-nio-8081-exec-96] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
17:22:52.949 [http-nio-8081-exec-42] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
17:23:27.875 [http-nio-8081-exec-47] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
17:28:31.799 [http-nio-8081-exec-58] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
17:33:52.221 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25844 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
17:33:52.225 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:33:52.225 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
17:33:54.616 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
17:33:54.618 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
17:33:54.619 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
17:33:54.697 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
17:33:56.449 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
17:33:57.159 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
17:33:59.958 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
17:33:59.965 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
17:33:59.966 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
17:33:59.967 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
17:33:59.968 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

17:33:59.968 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
17:33:59.969 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
17:33:59.969 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2289c3e8
17:34:00.903 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
17:34:01.242 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
17:34:01.253 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.373 seconds (JVM running for 9.826)
17:35:06.856 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:29:55.456 [schedule-pool-2] INFO  sys-user - [run,55] - [************]内网IP[yidianadmin][Success][登录成功]
18:31:09.970 [http-nio-8081-exec-57] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
18:31:52.377 [http-nio-8081-exec-65] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
18:32:53.221 [http-nio-8081-exec-71] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
18:36:02.521 [schedule-pool-4] INFO  sys-user - [run,55] - [************]内网IP[yidianadmin][Success][登录成功]
18:37:27.668 [http-nio-8081-exec-94] INFO  c.r.w.c.c.CommonController - [uploadToOss,118] - 上传文件开始:org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@73cfacce
18:39:06.987 [http-nio-8081-exec-1] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
18:44:46.524 [http-nio-8081-exec-18] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
18:44:49.243 [http-nio-8081-exec-19] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
18:44:54.539 [http-nio-8081-exec-20] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
18:44:56.243 [http-nio-8081-exec-21] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
18:44:57.951 [http-nio-8081-exec-22] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
18:46:01.039 [http-nio-8081-exec-48] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
18:46:43.951 [http-nio-8081-exec-55] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
18:59:49.745 [http-nio-8081-exec-16] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
18:59:51.354 [http-nio-8081-exec-14] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
19:00:40.742 [http-nio-8081-exec-18] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
19:04:09.676 [http-nio-8081-exec-27] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
19:04:57.202 [http-nio-8081-exec-31] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
19:05:25.436 [http-nio-8081-exec-34] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
19:05:34.382 [http-nio-8081-exec-36] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
19:07:39.179 [http-nio-8081-exec-51] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
19:07:41.202 [http-nio-8081-exec-53] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
19:07:43.753 [http-nio-8081-exec-52] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
19:07:45.521 [http-nio-8081-exec-54] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
19:07:58.880 [http-nio-8081-exec-60] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
19:09:46.007 [http-nio-8081-exec-64] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
19:20:53.987 [http-nio-8081-exec-89] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：customerId=PC0000000006
19:21:06.996 [http-nio-8081-exec-97] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
19:21:24.847 [http-nio-8081-exec-100] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：customerId=PC0000000006
19:22:23.998 [http-nio-8081-exec-2] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：customerId=PC0000000005
19:22:25.588 [http-nio-8081-exec-3] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：customerId=PC0000000004
19:22:27.958 [http-nio-8081-exec-5] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：customerId=PC0000000003
19:22:29.232 [http-nio-8081-exec-6] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：customerId=PC0000000002
19:22:31.425 [http-nio-8081-exec-8] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：customerId=PC0000000001
19:23:06.385 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 27448 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
19:23:06.387 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:23:06.388 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
19:23:08.736 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
19:23:08.739 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
19:23:08.739 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
19:23:08.819 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
19:23:10.635 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
19:23:11.399 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
19:23:14.291 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
19:23:14.316 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
19:23:14.316 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
19:23:14.317 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
19:23:14.318 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

19:23:14.318 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
19:23:14.318 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
19:23:14.319 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6aa7eeae
19:23:15.221 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
19:23:15.509 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
19:23:15.518 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.48 seconds (JVM running for 10.167)
19:23:33.282 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:25:21.870 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 21464 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
19:25:21.873 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:25:21.874 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
19:25:24.153 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
19:25:24.156 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
19:25:24.156 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
19:25:24.234 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
19:25:25.996 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
19:25:26.757 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
19:25:29.621 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
19:25:29.629 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
19:25:29.629 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
19:25:29.630 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
19:25:29.631 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

19:25:29.631 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
19:25:29.631 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
19:25:29.631 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@54d055a9
19:25:30.509 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
19:25:30.800 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
19:25:30.810 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.303 seconds (JVM running for 9.754)
19:25:34.342 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:30:07.199 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:30:07.197 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 9844 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
19:30:07.201 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
19:30:09.896 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
19:30:09.898 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
19:30:09.899 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
19:30:09.983 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
19:30:11.988 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
19:30:12.716 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
19:30:16.601 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
19:30:16.611 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
19:30:16.612 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
19:30:16.613 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
19:30:16.614 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

19:30:16.614 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
19:30:16.615 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
19:30:16.615 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6aa7eeae
19:30:17.705 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
19:30:18.041 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
19:30:18.050 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 11.244 seconds (JVM running for 11.824)
19:30:18.608 [http-nio-8081-exec-6] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:31:40.920 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25036 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
19:31:40.922 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:31:40.923 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
19:31:43.373 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
19:31:43.375 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
19:31:43.376 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
19:31:43.453 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
19:31:45.218 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
19:31:45.915 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
19:31:48.798 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
19:31:48.808 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
19:31:48.808 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
19:31:48.809 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
19:31:48.809 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

19:31:48.809 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
19:31:48.810 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
19:31:48.810 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@74a5447
19:31:49.767 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
19:31:50.060 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
19:31:50.068 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.522 seconds (JVM running for 10.014)
19:31:54.222 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:33:10.129 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25532 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
19:33:10.132 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:33:10.133 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
19:33:12.530 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
19:33:12.533 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
19:33:12.533 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
19:33:12.611 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
19:33:14.269 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
19:33:14.955 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
19:33:17.840 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
19:33:17.847 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
19:33:17.848 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
19:33:17.849 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
19:33:17.849 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

19:33:17.849 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
19:33:17.849 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
19:33:17.850 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@74a5447
19:33:18.771 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
19:33:19.213 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
19:33:19.222 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.425 seconds (JVM running for 9.87)
19:33:25.201 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:36:24.338 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 7216 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
19:36:24.342 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
19:36:24.345 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:36:26.832 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
19:36:26.834 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
19:36:26.835 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
19:36:26.910 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
19:36:28.820 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
19:36:29.499 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
19:36:32.533 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
19:36:32.568 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
19:36:32.568 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
19:36:32.573 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
19:36:32.574 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

19:36:32.575 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
19:36:32.575 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
19:36:32.575 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@76a9eee4
19:36:33.657 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
19:36:34.059 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
19:36:34.072 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.074 seconds (JVM running for 10.522)
19:36:39.440 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:37:35.790 [http-nio-8081-exec-18] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
19:41:25.682 [http-nio-8081-exec-45] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
19:45:41.370 [http-nio-8081-exec-2] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
19:45:45.279 [http-nio-8081-exec-6] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：customerId=PC0000000006
19:46:31.744 [http-nio-8081-exec-7] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
19:47:15.018 [http-nio-8081-exec-10] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：customerId=PC0000000006
19:47:37.667 [http-nio-8081-exec-14] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：customerId=PC0000000006
19:47:42.185 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 10484 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
19:47:42.189 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
19:47:42.189 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:47:44.510 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
19:47:44.512 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
19:47:44.513 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
19:47:44.596 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
19:47:46.241 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
19:47:46.942 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
19:47:49.811 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
19:47:49.821 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
19:47:49.821 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
19:47:49.821 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
19:47:49.822 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

19:47:49.823 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
19:47:49.823 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
19:47:49.823 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7af71dee
19:47:50.832 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
19:47:51.233 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
19:47:51.241 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.381 seconds (JVM running for 9.845)
19:47:57.066 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:49:05.906 [http-nio-8081-exec-16] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：customerId=PC0000000004
19:49:19.466 [http-nio-8081-exec-18] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：customerId=PC0000000004
19:58:08.388 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 12108 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
19:58:08.390 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:58:08.392 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
19:58:10.774 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
19:58:10.776 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
19:58:10.777 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
19:58:10.861 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
19:58:12.690 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
19:58:13.401 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
19:58:16.483 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
19:58:16.490 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
19:58:16.490 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
19:58:16.491 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
19:58:16.491 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

19:58:16.492 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
19:58:16.492 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
19:58:16.492 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5cfbc764
19:58:17.370 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
19:58:17.737 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
19:58:17.747 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.679 seconds (JVM running for 10.13)
19:58:28.051 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 24756 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
19:58:28.051 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:58:28.055 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
19:58:30.659 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
19:58:30.661 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
19:58:30.662 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
19:58:30.743 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
19:58:32.542 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
19:58:33.224 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
19:58:35.955 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
19:58:35.964 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
19:58:35.964 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
19:58:35.965 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
19:58:35.967 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

19:58:35.967 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
19:58:35.968 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
19:58:35.968 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2289c3e8
19:58:36.824 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
19:58:37.171 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
19:58:37.180 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.478 seconds (JVM running for 9.947)
19:58:40.734 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:10:03.235 [http-nio-8081-exec-55] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：customerId=PC0000000006
20:10:30.136 [http-nio-8081-exec-56] INFO  c.r.l.c.LoanCompensationController - [detail,83] - 查询流程代偿详情:LoanCompensation(id=19, trialBalance=null, applyId=***************************, loanId=6, customerId=null, customerName=彭XX, salesman=出单员, orgName=衢州华沂分公司, partnerId=**********, bank=皖新租赁, loanAmount=48000.00, otherDebt=300.00, totalMoney=300.00, examineStatus=3, reason=null, createDate=Thu Jul 17 21:42:33 CST 2025, updateDate=Fri Jul 18 15:07:41 CST 2025, fxjProportion=15, qdProportion=25, gmjProportion=10, kjjProportion=12, kjczProportion=22, sbczProportion=16, fxjMoney=0.00, qdMoney=0.00, gmjMoney=0.00, kjjMoney=0.00, kjczMoney=0.00, sbczMoney=0.00, fxjAccount=345345, qdAccount=系统划扣, gmjAccount=345345, kjjAccount=系统划扣, kjczAccount=345345, sbczAccount=系统划扣, image=https://daihou-file.oss-cn-hangzhou.aliyuncs.com/2025/07/18/a35c9597-756b-4bb0-adb2-e77e54eaf0ac.png, payouts=null, payoutsTime=null)
20:11:41.622 [http-nio-8081-exec-60] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000006
20:11:45.992 [http-nio-8081-exec-66] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000006
20:11:50.816 [http-nio-8081-exec-67] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000005
20:11:53.011 [http-nio-8081-exec-68] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000004
20:11:54.527 [http-nio-8081-exec-69] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000003
20:11:56.090 [http-nio-8081-exec-70] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000002
20:11:57.519 [http-nio-8081-exec-71] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
20:15:02.665 [http-nio-8081-exec-80] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000005
20:15:06.108 [http-nio-8081-exec-81] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
20:15:09.972 [http-nio-8081-exec-82] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000006
20:16:36.138 [http-nio-8081-exec-87] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
20:16:46.615 [http-nio-8081-exec-91] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
20:19:29.106 [http-nio-8081-exec-96] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
20:19:43.003 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 23460 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
20:19:43.007 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
20:19:43.007 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:19:45.420 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
20:19:45.423 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
20:19:45.423 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
20:19:45.502 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
20:19:47.247 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
20:19:47.934 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
20:19:50.773 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
20:19:50.780 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
20:19:50.780 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
20:19:50.781 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
20:19:50.783 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

20:19:50.783 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
20:19:50.783 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
20:19:50.783 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5d9b46d0
20:19:51.643 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
20:19:52.073 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
20:19:52.084 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.421 seconds (JVM running for 9.871)
20:19:59.374 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:20:42.234 [http-nio-8081-exec-8] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000004
20:20:43.940 [http-nio-8081-exec-9] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000002
20:24:35.484 [http-nio-8081-exec-36] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:25:32.567 [http-nio-8081-exec-42] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:29:10.095 [http-nio-8081-exec-60] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
20:29:46.542 [http-nio-8081-exec-79] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
20:30:25.439 [http-nio-8081-exec-92] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000006
20:30:27.114 [http-nio-8081-exec-93] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000004
20:30:28.887 [http-nio-8081-exec-94] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000005
20:31:12.866 [http-nio-8081-exec-100] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000006
20:31:19.011 [http-nio-8081-exec-2] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
20:31:24.046 [http-nio-8081-exec-6] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000004
20:31:26.226 [http-nio-8081-exec-5] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000002
20:31:30.263 [http-nio-8081-exec-10] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000003
20:32:38.751 [http-nio-8081-exec-18] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000006
20:32:42.712 [http-nio-8081-exec-17] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000004
20:32:49.204 [http-nio-8081-exec-22] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000005
20:32:50.581 [http-nio-8081-exec-21] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000004
20:32:52.207 [http-nio-8081-exec-24] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000003
20:32:54.184 [http-nio-8081-exec-23] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
20:33:28.007 [http-nio-8081-exec-31] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
20:35:19.618 [http-nio-8081-exec-47] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
20:37:07.352 [http-nio-8081-exec-54] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000002
20:37:10.105 [http-nio-8081-exec-49] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
20:41:07.300 [http-nio-8081-exec-70] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
20:45:30.817 [http-nio-8081-exec-90] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
20:47:28.237 [http-nio-8081-exec-2] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
20:49:57.093 [http-nio-8081-exec-12] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
20:52:41.085 [http-nio-8081-exec-17] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
20:55:37.499 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 11924 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
20:55:37.502 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
20:55:37.505 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:55:39.836 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
20:55:39.838 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
20:55:39.839 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
20:55:39.919 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
20:55:41.622 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
20:55:42.318 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
20:55:45.053 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
20:55:45.059 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
20:55:45.060 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
20:55:45.060 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
20:55:45.061 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

20:55:45.061 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
20:55:45.061 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
20:55:45.061 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6d1b850e
20:55:45.923 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
20:55:46.278 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
20:55:46.287 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.136 seconds (JVM running for 9.587)
20:55:57.902 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:55:58.260 [http-nio-8081-exec-1] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
21:00:51.500 [http-nio-8081-exec-15] INFO  c.r.l.c.LoanCompensationController - [detail,83] - 查询流程代偿详情:LoanCompensation(id=19, trialBalance=null, applyId=***************************, loanId=6, customerId=null, customerName=彭XX, salesman=出单员, orgName=衢州华沂分公司, partnerId=**********, bank=皖新租赁, loanAmount=48000.00, otherDebt=300.00, totalMoney=300.00, examineStatus=3, reason=null, createDate=Thu Jul 17 21:42:33 CST 2025, updateDate=Fri Jul 18 15:07:41 CST 2025, fxjProportion=15, qdProportion=25, gmjProportion=10, kjjProportion=12, kjczProportion=22, sbczProportion=16, fxjMoney=0.00, qdMoney=0.00, gmjMoney=0.00, kjjMoney=0.00, kjczMoney=0.00, sbczMoney=0.00, fxjAccount=345345, qdAccount=系统划扣, gmjAccount=345345, kjjAccount=系统划扣, kjczAccount=345345, sbczAccount=系统划扣, image=https://daihou-file.oss-cn-hangzhou.aliyuncs.com/2025/07/18/a35c9597-756b-4bb0-adb2-e77e54eaf0ac.png, payouts=null, payoutsTime=null)
21:04:54.180 [http-nio-8081-exec-24] INFO  c.r.l.c.LoanCompensationController - [detail,83] - 查询流程代偿详情:LoanCompensation(id=19, trialBalance=null, applyId=***************************, loanId=6, customerId=null, customerName=彭XX, salesman=出单员, orgName=衢州华沂分公司, partnerId=**********, bank=皖新租赁, loanAmount=48000.00, otherDebt=300.00, totalMoney=300.00, examineStatus=3, reason=null, createDate=Thu Jul 17 21:42:33 CST 2025, updateDate=Fri Jul 18 15:07:41 CST 2025, fxjProportion=15, qdProportion=25, gmjProportion=10, kjjProportion=12, kjczProportion=22, sbczProportion=16, fxjMoney=0.00, qdMoney=0.00, gmjMoney=0.00, kjjMoney=0.00, kjczMoney=0.00, sbczMoney=0.00, fxjAccount=345345, qdAccount=系统划扣, gmjAccount=345345, kjjAccount=系统划扣, kjczAccount=345345, sbczAccount=系统划扣, image=https://daihou-file.oss-cn-hangzhou.aliyuncs.com/2025/07/18/a35c9597-756b-4bb0-adb2-e77e54eaf0ac.png, payouts=null, payoutsTime=null)
21:19:56.533 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 24776 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
21:19:56.535 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:19:56.536 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:19:59.033 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
21:19:59.036 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:19:59.037 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:19:59.123 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:20:00.861 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
21:20:01.540 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
21:20:04.263 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
21:20:04.274 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
21:20:04.274 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
21:20:04.276 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
21:20:04.277 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

21:20:04.277 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
21:20:04.277 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
21:20:04.277 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5cfbc764
21:20:05.172 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
21:20:05.530 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
21:20:05.539 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.353 seconds (JVM running for 9.795)
21:20:15.180 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:20:17.513 [http-nio-8081-exec-3] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
21:21:35.050 [http-nio-8081-exec-8] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
21:24:05.199 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 2236 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
21:24:05.203 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:24:05.203 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:24:07.629 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
21:24:07.632 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:24:07.632 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:24:07.713 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:24:09.393 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
21:24:10.081 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
21:24:12.862 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
21:24:12.871 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
21:24:12.871 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
21:24:12.872 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
21:24:12.872 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

21:24:12.872 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
21:24:12.873 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
21:24:12.873 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5b287551
21:24:13.746 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
21:24:14.098 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
21:24:14.108 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.245 seconds (JVM running for 9.71)
21:24:18.966 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:24:25.129 [http-nio-8081-exec-4] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
21:24:55.343 [http-nio-8081-exec-11] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
21:29:20.831 [http-nio-8081-exec-27] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
21:30:54.051 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 8864 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
21:30:54.053 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:30:54.055 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:30:56.503 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
21:30:56.506 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:30:56.506 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:30:56.585 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:30:58.385 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
21:30:59.107 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
21:31:01.997 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
21:31:02.004 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
21:31:02.005 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
21:31:02.005 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
21:31:02.006 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

21:31:02.007 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
21:31:02.007 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
21:31:02.007 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3a13bb06
21:31:02.989 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
21:31:03.378 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
21:31:03.388 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.655 seconds (JVM running for 10.1)
21:31:12.324 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:34:41.606 [http-nio-8081-exec-42] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
21:37:28.078 [http-nio-8081-exec-51] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
21:40:42.161 [http-nio-8081-exec-69] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
21:42:18.687 [http-nio-8081-exec-82] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
21:52:03.077 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 5676 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
21:52:03.079 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:52:03.080 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:52:05.438 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
21:52:05.440 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:52:05.441 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:52:05.526 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:52:07.391 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
21:52:08.121 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
21:52:11.244 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
21:52:11.250 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
21:52:11.251 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
21:52:11.252 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
21:52:11.252 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

21:52:11.253 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
21:52:11.253 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
21:52:11.253 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6d1b850e
21:52:12.126 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
21:52:12.480 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
21:52:12.489 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.764 seconds (JVM running for 10.208)
21:52:14.661 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:00:36.260 [http-nio-8081-exec-19] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 4
