package com.ruoyi.installment_application_audit.service;

import java.util.List;
import com.ruoyi.installment_application_audit.domain.InstallmentApplicationAudit;

/**
 * 分期申请审核中间Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IInstallmentApplicationAuditService 
{
    /**
     * 查询分期申请审核中间
     * 
     * @param id 分期申请审核中间主键
     * @return 分期申请审核中间
     */
    public InstallmentApplicationAudit selectInstallmentApplicationAuditById(Long id);

    /**
     * 查询分期申请审核中间列表
     * 
     * @param installmentApplicationAudit 分期申请审核中间
     * @return 分期申请审核中间集合
     */
    public List<InstallmentApplicationAudit> selectInstallmentApplicationAuditList(InstallmentApplicationAudit installmentApplicationAudit);

    /**
     * 新增分期申请审核中间
     * 
     * @param installmentApplicationAudit 分期申请审核中间
     * @return 结果
     */
    public int insertInstallmentApplicationAudit(InstallmentApplicationAudit installmentApplicationAudit);

    /**
     * 修改分期申请审核中间
     * 
     * @param installmentApplicationAudit 分期申请审核中间
     * @return 结果
     */
    public int updateInstallmentApplicationAudit(InstallmentApplicationAudit installmentApplicationAudit);

    /**
     * 批量删除分期申请审核中间
     * 
     * @param ids 需要删除的分期申请审核中间主键集合
     * @return 结果
     */
    public int deleteInstallmentApplicationAuditByIds(Long[] ids);

    /**
     * 删除分期申请审核中间信息
     * 
     * @param id 分期申请审核中间主键
     * @return 结果
     */
    public int deleteInstallmentApplicationAuditById(Long id);
}
