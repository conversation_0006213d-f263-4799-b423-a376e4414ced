package com.ruoyi.litigation_case.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import com.ruoyi.litigation_log.domain.LitigationLog;

/**
 * 法诉案件对象 litigation_case
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
public class LitigationCase extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 法诉文员 */
    @Excel(name = "法诉文员")
    private String litigationClerk;

    /** 发起法诉日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发起法诉日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date litigationStartDate;

    /** 案件启动日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "案件启动日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date litigationStartDay;

    /** 状态更新日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "状态更新日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date statusUpdateDate;

    /** 法诉状态 */
    @Excel(name = "法诉状态")
    private String litigationStatus;

    /** 法诉子状态 */
    @Excel(name = "法诉子状态")
    private String litigationSubStatus;

    /** 法院地 */
    @Excel(name = "法院地")
    private String courtJurisdiction;

    /** 诉讼法院 */
    @Excel(name = "诉讼法院")
    private String lawsuitCourt;

    /** 诉前调号 */
    @Excel(name = "诉前调号")
    private String preLitigationNo;

    /** 诉前调号出具时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "诉前调号出具时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date preLitigationTime;

    /** 民初号 */
    @Excel(name = "民初号")
    private String civilCaseNo;

    /** 民初号时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "民初号时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date civilCaseTime;

    /** 开庭时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开庭时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date courtHearingTime;

    /** 执行号 */
    @Excel(name = "执行号")
    private String enforcementNo;

    /** 执保号 */
    @Excel(name = "执保号")
    private String enforcementPreservationNo;

    /** 催收员 */
    @Excel(name = "催收员")
    private String urgeUser;
    /** 银行代偿金额 */
    @Excel(name = "银行代偿金额")
    private BigDecimal bankAmount;
    /** 银行催回金额 */
    @Excel(name = "银行催回金额")
    private BigDecimal bankAmountBack;
    /** 银行剩余未还代偿金 */
    @Excel(name = "银行剩余未还代偿金")
    private BigDecimal bankCompensation;
    /** 代扣金额 */
    @Excel(name = "代扣金额")
    private BigDecimal withholdingAmount;
    /** 代扣催回金额 */
    @Excel(name = "代扣催回金额")
    private BigDecimal withholdingAmountBack;
    /** 代扣剩余未还代偿金 */
    @Excel(name = "代扣剩余未还代偿金")
    private BigDecimal withholdingCompensation;
    /** 违约金 */
    @Excel(name = "违约金")
    private BigDecimal liquidatedAmount;
    /** 催回违约金金额 */
    @Excel(name = "催回违约金金额")
    private BigDecimal liquidatedAmountBack;
    /** 剩余未还违约金金额 */
    @Excel(name = "剩余未还违约金金额")
    private BigDecimal liquidatedCompensation;
    /** 其他欠款 */
    @Excel(name = "其他欠款")
    private BigDecimal otherAmount;
    /** 催回未还其他欠款 */
    @Excel(name = "催回未还其他欠款")
    private BigDecimal otherAmountBack;
    /** 催回其他欠款 */
    @Excel(name = "催回其他欠款")
    private BigDecimal otherCompensation;

    /** 总金额 */
    @Excel(name = "总金额")
    private BigDecimal totalAmount;

    /** 总未还金额 */
    @Excel(name = "总未还金额")
    private BigDecimal totalCompensation;

    /** 总催回金额 */
    @Excel(name = "总催回金额")
    private BigDecimal totalAmountBack;

    /** 流程id */
    @Excel(name = "流程id")
    private Long loanId;

    /** 案件类型 1-普通案件 2-重点案件 */
    @Excel(name = "案件类型", readConverterExp = "1=普通案件,2=重点案件")
    private Integer litigationType;

    /** 起诉类型 1-债转 2-债加 3-担保物权 4-仲裁 5-赋强公证  6-拍状元  7-拍司令  8-属地诉讼  9-余值起诉  10-债权出售  11-签约地诉讼  12-特殊诉讼通道 */
    @Excel(name = "起诉类型")
    private String prosecutionType;

    /** 起诉内容  1-代偿起诉  2-担保费起诉  3-违约金起诉  4-居间费起诉  5-代偿+担保费起诉  6-代偿+担保费+违约金起诉  7-代偿+违约金起诉 */
    @Excel(name = "起诉内容")
    private String prosecutionContent;

    /** 起诉金额 */
    @Excel(name = "起诉金额")
    private BigDecimal prosecutionAmount;

    /** 最新催记记录 */
    private LitigationLog latestLitigationLog;

    /** 最新催记-催回金额 */
    private java.math.BigDecimal urgeMoney;
    /** 最新催记-还款状态 */
    private Integer repaymentStatus;
    /** 最新催记-催记状态 */
    private Integer urgeStatus;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setLitigationClerk(String litigationClerk) {
        this.litigationClerk = litigationClerk;
    }

    public String getLitigationClerk() {
        return litigationClerk;
    }

    public void setLitigationStartDate(Date litigationStartDate) {
        this.litigationStartDate = litigationStartDate;
    }

    public Date getLitigationStartDate() {
        return litigationStartDate;
    }

    public void setLitigationStartDay(Date litigationStartDay) {
        this.litigationStartDay = litigationStartDay;
    }

    public Date getLitigationStartDay() {
        return litigationStartDay;
    }

    public void setStatusUpdateDate(Date statusUpdateDate) {
        this.statusUpdateDate = statusUpdateDate;
    }

    public Date getStatusUpdateDate() {
        return statusUpdateDate;
    }

    public void setLitigationStatus(String litigationStatus) {
        this.litigationStatus = litigationStatus;
    }

    public String getLitigationStatus() {
        return litigationStatus;
    }

    public void setLitigationSubStatus(String litigationSubStatus) {
        this.litigationSubStatus = litigationSubStatus;
    }

    public String getLitigationSubStatus() {
        return litigationSubStatus;
    }

    public void setCourtJurisdiction(String courtJurisdiction) {
        this.courtJurisdiction = courtJurisdiction;
    }

    public String getCourtJurisdiction() {
        return courtJurisdiction;
    }

    public void setLawsuitCourt(String lawsuitCourt) {
        this.lawsuitCourt = lawsuitCourt;
    }

    public String getLawsuitCourt() {
        return lawsuitCourt;
    }

    public void setPreLitigationNo(String preLitigationNo) {
        this.preLitigationNo = preLitigationNo;
    }

    public String getPreLitigationNo() {
        return preLitigationNo;
    }

    public void setPreLitigationTime(Date preLitigationTime) {
        this.preLitigationTime = preLitigationTime;
    }

    public Date getPreLitigationTime() {
        return preLitigationTime;
    }

    public void setCivilCaseNo(String civilCaseNo) {
        this.civilCaseNo = civilCaseNo;
    }

    public String getCivilCaseNo() {
        return civilCaseNo;
    }

    public void setCivilCaseTime(Date civilCaseTime) {
        this.civilCaseTime = civilCaseTime;
    }

    public Date getCivilCaseTime() {
        return civilCaseTime;
    }

    public void setCourtHearingTime(Date courtHearingTime) {
        this.courtHearingTime = courtHearingTime;
    }

    public Date getCourtHearingTime() {
        return courtHearingTime;
    }

    public void setEnforcementNo(String enforcementNo) {
        this.enforcementNo = enforcementNo;
    }

    public String getEnforcementNo() {
        return enforcementNo;
    }

    public void setEnforcementPreservationNo(String enforcementPreservationNo) {
        this.enforcementPreservationNo = enforcementPreservationNo;
    }

    public String getEnforcementPreservationNo() {
        return enforcementPreservationNo;
    }

    public String getUrgeUser() {
        return urgeUser;
    }
    public void setUrgeUser(String urgeUser) {
        this.urgeUser = urgeUser;
    }
    public BigDecimal getBankAmount() {
        return bankAmount;
    }
    public void setBankAmount(BigDecimal bankAmount) {
        this.bankAmount = bankAmount;
    }
    public BigDecimal getBankAmountBack() {
        return bankAmountBack;
    }
    public void setBankAmountBack(BigDecimal bankAmountBack) {
        this.bankAmountBack = bankAmountBack;
    }
    public BigDecimal getBankCompensation() {
        return bankCompensation;
    }
    public void setBankCompensation(BigDecimal bankCompensation) {
        this.bankCompensation = bankCompensation;
    }
    public BigDecimal getWithholdingAmount() {
        return withholdingAmount;
    }
    public void setWithholdingAmount(BigDecimal withholdingAmount) {
        this.withholdingAmount = withholdingAmount;
    }
    public BigDecimal getWithholdingAmountBack() {
        return withholdingAmountBack;
    }
    public void setWithholdingAmountBack(BigDecimal withholdingAmountBack) {
        this.withholdingAmountBack = withholdingAmountBack;
    }
    public BigDecimal getWithholdingCompensation() {
        return withholdingCompensation;
    }
    public void setWithholdingCompensation(BigDecimal withholdingCompensation) {
        this.withholdingCompensation = withholdingCompensation;
    }
    public BigDecimal getLiquidatedAmount() {
        return liquidatedAmount;
    }
    public void setLiquidatedAmount(BigDecimal liquidatedAmount) {
        this.liquidatedAmount = liquidatedAmount;
    }
    public BigDecimal getLiquidatedAmountBack() {
        return liquidatedAmountBack;
    }
    public void setLiquidatedAmountBack(BigDecimal liquidatedAmountBack) {
        this.liquidatedAmountBack = liquidatedAmountBack;
    }
    public BigDecimal getLiquidatedCompensation() {
        return liquidatedCompensation;
    }
    public void setLiquidatedCompensation(BigDecimal liquidatedCompensation) {
        this.liquidatedCompensation = liquidatedCompensation;
    }
    public BigDecimal getOtherAmount() {
        return otherAmount;
    }
    public void setOtherAmount(BigDecimal otherAmount) {
        this.otherAmount = otherAmount;
    }
    public BigDecimal getOtherAmountBack() {
        return otherAmountBack;
    }
    public void setOtherAmountBack(BigDecimal otherAmountBack) {
        this.otherAmountBack = otherAmountBack;
    }
    public BigDecimal getOtherCompensation() {
        return otherCompensation;
    }
    public void setOtherCompensation(BigDecimal otherCompensation) {
        this.otherCompensation = otherCompensation;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalCompensation() {
        return totalCompensation;
    }
    public void setTotalCompensation(BigDecimal totalCompensation) {
        this.totalCompensation = totalCompensation;
    }

    public BigDecimal getTotalAmountBack() {
        return totalAmountBack;
    }
    public void setTotalAmountBack(BigDecimal totalAmountBack) {
        this.totalAmountBack = totalAmountBack;
    }

    public void setLoanId(Long loanId) {
        this.loanId = loanId;
    }

    public Long getLoanId() {
        return loanId;
    }

    public Integer getLitigationType() {
        return litigationType;
    }
    public void setLitigationType(Integer litigationType) {
        this.litigationType = litigationType;
    }
    public String getProsecutionType() {
        return prosecutionType;
    }
    public void setProsecutionType(String prosecutionType) {
        this.prosecutionType = prosecutionType;
    }
    public String getProsecutionContent() {
        return prosecutionContent;
    }
    public void setProsecutionContent(String prosecutionContent) {
        this.prosecutionContent = prosecutionContent;
    }
    public BigDecimal getProsecutionAmount() {
        return prosecutionAmount;
    }
    public void setProsecutionAmount(BigDecimal prosecutionAmount) {
        this.prosecutionAmount = prosecutionAmount;
    }

    public LitigationLog getLatestLitigationLog() {
        return latestLitigationLog;
    }
    public void setLatestLitigationLog(LitigationLog latestLitigationLog) {
        this.latestLitigationLog = latestLitigationLog;
    }

    public java.math.BigDecimal getUrgeMoney() {
        return urgeMoney;
    }
    public void setUrgeMoney(java.math.BigDecimal urgeMoney) {
        this.urgeMoney = urgeMoney;
    }
    public Integer getRepaymentStatus() {
        return repaymentStatus;
    }
    public void setRepaymentStatus(Integer repaymentStatus) {
        this.repaymentStatus = repaymentStatus;
    }
    public Integer getUrgeStatus() {
        return urgeStatus;
    }
    public void setUrgeStatus(Integer urgeStatus) {
        this.urgeStatus = urgeStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("litigationClerk", getLitigationClerk())
                .append("litigationStartDate", getLitigationStartDate())
                .append("litigationStartDay", getLitigationStartDay())
                .append("statusUpdateDate", getStatusUpdateDate())
                .append("litigationStatus", getLitigationStatus())
                .append("litigationSubStatus", getLitigationSubStatus())
                .append("courtJurisdiction", getCourtJurisdiction())
                .append("lawsuitCourt", getLawsuitCourt())
                .append("preLitigationNo", getPreLitigationNo())
                .append("preLitigationTime", getPreLitigationTime())
                .append("civilCaseNo", getCivilCaseNo())
                .append("civilCaseTime", getCivilCaseTime())
                .append("courtHearingTime", getCourtHearingTime())
                .append("enforcementNo", getEnforcementNo())
                .append("enforcementPreservationNo", getEnforcementPreservationNo())
                .append("urgeUser", getUrgeUser())
                .append("bankAmount", getBankAmount())
                .append("bankAmountBack", getBankAmountBack())
                .append("bankCompensation", getBankCompensation())
                .append("withholdingAmount", getWithholdingAmount())
                .append("withholdingAmountBack", getWithholdingAmountBack())
                .append("withholdingCompensation", getWithholdingCompensation())
                .append("liquidatedAmount", getLiquidatedAmount())
                .append("liquidatedAmountBack", getLiquidatedAmountBack())
                .append("liquidatedCompensation", getLiquidatedCompensation())
                .append("otherAmount", getOtherAmount())
                .append("otherAmountBack", getOtherAmountBack())
                .append("otherCompensation", getOtherCompensation())
                .append("totalAmount", getTotalAmount())
                .append("totalCompensation", getTotalCompensation())
                .append("totalAmountBack", getTotalAmountBack())
                .append("loanId", getLoanId())
                .append("litigationType", getLitigationType())
                .append("prosecutionType", getProsecutionType())
                .append("prosecutionContent", getProsecutionContent())
                .append("prosecutionAmount", getProsecutionAmount())
                .append("latestLitigationLog", getLatestLitigationLog())
                .append("urgeMoney", getUrgeMoney())
                .append("repaymentStatus", getRepaymentStatus())
                .append("urgeStatus", getUrgeStatus())
                .toString();
    }
}
