package com.ruoyi.car_warehousing.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 车辆出入库对象 car_warehousing
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
public class CarWarehousing extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 创建时间 */
    private Date createDate;

    /** 更新时间 */
    private Date updateDate;

    /** 申请编号 */
    private String applyNo;

    /** 找车团队 */
    private Long teamId;

    /** 车库id */
    private Long garageId;

    /** 1-入库，2-出库 */
    private Integer libraryStatus;

    /** 入库时间 */
    private Date inboundTime;

    /** 出库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outboundTime;

    /** 找车佣金 */
    private BigDecimal locatingCommission;

    /** 钥匙状态：1-已邮寄，2-已收回，3-未归还 */
    @Excel(name = "钥匙状态：1-已邮寄，2-已收回，3-未归还")
    private Long keyStatus;

    /** 收车方式：1-主动交车，2-钥匙开车，3-板车拖车 */
    @Excel(name = "收车方式：1-主动交车，2-钥匙开车，3-板车拖车")
    private Integer collectionMethod;

    /** 收车位置 */
    @Excel(name = "收车位置")
    private String carLocation;

    /** 订单状态，0-发起，1-已分配，2-已完成，3-未完成，4-已撤销 */
    @Excel(name = "订单状态，0-发起，1-已分配，2-已完成，3-未完成，4-已撤销")
    private Integer status;

    /** 停车费 */
    @Excel(name = "停车费")
    private BigDecimal parkingFee;

    /** 卖车价格 */
    @Excel(name = "卖车价格")
    private BigDecimal sellingFares;

    /** 出库类型：1-归还车主，2-协商卖车，3-法院扣车 */
    @Excel(name = "出库类型", readConverterExp = "1=归还车主,2=协商卖车,3=法院扣车")
    private Integer outbounStatus;

    /** 事故标记：1-无事故，2-有事故 */
    @Excel(name = "事故标记", readConverterExp = "1=无事故,2=有事故")
    private Integer accidentFlag;

    // public void setId(String id)
    // {
    // this.id = id;
    // }
    //
    // public String getId()
    // {
    // return id;
    // }
    //
    // public void setCreateDate(Date createDate)
    // {
    // this.createDate = createDate;
    // }
    //
    // public Date getCreateDate()
    // {
    // return createDate;
    // }
    //
    // public void setUpdateDate(Date updateDate)
    // {
    // this.updateDate = updateDate;
    // }
    //
    // public Date getUpdateDate()
    // {
    // return updateDate;
    // }
    //
    // public void setApplyNo(String applyNo)
    // {
    // this.applyNo = applyNo;
    // }
    //
    // public String getApplyNo()
    // {
    // return applyNo;
    // }
    //
    // public void setTeamId(Long teamId)
    // {
    // this.teamId = teamId;
    // }
    //
    // public Long getTeamId()
    // {
    // return teamId;
    // }
    //
    // public void setGarageId(Long garageId)
    // {
    // this.garageId = garageId;
    // }
    //
    // public Long getGarageId()
    // {
    // return garageId;
    // }
    //
    // public void setLibraryStatus(Integer libraryStatus)
    // {
    // this.libraryStatus = libraryStatus;
    // }
    //
    // public Integer getLibraryStatus()
    // {
    // return libraryStatus;
    // }
    //
    // public void setInboundTime(Date inboundTime)
    // {
    // this.inboundTime = inboundTime;
    // }
    //
    // public Date getInboundTime()
    // {
    // return inboundTime;
    // }
    //
    // public void setOutboundTime(Date outboundTime)
    // {
    // this.outboundTime = outboundTime;
    // }
    //
    // public Date getOutboundTime()
    // {
    // return outboundTime;
    // }
    //
    // public void setLocatingCommission(BigDecimal locatingCommission)
    // {
    // this.locatingCommission = locatingCommission;
    // }
    //
    // public BigDecimal getLocatingCommission()
    // {
    // return locatingCommission;
    // }
    //
    // public void setKeyStatus(Long keyStatus)
    // {
    // this.keyStatus = keyStatus;
    // }
    //
    // public Long getKeyStatus()
    // {
    // return keyStatus;
    // }
    //
    // public void setCollectionMethod(Integer collectionMethod)
    // {
    // this.collectionMethod = collectionMethod;
    // }
    //
    // public Integer getCollectionMethod()
    // {
    // return collectionMethod;
    // }
    //
    // public void setCarLocation(String carLocation)
    // {
    // this.carLocation = carLocation;
    // }
    //
    // public String getCarLocation()
    // {
    // return carLocation;
    // }
    //
    // public void setStatus(Integer status)
    // {
    // this.status = status;
    // }
    //
    // public Integer getStatus()
    // {
    // return status;
    // }
    //
    // @Override
    // public String toString() {
    // return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
    // .append("id", getId())
    // .append("createBy", getCreateBy())
    // .append("createDate", getCreateDate())
    // .append("updateBy", getUpdateBy())
    // .append("updateDate", getUpdateDate())
    // .append("applyNo", getApplyNo())
    // .append("teamId", getTeamId())
    // .append("garageId", getGarageId())
    // .append("libraryStatus", getLibraryStatus())
    // .append("inboundTime", getInboundTime())
    // .append("outboundTime", getOutboundTime())
    // .append("locatingCommission", getLocatingCommission())
    // .append("keyStatus", getKeyStatus())
    // .append("collectionMethod", getCollectionMethod())
    // .append("carLocation", getCarLocation())
    // .append("status", getStatus())
    // .toString();
    // }
}
