package com.ruoyi.lh_repay_plan.service;

import java.util.List;
import com.ruoyi.lh_repay_plan.domain.LhRepayPlan;

/**
 * 蓝海还款计划Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface ILhRepayPlanService 
{
    /**
     * 查询蓝海还款计划
     * 
     * @param id 蓝海还款计划主键
     * @return 蓝海还款计划
     */
    public LhRepayPlan selectLhRepayPlanById(String id);

    /**
     * 查询蓝海还款计划列表
     * 
     * @param lhRepayPlan 蓝海还款计划
     * @return 蓝海还款计划集合
     */
    public List<LhRepayPlan> selectLhRepayPlanList(LhRepayPlan lhRepayPlan);

    /**
     * 新增蓝海还款计划
     * 
     * @param lhRepayPlan 蓝海还款计划
     * @return 结果
     */
    public int insertLhRepayPlan(LhRepayPlan lhRepayPlan);

    /**
     * 修改蓝海还款计划
     * 
     * @param lhRepayPlan 蓝海还款计划
     * @return 结果
     */
    public int updateLhRepayPlan(LhRepayPlan lhRepayPlan);

    /**
     * 批量删除蓝海还款计划
     * 
     * @param ids 需要删除的蓝海还款计划主键集合
     * @return 结果
     */
    public int deleteLhRepayPlanByIds(String[] ids);

    /**
     * 删除蓝海还款计划信息
     * 
     * @param id 蓝海还款计划主键
     * @return 结果
     */
    public int deleteLhRepayPlanById(String id);
}
