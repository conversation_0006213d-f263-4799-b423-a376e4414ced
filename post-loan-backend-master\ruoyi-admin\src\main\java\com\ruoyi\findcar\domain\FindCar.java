package com.ruoyi.findcar.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 找车结果上报对象 find_car
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
public class FindCar extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 序号 */
    private String id;

    /** 用户 */
    private String memberId;

    /** 仓库id */
    private Long guarantyId;

    /** 车辆状态 */
    @Excel(name = "车辆状态")
    private String carStatus;

    /** 入库状态 */
    private String store;

    /** 车辆图片 */
    @Excel(name = "车辆图片")
    private String img;

    /** 交车状态 */
    @Excel(name = "交车状态")
    private String collectionMethod;

    /** 创建时间 */
    private Date createDate;

    /** 修改时间 */
    private Date updateDate;

    /** 删除标识 */
    private String delFlag;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setMemberId(String memberId) 
    {
        this.memberId = memberId;
    }

    public String getMemberId() 
    {
        return memberId;
    }

    public void setGuarantyId(Long guarantyId) 
    {
        this.guarantyId = guarantyId;
    }

    public Long getGuarantyId() 
    {
        return guarantyId;
    }

    public void setCarStatus(String carStatus) 
    {
        this.carStatus = carStatus;
    }

    public String getCarStatus() 
    {
        return carStatus;
    }

    public void setStore(String store) 
    {
        this.store = store;
    }

    public String getStore() 
    {
        return store;
    }

    public void setImg(String img) 
    {
        this.img = img;
    }

    public String getImg() 
    {
        return img;
    }

    public void setCollectionMethod(String collectionMethod) 
    {
        this.collectionMethod = collectionMethod;
    }

    public String getCollectionMethod() 
    {
        return collectionMethod;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("memberId", getMemberId())
            .append("guarantyId", getGuarantyId())
            .append("carStatus", getCarStatus())
            .append("store", getStore())
            .append("img", getImg())
            .append("collectionMethod", getCollectionMethod())
            .append("createBy", getCreateBy())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
