package com.ruoyi.system.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SyPrepaymentApplyMapper;
import com.ruoyi.system.domain.SyPrepaymentApply;
import com.ruoyi.system.service.ISyPrepaymentApplyService;

/**
 * 苏银提前还款Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class SyPrepaymentApplyServiceImpl implements ISyPrepaymentApplyService 
{
    @Autowired
    private SyPrepaymentApplyMapper syPrepaymentApplyMapper;

    /**
     * 查询苏银提前还款
     * 
     * @param id 苏银提前还款主键
     * @return 苏银提前还款
     */
    @Override
    public SyPrepaymentApply selectSyPrepaymentApplyById(String id)
    {
        return syPrepaymentApplyMapper.selectSyPrepaymentApplyById(id);
    }

    /**
     * 查询苏银提前还款列表
     * 
     * @param syPrepaymentApply 苏银提前还款
     * @return 苏银提前还款
     */
    @Override
    public List<SyPrepaymentApply> selectSyPrepaymentApplyList(SyPrepaymentApply syPrepaymentApply)
    {
        return syPrepaymentApplyMapper.selectSyPrepaymentApplyList(syPrepaymentApply);
    }

    @Override
    public SyPrepaymentApply selectSyPrepaymentApplyLists(SyPrepaymentApply syPrepaymentApply)
    {
        return syPrepaymentApplyMapper.selectSyPrepaymentApplyLists(syPrepaymentApply);
    }
    /**
     * 新增苏银提前还款
     * 
     * @param syPrepaymentApply 苏银提前还款
     * @return 结果
     */
    @Override
    public int insertSyPrepaymentApply(SyPrepaymentApply syPrepaymentApply)
    {
        return syPrepaymentApplyMapper.insertSyPrepaymentApply(syPrepaymentApply);
    }

    /**
     * 修改苏银提前还款
     * 
     * @param syPrepaymentApply 苏银提前还款
     * @return 结果
     */
    @Override
    public int updateSyPrepaymentApply(SyPrepaymentApply syPrepaymentApply)
    {
        return syPrepaymentApplyMapper.updateSyPrepaymentApply(syPrepaymentApply);
    }

    /**
     * 批量删除苏银提前还款
     * 
     * @param ids 需要删除的苏银提前还款主键
     * @return 结果
     */
    @Override
    public int deleteSyPrepaymentApplyByIds(String[] ids)
    {
        return syPrepaymentApplyMapper.deleteSyPrepaymentApplyByIds(ids);
    }

    /**
     * 删除苏银提前还款信息
     * 
     * @param id 苏银提前还款主键
     * @return 结果
     */
    @Override
    public int deleteSyPrepaymentApplyById(String id)
    {
        return syPrepaymentApplyMapper.deleteSyPrepaymentApplyById(id);
    }
}
