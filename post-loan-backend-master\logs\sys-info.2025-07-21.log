08:50:48.592 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
08:50:48.595 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 21.0.1 on mjh with PID 16924 (C:\code\project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by 32052 in C:\code\project\java_project\post-loan-backend-master)
08:50:48.601 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
08:50:52.449 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
08:50:52.450 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
08:50:52.451 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
08:50:52.538 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
08:50:54.754 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
08:50:55.345 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
08:50:59.920 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
08:50:59.959 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
08:50:59.960 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
08:50:59.967 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
08:50:59.970 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

08:50:59.971 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
08:50:59.971 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
08:50:59.972 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@69a464ed
08:51:01.579 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
08:51:02.220 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
08:51:02.237 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 14.124 seconds (JVM running for 14.818)
09:01:49.611 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:01:52.683 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
09:07:51.126 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 21.0.1 on mjh with PID 28988 (C:\code\project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by 32052 in C:\code\project\java_project\post-loan-backend-master)
09:07:51.127 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:07:51.129 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:07:54.820 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
09:07:54.823 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:07:54.823 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:07:54.911 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:07:56.964 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:07:57.579 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:08:01.845 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:08:01.864 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:08:01.864 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:08:01.865 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:08:01.867 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:08:01.867 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:08:01.867 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:08:01.867 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@605e8519
09:08:03.353 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
09:08:04.000 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:08:04.017 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 13.351 seconds (JVM running for 13.96)
09:20:22.352 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:04:37.046 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:04:37.053 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 11580 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
14:04:37.055 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:04:42.268 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:04:42.270 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:04:42.270 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:04:42.343 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:04:44.191 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:04:44.883 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:04:48.308 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:04:48.315 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:04:48.315 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:04:48.316 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:04:48.317 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:04:48.317 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:04:48.317 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:04:48.318 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6631e069
14:04:49.260 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:04:49.648 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:04:49.656 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 12.967 seconds (JVM running for 13.432)
14:06:38.618 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:06:38.637 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:06:38.637 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:06:38.637 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:06:38.638 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:06:38.643 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
14:06:38.648 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
14:06:38.648 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
14:06:38.649 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
14:06:52.517 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 22292 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
14:06:52.522 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:06:52.526 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:06:54.925 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:06:54.927 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:06:54.927 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:06:55.005 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:06:56.692 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:06:57.387 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:07:00.169 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:07:00.176 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:07:00.176 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:07:00.177 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:07:00.178 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:07:00.178 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:07:00.178 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:07:00.178 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@cf9c4ae
14:07:01.070 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:07:01.475 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:07:01.483 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.279 seconds (JVM running for 9.722)
14:07:56.854 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:07:56.873 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:07:56.873 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:07:56.873 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:07:56.874 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:07:56.879 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
14:07:56.885 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
14:07:56.885 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
14:07:56.886 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
14:09:15.877 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25392 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
14:09:15.880 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:09:15.881 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:09:18.210 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:09:18.213 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:09:18.213 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:09:18.289 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:09:20.558 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:09:21.260 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:09:24.103 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:09:24.111 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:09:24.111 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:09:24.112 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:09:24.113 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:09:24.113 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:09:24.113 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:09:24.113 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@750a5eb
14:09:25.024 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:09:25.410 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:09:25.420 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.88 seconds (JVM running for 10.348)
14:11:26.767 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:11:26.789 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:11:26.790 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:11:26.790 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:11:26.791 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:11:26.799 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
14:11:26.806 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
14:11:26.807 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
14:11:26.808 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
14:13:29.488 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 23848 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
14:13:29.490 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:13:29.491 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:13:31.827 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:13:31.829 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:13:31.830 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:13:31.906 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:13:33.625 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:13:34.340 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:13:37.187 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:13:37.200 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:13:37.200 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:13:37.201 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:13:37.201 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:13:37.201 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:13:37.202 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:13:37.202 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@30dd1f9f
14:13:38.146 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:13:38.548 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:13:38.558 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.399 seconds (JVM running for 9.834)
14:13:44.529 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:13:44.558 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:13:44.558 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:13:44.558 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:13:44.559 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:13:44.565 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
14:13:44.573 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
14:13:44.573 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
14:13:44.574 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
14:14:42.943 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 9332 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
14:14:42.946 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:14:42.946 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:14:45.433 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:14:45.435 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:14:45.436 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:14:45.521 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:14:47.274 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:14:47.984 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:14:51.077 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:14:51.091 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:14:51.092 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:14:51.092 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:14:51.093 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:14:51.093 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:14:51.094 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:14:51.094 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6479bf15
14:14:52.061 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:14:52.437 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:14:52.448 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.823 seconds (JVM running for 10.275)
14:15:09.354 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:15:09.352 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 21396 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
14:15:09.356 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:15:11.829 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:15:11.832 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:15:11.832 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:15:11.914 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:15:13.765 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:15:14.439 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:15:17.141 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:15:17.162 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:15:17.162 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:15:17.164 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:15:17.165 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:15:17.165 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:15:17.166 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:15:17.166 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@658b327c
14:15:18.058 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:15:18.404 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:15:18.412 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.395 seconds (JVM running for 9.877)
14:17:12.947 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 7204 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
14:17:12.951 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:17:12.951 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:17:16.498 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:17:16.501 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:17:16.501 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:17:16.582 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:17:18.426 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:17:19.094 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:17:22.203 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:17:22.229 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:17:22.230 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:17:22.231 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:17:22.233 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:17:22.233 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:17:22.234 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:17:22.234 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2f7f01ef
14:17:23.182 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:17:23.579 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:17:23.589 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.974 seconds (JVM running for 11.445)
14:18:56.578 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:19:01.257 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
16:24:35.985 [schedule-pool-1] INFO  sys-user - [run,55] - [************]内网IP[yidianadmin][Success][登录成功]
16:35:12.589 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 2232 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
16:35:12.591 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:35:12.592 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
16:35:14.987 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
16:35:14.990 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
16:35:14.990 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
16:35:15.062 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
16:35:16.746 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
16:35:17.446 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
16:35:20.271 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
16:35:20.285 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
16:35:20.285 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
16:35:20.286 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
16:35:20.287 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

16:35:20.287 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
16:35:20.287 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
16:35:20.288 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6d36269e
16:35:21.218 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
16:35:21.632 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
16:35:21.641 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.423 seconds (JVM running for 9.887)
16:36:10.539 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:39:25.695 [http-nio-8081-exec-13] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
18:21:19.969 [schedule-pool-1] INFO  sys-user - [run,55] - [************]内网IP[yidianadmin][Success][登录成功]
18:21:25.057 [http-nio-8081-exec-34] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
18:52:12.726 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 13624 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
18:52:12.730 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
18:52:12.730 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:52:15.187 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
18:52:15.190 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
18:52:15.190 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
18:52:15.273 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
18:52:17.097 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
18:52:17.764 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
18:52:20.653 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
18:52:20.661 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
18:52:20.661 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
18:52:20.662 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
18:52:20.662 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

18:52:20.663 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
18:52:20.663 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
18:52:20.663 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@75ae14c2
18:52:21.594 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
18:52:21.982 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
18:52:21.991 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.625 seconds (JVM running for 10.081)
18:52:29.400 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:35:29.046 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:35:29.051 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 14400 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
19:35:29.053 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
19:35:35.764 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
19:35:35.766 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
19:35:35.766 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
19:35:35.851 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
19:35:37.644 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
19:35:38.333 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
19:35:41.786 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
19:35:41.821 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
19:35:41.822 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
19:35:41.826 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
19:35:41.828 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

19:35:41.829 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
19:35:41.829 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
19:35:41.829 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@450c73d4
19:35:42.815 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
19:35:43.165 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
19:35:43.175 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 14.481 seconds (JVM running for 15.265)
19:41:33.245 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 28680 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
19:41:33.245 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:41:33.248 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
19:41:35.552 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
19:41:35.554 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
19:41:35.555 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
19:41:35.627 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
19:41:37.433 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
19:41:38.110 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
19:41:40.787 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
19:41:40.801 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
19:41:40.801 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
19:41:40.802 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
19:41:40.803 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

19:41:40.803 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
19:41:40.803 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
19:41:40.803 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4f8e3f83
19:41:41.730 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
19:41:42.080 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
19:41:42.089 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.167 seconds (JVM running for 9.612)
19:41:59.127 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:47:37.911 [schedule-pool-1] INFO  sys-user - [run,55] - [************]内网IP[yidianadmin][Success][登录成功]
19:50:50.104 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 16708 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
19:50:50.107 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
19:50:50.108 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:50:52.664 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
19:50:52.666 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
19:50:52.667 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
19:50:52.759 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
19:50:54.425 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
19:50:55.105 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
19:50:58.166 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
19:50:58.206 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
19:50:58.206 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
19:50:58.211 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
19:50:58.214 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

19:50:58.214 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
19:50:58.215 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
19:50:58.215 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4aa04838
19:50:59.150 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
19:50:59.518 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
19:50:59.530 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.807 seconds (JVM running for 10.294)
19:51:03.351 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:51:03.620 [http-nio-8081-exec-1] INFO  c.r.v.c.VwLitigationCaseFullController - [list,48] - 开始查询法诉案件列表，查询条件: 流程序号=null, 法诉文员=null
19:51:03.722 [http-nio-8081-exec-1] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,106] - 开始批量填充催记数据，案件数量: 1
19:51:03.723 [http-nio-8081-exec-1] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,113] - 需要查询催记数据的流程序号列表: [6]
19:51:03.745 [http-nio-8081-exec-1] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,118] - 批量查询到的催记数据总数: 0
19:51:03.746 [http-nio-8081-exec-1] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,123] - 其中status=2的催记数量: 0
19:51:03.746 [http-nio-8081-exec-1] INFO  c.r.v.c.VwLitigationCaseFullController - [list,54] - 查询完成，返回数据条数: 1
19:51:03.747 [http-nio-8081-exec-1] INFO  c.r.v.c.VwLitigationCaseFullController - [list,60] - 数据[0] - 流程序号: 6, 日志内容: null, 催回总金额: null
19:51:05.090 [http-nio-8081-exec-5] INFO  c.r.v.c.VwLitigationCaseFullController - [list,48] - 开始查询法诉案件列表，查询条件: 流程序号=null, 法诉文员=null
19:51:05.128 [http-nio-8081-exec-5] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,106] - 开始批量填充催记数据，案件数量: 1
19:51:05.129 [http-nio-8081-exec-5] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,113] - 需要查询催记数据的流程序号列表: [6]
19:51:05.145 [http-nio-8081-exec-5] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,118] - 批量查询到的催记数据总数: 0
19:51:05.146 [http-nio-8081-exec-5] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,123] - 其中status=2的催记数量: 0
19:51:05.146 [http-nio-8081-exec-5] INFO  c.r.v.c.VwLitigationCaseFullController - [list,54] - 查询完成，返回数据条数: 1
19:51:05.146 [http-nio-8081-exec-5] INFO  c.r.v.c.VwLitigationCaseFullController - [list,60] - 数据[0] - 流程序号: 6, 日志内容: null, 催回总金额: null
19:51:15.666 [http-nio-8081-exec-6] INFO  c.r.v.c.VwLitigationCaseFullController - [list,48] - 开始查询法诉案件列表，查询条件: 流程序号=null, 法诉文员=null
19:51:15.705 [http-nio-8081-exec-6] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,106] - 开始批量填充催记数据，案件数量: 1
19:51:15.705 [http-nio-8081-exec-6] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,113] - 需要查询催记数据的流程序号列表: [6]
19:51:15.722 [http-nio-8081-exec-6] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,118] - 批量查询到的催记数据总数: 0
19:51:15.722 [http-nio-8081-exec-6] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,123] - 其中status=2的催记数量: 0
19:51:15.723 [http-nio-8081-exec-6] INFO  c.r.v.c.VwLitigationCaseFullController - [list,54] - 查询完成，返回数据条数: 1
19:51:15.723 [http-nio-8081-exec-6] INFO  c.r.v.c.VwLitigationCaseFullController - [list,60] - 数据[0] - 流程序号: 6, 日志内容: null, 催回总金额: null
19:51:34.580 [http-nio-8081-exec-10] INFO  c.r.v.c.VwLitigationCaseFullController - [list,48] - 开始查询法诉案件列表，查询条件: 流程序号=null, 法诉文员=null
19:51:34.618 [http-nio-8081-exec-10] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,106] - 开始批量填充催记数据，案件数量: 1
19:51:34.618 [http-nio-8081-exec-10] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,113] - 需要查询催记数据的流程序号列表: [6]
19:51:34.635 [http-nio-8081-exec-10] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,118] - 批量查询到的催记数据总数: 0
19:51:34.635 [http-nio-8081-exec-10] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,123] - 其中status=2的催记数量: 0
19:51:34.635 [http-nio-8081-exec-10] INFO  c.r.v.c.VwLitigationCaseFullController - [list,54] - 查询完成，返回数据条数: 1
19:51:34.636 [http-nio-8081-exec-10] INFO  c.r.v.c.VwLitigationCaseFullController - [list,60] - 数据[0] - 流程序号: 6, 日志内容: null, 催回总金额: null
19:51:41.915 [http-nio-8081-exec-15] INFO  c.r.v.c.VwLitigationCaseFullController - [list,48] - 开始查询法诉案件列表，查询条件: 流程序号=null, 法诉文员=null
19:51:41.951 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,106] - 开始批量填充催记数据，案件数量: 1
19:51:41.951 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,113] - 需要查询催记数据的流程序号列表: [6]
19:51:41.969 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,118] - 批量查询到的催记数据总数: 0
19:51:41.969 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,123] - 其中status=2的催记数量: 0
19:51:41.969 [http-nio-8081-exec-15] INFO  c.r.v.c.VwLitigationCaseFullController - [list,54] - 查询完成，返回数据条数: 1
19:51:41.969 [http-nio-8081-exec-15] INFO  c.r.v.c.VwLitigationCaseFullController - [list,60] - 数据[0] - 流程序号: 6, 日志内容: null, 催回总金额: null
19:53:34.179 [http-nio-8081-exec-18] INFO  c.r.v.c.VwLitigationCaseFullController - [list,48] - 开始查询法诉案件列表，查询条件: 流程序号=null, 法诉文员=null
19:53:34.227 [http-nio-8081-exec-18] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,106] - 开始批量填充催记数据，案件数量: 1
19:53:34.228 [http-nio-8081-exec-18] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,113] - 需要查询催记数据的流程序号列表: [6]
19:53:34.244 [http-nio-8081-exec-18] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,118] - 批量查询到的催记数据总数: 0
19:53:34.244 [http-nio-8081-exec-18] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,123] - 其中status=2的催记数量: 0
19:53:34.245 [http-nio-8081-exec-18] INFO  c.r.v.c.VwLitigationCaseFullController - [list,54] - 查询完成，返回数据条数: 1
19:53:34.245 [http-nio-8081-exec-18] INFO  c.r.v.c.VwLitigationCaseFullController - [list,60] - 数据[0] - 流程序号: 6, 日志内容: null, 催回总金额: null
19:53:46.493 [http-nio-8081-exec-20] INFO  c.r.v.c.VwLitigationCaseFullController - [list,48] - 开始查询法诉案件列表，查询条件: 流程序号=null, 法诉文员=null
19:53:46.534 [http-nio-8081-exec-20] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,106] - 开始批量填充催记数据，案件数量: 1
19:53:46.534 [http-nio-8081-exec-20] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,113] - 需要查询催记数据的流程序号列表: [6]
19:53:46.552 [http-nio-8081-exec-20] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,118] - 批量查询到的催记数据总数: 0
19:53:46.552 [http-nio-8081-exec-20] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,123] - 其中status=2的催记数量: 0
19:53:46.553 [http-nio-8081-exec-20] INFO  c.r.v.c.VwLitigationCaseFullController - [list,54] - 查询完成，返回数据条数: 1
19:53:46.553 [http-nio-8081-exec-20] INFO  c.r.v.c.VwLitigationCaseFullController - [list,60] - 数据[0] - 流程序号: 6, 日志内容: null, 催回总金额: null
19:55:01.858 [http-nio-8081-exec-23] INFO  c.r.v.c.VwLitigationCaseFullController - [list,48] - 开始查询法诉案件列表，查询条件: 流程序号=null, 法诉文员=null
19:55:01.912 [http-nio-8081-exec-23] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,106] - 开始批量填充催记数据，案件数量: 1
19:55:01.913 [http-nio-8081-exec-23] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,113] - 需要查询催记数据的流程序号列表: [6]
19:55:01.930 [http-nio-8081-exec-23] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,118] - 批量查询到的催记数据总数: 0
19:55:01.931 [http-nio-8081-exec-23] INFO  c.r.v.s.i.VwLitigationCaseFullServiceImpl - [selectVwLitigationCaseFullList,123] - 其中status=2的催记数量: 0
19:55:01.931 [http-nio-8081-exec-23] INFO  c.r.v.c.VwLitigationCaseFullController - [list,54] - 查询完成，返回数据条数: 1
19:55:01.931 [http-nio-8081-exec-23] INFO  c.r.v.c.VwLitigationCaseFullController - [list,60] - 数据[0] - 流程序号: 6, 日志内容: null, 催回总金额: null
19:57:13.574 [http-nio-8081-exec-27] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
19:57:20.783 [http-nio-8081-exec-29] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
20:02:02.832 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 27212 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
20:02:02.836 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
20:02:02.836 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:02:05.156 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
20:02:05.158 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
20:02:05.158 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
20:02:05.235 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
20:02:06.962 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
20:02:07.647 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
20:02:10.446 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
20:02:10.453 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
20:02:10.453 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
20:02:10.454 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
20:02:10.455 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

20:02:10.455 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
20:02:10.455 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
20:02:10.455 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@58ef879c
20:02:11.359 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
20:02:11.768 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
20:02:11.779 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.276 seconds (JVM running for 9.738)
20:02:12.787 [http-nio-8081-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:02:54.260 [http-nio-8081-exec-12] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
20:18:36.514 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25940 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
20:18:36.518 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
20:18:36.521 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:18:39.101 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
20:18:39.103 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
20:18:39.104 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
20:18:39.186 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
20:18:40.981 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
20:18:41.689 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
20:18:44.503 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
20:18:44.534 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
20:18:44.534 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
20:18:44.537 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
20:18:44.539 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

20:18:44.539 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
20:18:44.539 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
20:18:44.539 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@de74be7
20:18:45.482 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
20:18:45.932 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
20:18:45.941 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.785 seconds (JVM running for 10.281)
20:19:01.510 [http-nio-8081-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:19:02.479 [http-nio-8081-exec-1] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:19:24.331 [http-nio-8081-exec-4] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:19:33.237 [http-nio-8081-exec-5] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:21:36.338 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 23192 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
20:21:36.343 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
20:21:36.349 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:21:38.731 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
20:21:38.734 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
20:21:38.734 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
20:21:38.813 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
20:21:40.561 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
20:21:41.259 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
20:21:44.126 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
20:21:44.139 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
20:21:44.140 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
20:21:44.143 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
20:21:44.144 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

20:21:44.145 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
20:21:44.145 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
20:21:44.145 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@26b0ea93
20:21:44.982 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
20:21:45.367 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
20:21:45.378 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.369 seconds (JVM running for 9.839)
20:22:01.337 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:24:17.433 [http-nio-8081-exec-15] INFO  c.r.f.d.DynamicDataSourceContextHolder - [setDataSourceType,26] - 切换到SLAVE数据源
20:24:29.693 [http-nio-8081-exec-29] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:25:31.497 [http-nio-8081-exec-21] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
20:42:36.855 [http-nio-8081-exec-85] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
20:44:26.533 [http-nio-8081-exec-5] INFO  c.r.f.d.DynamicDataSourceContextHolder - [setDataSourceType,26] - 切换到SLAVE数据源
20:46:27.444 [http-nio-8081-exec-14] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
20:47:43.311 [http-nio-8081-exec-29] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:47:56.826 [http-nio-8081-exec-19] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
20:51:22.152 [http-nio-8081-exec-37] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:53:23.683 [http-nio-8081-exec-53] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:54:53.319 [http-nio-8081-exec-64] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:55:36.838 [http-nio-8081-exec-63] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:56:15.566 [http-nio-8081-exec-69] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:59:10.629 [http-nio-8081-exec-82] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:59:15.064 [http-nio-8081-exec-84] INFO  c.r.s.c.SysOfficeController - [dh_assign,126] - 修改机构里面的贷后文员：SysOffice(id=2018050310721801571307520, loanUser=贷后文员2, legalUser=null, loanName=贷后文员2, legalName=null, loanTime=Mon Jul 21 20:59:15 CST 2025, legalTime=null, parentId=null, parentIds=null, name=null, status=null, sort=null, areaId=null, code=null, type=null, grade=null, address=null, zipCode=null, master=null, phone=null, fax=null, email=null, useable=null, primaryPerson=null, deputyPerson=null, remarks=null, createDate=null, updateDate=null, delFlag=null, fullName=null, courtName=null, areaName=null, signFlag=null, gpsScheme=null)
20:59:15.131 [http-nio-8081-exec-84] INFO  c.r.s.c.SysOfficeController - [dh_assign,126] - 修改机构里面的贷后文员：SysOffice(id=2018052910867885079031808, loanUser=贷后文员2, legalUser=null, loanName=贷后文员2, legalName=null, loanTime=Mon Jul 21 20:59:15 CST 2025, legalTime=null, parentId=null, parentIds=null, name=null, status=null, sort=null, areaId=null, code=null, type=null, grade=null, address=null, zipCode=null, master=null, phone=null, fax=null, email=null, useable=null, primaryPerson=null, deputyPerson=null, remarks=null, createDate=null, updateDate=null, delFlag=null, fullName=null, courtName=null, areaName=null, signFlag=null, gpsScheme=null)
20:59:15.521 [http-nio-8081-exec-86] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:59:22.974 [http-nio-8081-exec-87] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
20:59:27.042 [http-nio-8081-exec-88] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
20:59:31.934 [http-nio-8081-exec-90] INFO  c.r.s.c.SysOfficeController - [dh_assign,126] - 修改机构里面的贷后文员：SysOffice(id=2018050310721801571307520, loanUser=daihouwenyuan, legalUser=null, loanName=贷后文员, legalName=null, loanTime=Mon Jul 21 20:59:31 CST 2025, legalTime=null, parentId=null, parentIds=null, name=null, status=null, sort=null, areaId=null, code=null, type=null, grade=null, address=null, zipCode=null, master=null, phone=null, fax=null, email=null, useable=null, primaryPerson=null, deputyPerson=null, remarks=null, createDate=null, updateDate=null, delFlag=null, fullName=null, courtName=null, areaName=null, signFlag=null, gpsScheme=null)
20:59:31.992 [http-nio-8081-exec-90] INFO  c.r.s.c.SysOfficeController - [dh_assign,126] - 修改机构里面的贷后文员：SysOffice(id=2018052910867885079031808, loanUser=daihouwenyuan, legalUser=null, loanName=贷后文员, legalName=null, loanTime=Mon Jul 21 20:59:31 CST 2025, legalTime=null, parentId=null, parentIds=null, name=null, status=null, sort=null, areaId=null, code=null, type=null, grade=null, address=null, zipCode=null, master=null, phone=null, fax=null, email=null, useable=null, primaryPerson=null, deputyPerson=null, remarks=null, createDate=null, updateDate=null, delFlag=null, fullName=null, courtName=null, areaName=null, signFlag=null, gpsScheme=null)
20:59:32.194 [http-nio-8081-exec-94] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:59:42.009 [http-nio-8081-exec-96] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
20:59:49.404 [http-nio-8081-exec-98] INFO  c.r.s.c.SysOfficeController - [dh_assign,126] - 修改机构里面的贷后文员：SysOffice(id=2018050310721801571307520, loanUser=daihouwenyuan, legalUser=null, loanName=贷后文员, legalName=null, loanTime=Mon Jul 21 20:59:49 CST 2025, legalTime=null, parentId=null, parentIds=null, name=null, status=null, sort=null, areaId=null, code=null, type=null, grade=null, address=null, zipCode=null, master=null, phone=null, fax=null, email=null, useable=null, primaryPerson=null, deputyPerson=null, remarks=null, createDate=null, updateDate=null, delFlag=null, fullName=null, courtName=null, areaName=null, signFlag=null, gpsScheme=null)
20:59:49.463 [http-nio-8081-exec-98] INFO  c.r.s.c.SysOfficeController - [dh_assign,126] - 修改机构里面的贷后文员：SysOffice(id=2018052910867885079031808, loanUser=daihouwenyuan, legalUser=null, loanName=贷后文员, legalName=null, loanTime=Mon Jul 21 20:59:49 CST 2025, legalTime=null, parentId=null, parentIds=null, name=null, status=null, sort=null, areaId=null, code=null, type=null, grade=null, address=null, zipCode=null, master=null, phone=null, fax=null, email=null, useable=null, primaryPerson=null, deputyPerson=null, remarks=null, createDate=null, updateDate=null, delFlag=null, fullName=null, courtName=null, areaName=null, signFlag=null, gpsScheme=null)
20:59:49.546 [http-nio-8081-exec-99] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
21:00:01.403 [http-nio-8081-exec-1] INFO  c.r.s.c.SysOfficeController - [dh_assign,126] - 修改机构里面的贷后文员：SysOffice(id=2018050310721801571307520, loanUser=贷后文员2, legalUser=null, loanName=贷后文员2, legalName=null, loanTime=Mon Jul 21 21:00:01 CST 2025, legalTime=null, parentId=null, parentIds=null, name=null, status=null, sort=null, areaId=null, code=null, type=null, grade=null, address=null, zipCode=null, master=null, phone=null, fax=null, email=null, useable=null, primaryPerson=null, deputyPerson=null, remarks=null, createDate=null, updateDate=null, delFlag=null, fullName=null, courtName=null, areaName=null, signFlag=null, gpsScheme=null)
21:00:01.461 [http-nio-8081-exec-1] INFO  c.r.s.c.SysOfficeController - [dh_assign,126] - 修改机构里面的贷后文员：SysOffice(id=2018052910867885079031808, loanUser=贷后文员2, legalUser=null, loanName=贷后文员2, legalName=null, loanTime=Mon Jul 21 21:00:01 CST 2025, legalTime=null, parentId=null, parentIds=null, name=null, status=null, sort=null, areaId=null, code=null, type=null, grade=null, address=null, zipCode=null, master=null, phone=null, fax=null, email=null, useable=null, primaryPerson=null, deputyPerson=null, remarks=null, createDate=null, updateDate=null, delFlag=null, fullName=null, courtName=null, areaName=null, signFlag=null, gpsScheme=null)
21:00:01.550 [http-nio-8081-exec-2] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
21:00:52.711 [http-nio-8081-exec-3] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
21:02:46.797 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 30024 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
21:02:46.801 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:02:46.802 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:02:49.081 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
21:02:49.083 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:02:49.083 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:02:49.166 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:02:50.861 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
21:02:51.535 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
21:02:54.456 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
21:02:54.467 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
21:02:54.467 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
21:02:54.468 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
21:02:54.469 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

21:02:54.469 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
21:02:54.469 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
21:02:54.469 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@59728513
21:02:55.477 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
21:02:55.874 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
21:02:55.885 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.428 seconds (JVM running for 9.869)
21:02:56.366 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:03:05.945 [http-nio-8081-exec-10] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
21:03:21.229 [http-nio-8081-exec-23] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
21:04:50.418 [http-nio-8081-exec-29] INFO  c.r.l.c.LoanCompensationController - [detail,83] - 查询流程代偿详情:LoanCompensation(id=19, trialBalance=null, applyId=BA2024031222836608799723521, loanId=6, customerName=彭XX, salesman=出单员, orgName=衢州华沂分公司, partnerId=**********, bank=皖新租赁, loanAmount=48000.00, otherDebt=300.00, totalMoney=300.00, examineStatus=3, reason=null, createDate=Thu Jul 17 21:42:33 CST 2025, updateDate=Fri Jul 18 15:07:41 CST 2025, fxjProportion=15, qdProportion=25, gmjProportion=10, kjjProportion=12, kjczProportion=22, sbczProportion=16, fxjMoney=0.00, qdMoney=0.00, gmjMoney=0.00, kjjMoney=0.00, kjczMoney=0.00, sbczMoney=0.00, fxjAccount=345345, qdAccount=系统划扣, gmjAccount=345345, kjjAccount=系统划扣, kjczAccount=345345, sbczAccount=系统划扣, image=https://daihou-file.oss-cn-hangzhou.aliyuncs.com/2025/07/18/a35c9597-756b-4bb0-adb2-e77e54eaf0ac.png, payouts=null, payoutsTime=null)
21:06:20.828 [http-nio-8081-exec-40] INFO  c.r.l.c.LoanReminderController - [approve,168] - existingReminder: LoanReminder(id=1, loanId=6, userId=null, identity=出单员工, customerName=陈XX, customerMobile=null, carStatus=9, repaymentStatus=0, examineStatus=1, examineReason=6+66, bMoney=null, dMoney=null, bRepaymentImg=null, dRepaymentImg=null, bAccount=null, dAccount=null, urgeStatus=3, urgeDescribe=null, urgeMoney=null, appointedTime=Mon Jun 30 00:00:00 CST 2025, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, createBy=admin, createTime=Wed Jun 25 10:15:54 CST 2025, updateBy=yidianadmin, updateTime=Tue Jul 15 20:26:24 CST 2025)
21:06:20.832 [http-nio-8081-exec-40] INFO  c.r.l.c.LoanReminderController - [approve,175] - 贷款ID: 6
21:06:20.832 [http-nio-8081-exec-40] INFO  c.r.l.c.LoanReminderController - [approve,198] - 非代偿催记，检查并更新还款状态
21:06:20.856 [http-nio-8081-exec-40] INFO  c.r.a.s.i.AccountLoanServiceImpl - [checkAndUpdateRepaymentStatus,214] - 获取当前贷后还款状态, 贷款ID: 6, 当前状态: 6
21:06:20.857 [http-nio-8081-exec-40] INFO  c.r.a.s.i.AccountLoanServiceImpl - [updateRepaymentStatus,145] - 开始更新贷后还款状态, 贷款ID: 6, 目标状态: 7
21:06:20.933 [http-nio-8081-exec-40] INFO  c.r.a.s.i.AccountLoanServiceImpl - [updateRepaymentStatus,171] - 更新贷后还款状态结果: 成功, 贷款ID: 6, 目标状态: 7
21:06:20.933 [http-nio-8081-exec-40] INFO  c.r.a.s.i.AccountLoanServiceImpl - [checkAndUpdateRepaymentStatus,239] - 贷款状态更新成功，贷款ID: 6, 从状态: 6 更新为: 7
21:06:26.657 [http-nio-8081-exec-36] INFO  c.r.l.c.LoanReminderController - [approve,168] - existingReminder: LoanReminder(id=1, loanId=6, userId=null, identity=出单员工, customerName=陈XX, customerMobile=null, carStatus=9, repaymentStatus=0, examineStatus=1, examineReason=6+66, bMoney=null, dMoney=null, bRepaymentImg=null, dRepaymentImg=null, bAccount=null, dAccount=null, urgeStatus=3, urgeDescribe=null, urgeMoney=null, appointedTime=Mon Jun 30 00:00:00 CST 2025, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, createBy=admin, createTime=Wed Jun 25 10:15:54 CST 2025, updateBy=yidianadmin, updateTime=Mon Jul 21 21:06:21 CST 2025)
21:08:51.679 [http-nio-8081-exec-83] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
21:10:30.549 [http-nio-8081-exec-91] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
21:11:37.669 [http-nio-8081-exec-99] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
21:12:13.356 [http-nio-8081-exec-2] INFO  c.r.l.c.LoanCompensationController - [detail,83] - 查询流程代偿详情:LoanCompensation(id=21, trialBalance=null, applyId=***************************, loanId=3, customerName=舒X, salesman=出单员, orgName=衢州华沂分公司, partnerId=IO00000007, bank=中关村银行, loanAmount=48000.00, otherDebt=null, totalMoney=null, examineStatus=0, reason=null, createDate=Thu Jul 17 22:06:04 CST 2025, updateDate=null, fxjProportion=null, qdProportion=null, gmjProportion=null, kjjProportion=null, kjczProportion=null, sbczProportion=null, fxjMoney=null, qdMoney=null, gmjMoney=null, kjjMoney=null, kjczMoney=null, sbczMoney=null, fxjAccount=null, qdAccount=null, gmjAccount=null, kjjAccount=null, kjczAccount=null, sbczAccount=null, image=null, payouts=null, payoutsTime=null)
21:19:32.219 [http-nio-8081-exec-45] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
21:20:11.309 [http-nio-8081-exec-36] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
21:23:12.671 [http-nio-8081-exec-43] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
21:23:14.488 [http-nio-8081-exec-57] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
21:23:16.372 [http-nio-8081-exec-58] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
21:23:18.003 [http-nio-8081-exec-46] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
21:23:19.579 [http-nio-8081-exec-60] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
21:23:21.705 [http-nio-8081-exec-61] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
21:23:23.636 [http-nio-8081-exec-48] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
21:23:28.778 [http-nio-8081-exec-63] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
21:25:54.188 [http-nio-8081-exec-69] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
21:25:57.776 [http-nio-8081-exec-70] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
21:26:01.448 [http-nio-8081-exec-71] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000001
21:29:40.516 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:29:40.514 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 21664 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
21:29:40.518 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:29:42.835 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
21:29:42.837 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:29:42.838 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:29:42.910 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:29:44.717 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
21:29:45.420 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
21:29:48.077 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
21:29:48.094 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
21:29:48.094 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
21:29:48.096 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
21:29:48.097 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

21:29:48.097 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
21:29:48.097 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
21:29:48.097 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@de74be7
21:29:48.970 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
21:29:49.368 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
21:29:49.376 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.187 seconds (JVM running for 9.633)
21:30:17.066 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:31:55.325 [http-nio-8081-exec-15] INFO  c.r.l.c.LoanSettleController - [approveCompensationSettlement,187] - 非代偿结清，不执行更新repayment_status操作，loanId=5
21:34:48.175 [http-nio-8081-exec-38] INFO  c.r.v.c.VwLoanCompensationController - [listGte3,59] - 查询结果数量: 10
21:43:58.474 [http-nio-8081-exec-68] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
21:56:53.439 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:56:53.445 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 30996 (D:\code_project\java_project\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\post-loan-backend-master)
21:56:53.446 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:56:58.068 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
21:56:58.071 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:56:58.071 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:56:58.152 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:56:59.969 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
21:57:00.648 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
21:57:03.861 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
21:57:03.869 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
21:57:03.870 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
21:57:03.870 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
21:57:03.871 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

21:57:03.871 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
21:57:03.871 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
21:57:03.872 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@17123d5a
21:57:04.833 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
21:57:04.840 [restartedMain] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
21:57:04.841 [restartedMain] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
21:57:04.841 [restartedMain] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
21:57:04.841 [restartedMain] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:57:04.848 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
21:57:04.858 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
21:57:04.859 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
21:57:04.860 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
21:57:04.982 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Pausing ProtocolHandler ["http-nio-8081"]
21:57:04.982 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
21:57:04.992 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Stopping ProtocolHandler ["http-nio-8081"]
21:57:04.993 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Destroying ProtocolHandler ["http-nio-8081"]
21:57:22.423 [http-nio-8081-exec-15] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
21:57:29.713 [http-nio-8081-exec-18] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
21:57:45.137 [http-nio-8081-exec-19] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
21:57:56.606 [http-nio-8081-exec-21] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
21:58:34.020 [http-nio-8081-exec-23] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
21:58:52.070 [http-nio-8081-exec-27] INFO  c.r.s.c.SysOfficeController - [orgList,77] - 角色ID：1
