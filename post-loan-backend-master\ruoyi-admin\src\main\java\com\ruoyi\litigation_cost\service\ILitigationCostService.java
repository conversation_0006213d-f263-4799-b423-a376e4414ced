package com.ruoyi.litigation_cost.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.litigation_cost.domain.LitigationCost;

/**
 * 法诉费用明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface ILitigationCostService 
{
    /**
     * 查询法诉费用明细
     * 
     * @param id 法诉费用明细主键
     * @return 法诉费用明细
     */
    public LitigationCost selectLitigationCostById(Long id);

    /**
     * 查询法诉费用明细列表
     * 
     * @param litigationCost 法诉费用明细
     * @return 法诉费用明细集合
     */
    public List<LitigationCost> selectLitigationCostList(LitigationCost litigationCost);

    /**
     * 新增法诉费用明细
     * 
     * @param litigationCost 法诉费用明细
     * @return 结果
     */
    public int insertLitigationCost(LitigationCost litigationCost);

    /**
     * 修改法诉费用明细
     * 
     * @param litigationCost 法诉费用明细
     * @return 结果
     */
    public int updateLitigationCost(LitigationCost litigationCost);

    /**
     * 批量删除法诉费用明细
     * 
     * @param ids 需要删除的法诉费用明细主键集合
     * @return 结果
     */
    public int deleteLitigationCostByIds(Long[] ids);

    /**
     * 删除法诉费用明细信息
     *
     * @param id 法诉费用明细主键
     * @return 结果
     */
    public int deleteLitigationCostById(Long id);

    /**
     * 获取已提交的限制性费用类型（判决金额和利息）
     *
     * @param litigationCaseId 法诉案件ID
     * @return 已提交的限制性费用类型列表
     */
    public List<String> getSubmittedLimitedFeeTypes(Long litigationCaseId);

    /**
     * 根据案件ID列表获取费用汇总数据
     *
     * @param caseIds 案件ID列表
     * @return 费用汇总数据，key为案件ID，value为费用汇总信息
     */
    public Map<Long, Map<String, Object>> getCostSummaryByCaseIds(List<Long> caseIds);
}
