package com.ruoyi.ind_work.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.ind_work.mapper.IndWorkMapper;
import com.ruoyi.ind_work.domain.IndWork;
import com.ruoyi.ind_work.service.IIndWorkService;

/**
 * 个人客户工作信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@Service
public class IndWorkServiceImpl implements IIndWorkService 
{
    @Autowired
    private IndWorkMapper indWorkMapper;

    /**
     * 查询个人客户工作信息
     * 
     * @param id 个人客户工作信息主键
     * @return 个人客户工作信息
     */
    @Override
    public IndWork selectIndWorkById(String id)
    {
        return indWorkMapper.selectIndWorkById(id);
    }

    /**
     * 查询个人客户工作信息列表
     * 
     * @param indWork 个人客户工作信息
     * @return 个人客户工作信息
     */
    @Override
    public List<IndWork> selectIndWorkList(IndWork indWork)
    {
        return indWorkMapper.selectIndWorkList(indWork);
    }

    /**
     * 新增个人客户工作信息
     * 
     * @param indWork 个人客户工作信息
     * @return 结果
     */
    @Override
    public int insertIndWork(IndWork indWork)
    {
        return indWorkMapper.insertIndWork(indWork);
    }

    /**
     * 修改个人客户工作信息
     * 
     * @param indWork 个人客户工作信息
     * @return 结果
     */
    @Override
    public int updateIndWork(IndWork indWork)
    {
        return indWorkMapper.updateIndWork(indWork);
    }

    /**
     * 批量删除个人客户工作信息
     * 
     * @param ids 需要删除的个人客户工作信息主键
     * @return 结果
     */
    @Override
    public int deleteIndWorkByIds(String[] ids)
    {
        return indWorkMapper.deleteIndWorkByIds(ids);
    }

    /**
     * 删除个人客户工作信息信息
     * 
     * @param id 个人客户工作信息主键
     * @return 结果
     */
    @Override
    public int deleteIndWorkById(String id)
    {
        return indWorkMapper.deleteIndWorkById(id);
    }
}
