package com.ruoyi.loan_extension.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.loan_extension.mapper.LoanExtensionMapper;
import com.ruoyi.loan_extension.domain.LoanExtension;
import com.ruoyi.loan_extension.service.ILoanExtensionService;

/**
 * 流程延期申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class LoanExtensionServiceImpl implements ILoanExtensionService 
{
    @Autowired
    private LoanExtensionMapper loanExtensionMapper;

    /**
     * 查询流程延期申请
     * 
     * @param id 流程延期申请主键
     * @return 流程延期申请
     */
    @Override
    public LoanExtension selectLoanExtensionById(Integer id)
    {
        return loanExtensionMapper.selectLoanExtensionById(id);
    }

    /**
     * 查询流程延期申请列表
     * 
     * @param loanExtension 流程延期申请
     * @return 流程延期申请
     */
    @Override
    public List<LoanExtension> selectLoanExtensionList(LoanExtension loanExtension)
    {
        return loanExtensionMapper.selectLoanExtensionList(loanExtension);
    }

    /**
     * 新增流程延期申请
     * 
     * @param loanExtension 流程延期申请
     * @return 结果
     */
    @Override
    public int insertLoanExtension(LoanExtension loanExtension)
    {
        loanExtension.setCreateTime(DateUtils.getNowDate());
        return loanExtensionMapper.insertLoanExtension(loanExtension);
    }

    /**
     * 修改流程延期申请
     * 
     * @param loanExtension 流程延期申请
     * @return 结果
     */
    @Override
    public int updateLoanExtension(LoanExtension loanExtension)
    {
        loanExtension.setUpdateTime(DateUtils.getNowDate());
        return loanExtensionMapper.updateLoanExtension(loanExtension);
    }

    /**
     * 批量删除流程延期申请
     * 
     * @param ids 需要删除的流程延期申请主键
     * @return 结果
     */
    @Override
    public int deleteLoanExtensionByIds(Integer[] ids)
    {
        return loanExtensionMapper.deleteLoanExtensionByIds(ids);
    }

    /**
     * 删除流程延期申请信息
     * 
     * @param id 流程延期申请主键
     * @return 结果
     */
    @Override
    public int deleteLoanExtensionById(Integer id)
    {
        return loanExtensionMapper.deleteLoanExtensionById(id);
    }
}
