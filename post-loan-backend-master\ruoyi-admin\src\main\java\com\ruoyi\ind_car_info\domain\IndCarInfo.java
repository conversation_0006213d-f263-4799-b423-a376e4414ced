package com.ruoyi.ind_car_info.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 车辆信息对象 ind_car_info
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public class IndCarInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyNo;

    /** 客户编号 */
    @Excel(name = "客户编号")
    private String customerId;

    /** 车辆归属地-省 */
    @Excel(name = "车辆归属地-省")
    private String plateProvince;

    /** 车辆归属地-市 */
    @Excel(name = "车辆归属地-市")
    private String plateCity;

    /** 车辆归属地 */
    @Excel(name = "车辆归属地")
    private String plateAddress;

    /** 行驶里程 */
    @Excel(name = "行驶里程")
    private Long mileage;

    /** 车辆颜色(登记证) */
    @Excel(name = "车辆颜色(登记证)")
    private String color;

    /** 车辆性质(行驶证) */
    @Excel(name = "车辆性质(行驶证)")
    private String nature;

    /** 车辆型号(登记证) */
    @Excel(name = "车辆型号(登记证)")
    private String brandType;

    /** 车辆能源 */
    @Excel(name = "车辆能源")
    private String energy;

    /** 发证日期(登记证) */
    @Excel(name = "发证日期(登记证)")
    private String regIssueDate;

    /** 登记时间(登记证) */
    @Excel(name = "登记时间(登记证)")
    private String registrationDate;

    /** 是否有登记证 */
    @Excel(name = "是否有登记证")
    private String registrateFlag;

    /** 无法提供登记证原因 */
    @Excel(name = "无法提供登记证原因")
    private String registrateReason;

    /** 品牌id(车300) */
    @Excel(name = "品牌id(车300)")
    private String brandId;

    /** 品牌名称(车300) */
    @Excel(name = "品牌名称(车300)")
    private String brandName;

    /** 车系id(车300) */
    @Excel(name = "车系id(车300)")
    private String seriesId;

    /** 车系名称(车300) */
    @Excel(name = "车系名称(车300)")
    private String seriesName;

    /** 车型ID(车300) */
    @Excel(name = "车型ID(车300)")
    private String modelId;

    /** 车型名称(车300) */
    @Excel(name = "车型名称(车300)")
    private String modelName;

    /** 城市id(车300) */
    @Excel(name = "城市id(车300)")
    private String cityId;

    /** 城市名称(车300) */
    @Excel(name = "城市名称(车300)")
    private String cityName;

    /** 公里数(车300) */
    @Excel(name = "公里数(车300)")
    private Long mileAge;

    /** 估值上牌日期(车300) */
    @Excel(name = "估值上牌日期(车300)")
    private String regDate;

    /** 车型指导价(车300) */
    @Excel(name = "车型指导价(车300)")
    private BigDecimal modelPrice;

    /** 车商零售价(车300) */
    @Excel(name = "车商零售价(车300)")
    private BigDecimal dealerPrice;

    /** 车商最高零售价(车300) */
    @Excel(name = "车商最高零售价(车300)")
    private BigDecimal highDealerPrice;

    /** 交易价(车300) */
    @Excel(name = "交易价(车300)")
    private BigDecimal individualPrice;

    /** 收车价(车300) */
    @Excel(name = "收车价(车300)")
    private BigDecimal dealerBuyPrice;

    /** 个人最低交易价(车300) */
    @Excel(name = "个人最低交易价(车300)")
    private BigDecimal individualLowSoldPrice;

    /** 车商最低收车价(车300) */
    @Excel(name = "车商最低收车价(车300)")
    private BigDecimal dealerLowBuyPrice;

    /** 车商最高零售价(车300) */
    @Excel(name = "车商最高零售价(车300)")
    private BigDecimal dealerHighSoldPrice;

    /** 车商最低零售价(车300) */
    @Excel(name = "车商最低零售价(车300)")
    private BigDecimal dealerLowSoldPrice;

    /** 详细估值结果页面(车300) */
    @Excel(name = "详细估值结果页面(车300)")
    private String url;

    /** 创建时间 */
    private Date createDate;

    /** 更新时间 */
    private Date updateDate;

    /** 删除标记 */
    private String delFlag;

    /** 人所在地-省 */
    @Excel(name = "人所在地-省")
    private String manProvince;

    /** 人所在地-市 */
    @Excel(name = "人所在地-市")
    private String manCity;

    /** 人所在地-区 */
    @Excel(name = "人所在地-区")
    private String manBorough;

    /** 人所在地-地址 */
    @Excel(name = "人所在地-地址")
    private String manAddress;

    /** 人所在地-详细地址 */
    @Excel(name = "人所在地-详细地址")
    private String manDetailAddress;

    /** 车所在地-省 */
    @Excel(name = "车所在地-省")
    private String carProvince;

    /** 车所在地-市 */
    @Excel(name = "车所在地-市")
    private String carCity;

    /** 车所在地-区 */
    @Excel(name = "车所在地-区")
    private String carBorough;

    /** 车所在地-地址 */
    @Excel(name = "车所在地-地址")
    private String carAddress;

    /** 车所在地-详细地址 */
    @Excel(name = "车所在地-详细地址")
    private String carDetailAddress;

    /** 车地分类 */
    @Excel(name = "车地分类")
    private String carLandType;

    /** 车辆状态 */
    @Excel(name = "车辆状态")
    private String carStatus;

    /** GPS状态 */
    @Excel(name = "GPS状态")
    private String gpsStatus;

    /** 颜色(车300) */
    @Excel(name = "颜色(车300)")
    private String carColor;

    /** 车型年款(车300) */
    @Excel(name = "车型年款(车300)")
    private Long modelYear;

    /** 排量(车300) */
    @Excel(name = "排量(车300)")
    private String modelLiter;

    /** 变速箱类型(车300) */
    @Excel(name = "变速箱类型(车300)")
    private String modelGear;

    /** 排放标准(车300) */
    @Excel(name = "排放标准(车300)")
    private String modelEmissionStandard;

    /** 最小上牌年份(车300) */
    @Excel(name = "最小上牌年份(车300)")
    private Long minRegYear;

    /** 最大上牌年份(车300) */
    @Excel(name = "最大上牌年份(车300)")
    private Long maxRegYear;

    /** 车系组名(车300) */
    @Excel(name = "车系组名(车300)")
    private String seriesGroupName;

    /** 定价报告url(车300) */
    @Excel(name = "定价报告url(车300)")
    private String reportUrl;

    /** 类型1:乘用2:商用(车300) */
    @Excel(name = "类型1:乘用2:商用(车300)")
    private String vehicleType;

    /** 车系类别(苏银) */
    @Excel(name = "车系类别(苏银)")
    private String seriesType;

    /** 车辆状况(浙商/中关村) */
    @Excel(name = "车辆状况(浙商/中关村)")
    private String carState;

    /** 订单编号(精真估) */
    @Excel(name = "订单编号(精真估)")
    private String jzgOrderNo;

    /** 评估价格-售车价(精真估) */
    @Excel(name = "评估价格-售车价(精真估)")
    private BigDecimal jzgAssessmentPriceSold;

    /** 评估价格-收车价(精真估) */
    @Excel(name = "评估价格-收车价(精真估)")
    private BigDecimal jzgAssessmentPriceBuy;

    /** 厂家指导价(精真估) */
    @Excel(name = "厂家指导价(精真估)")
    private BigDecimal jzgManufacturerPrice;

    /** 估值报告 pdf 下载链接(精真估) */
    @Excel(name = "估值报告 pdf 下载链接(精真估)")
    private String jzgAssessmentReportPdf;

    /** 订单状态 1 成功 2 退回  4 拒评(精真估) */
    @Excel(name = "订单状态 1 成功 2 退回  4 拒评(精真估)")
    private String jzgStatus;

    /** 订单状态描述(精真估) */
    @Excel(name = "订单状态描述(精真估)")
    private String jzgStatusDes;

    /** 车辆出厂日期(登记证) */
    @Excel(name = "车辆出厂日期(登记证)")
    private String departureDate;

    /** 过户次数(登记证) */
    @Excel(name = "过户次数(登记证)")
    private Long transferCount;

    /** 保险情况(中关村) */
    @Excel(name = "保险情况(中关村)")
    private String insuranceCondition;

    /** 登记证编号(中关村) */
    @Excel(name = "登记证编号(中关村)")
    private String registrationNo;

    /** 订单号(车300) */
    @Excel(name = "订单号(车300)")
    private String orderNo;

    /** 订单状态(车300) */
    @Excel(name = "订单状态(车300)")
    private String orderStatus;

    /** 订单报告pdf(车300) */
    @Excel(name = "订单报告pdf(车300)")
    private String orderReportUrl;

    /** 订单报告web(车300) */
    @Excel(name = "订单报告web(车300)")
    private String orderReportWeb;

    /** 过户前车牌号(华瑞) */
    @Excel(name = "过户前车牌号(华瑞)")
    private String beforePlateNo;

    /** 制造商名称(登记证) */
    @Excel(name = "制造商名称(登记证)")
    private String manufacturer;

    /** 座位数(登记证) */
    @Excel(name = "座位数(登记证)")
    private Long seatNumber;

    /** 品牌代码(皖新) */
    @Excel(name = "品牌代码(皖新)")
    private String wxBrandCode;

    /** 品牌名称(皖新) */
    @Excel(name = "品牌名称(皖新)")
    private String wxBrandName;

    /** 车系代码(皖新) */
    @Excel(name = "车系代码(皖新)")
    private String wxSeriesCode;

    /** 车系名称(皖新) */
    @Excel(name = "车系名称(皖新)")
    private String wxSeriesName;

    /** 车型代码(皖新) */
    @Excel(name = "车型代码(皖新)")
    private String wxModelCode;

    /** 车型名称(皖新) */
    @Excel(name = "车型名称(皖新)")
    private String wxModelName;

    /** 指导价(皖新) */
    @Excel(name = "指导价(皖新)")
    private BigDecimal wxGuidancePrice;

    /** 车辆评估金额(皖新) */
    @Excel(name = "车辆评估金额(皖新)")
    private BigDecimal wxAssessmentPrice;

    /** 报告订单号(皖新) */
    @Excel(name = "报告订单号(皖新)")
    private String wxReportOrderNo;

    /** 事故车报告url(皖新) */
    @Excel(name = "事故车报告url(皖新)")
    private String wxReportUrl;

    /** 是否事故车(皖新):0-否 1-是 */
    @Excel(name = "是否事故车(皖新):0-否 1-是")
    private String isAccident;

    /** 车辆评估Id(皖新) */
    @Excel(name = "车辆评估Id(皖新)")
    private String avpId;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setApplyNo(String applyNo) 
    {
        this.applyNo = applyNo;
    }

    public String getApplyNo() 
    {
        return applyNo;
    }

    public void setCustomerId(String customerId) 
    {
        this.customerId = customerId;
    }

    public String getCustomerId() 
    {
        return customerId;
    }

    public void setPlateProvince(String plateProvince) 
    {
        this.plateProvince = plateProvince;
    }

    public String getPlateProvince() 
    {
        return plateProvince;
    }

    public void setPlateCity(String plateCity) 
    {
        this.plateCity = plateCity;
    }

    public String getPlateCity() 
    {
        return plateCity;
    }

    public void setPlateAddress(String plateAddress) 
    {
        this.plateAddress = plateAddress;
    }

    public String getPlateAddress() 
    {
        return plateAddress;
    }

    public void setMileage(Long mileage) 
    {
        this.mileage = mileage;
    }

    public Long getMileage() 
    {
        return mileage;
    }

    public void setColor(String color) 
    {
        this.color = color;
    }

    public String getColor() 
    {
        return color;
    }

    public void setNature(String nature) 
    {
        this.nature = nature;
    }

    public String getNature() 
    {
        return nature;
    }

    public void setBrandType(String brandType) 
    {
        this.brandType = brandType;
    }

    public String getBrandType() 
    {
        return brandType;
    }

    public void setEnergy(String energy) 
    {
        this.energy = energy;
    }

    public String getEnergy() 
    {
        return energy;
    }

    public void setRegIssueDate(String regIssueDate) 
    {
        this.regIssueDate = regIssueDate;
    }

    public String getRegIssueDate() 
    {
        return regIssueDate;
    }

    public void setRegistrationDate(String registrationDate) 
    {
        this.registrationDate = registrationDate;
    }

    public String getRegistrationDate() 
    {
        return registrationDate;
    }

    public void setRegistrateFlag(String registrateFlag) 
    {
        this.registrateFlag = registrateFlag;
    }

    public String getRegistrateFlag() 
    {
        return registrateFlag;
    }

    public void setRegistrateReason(String registrateReason) 
    {
        this.registrateReason = registrateReason;
    }

    public String getRegistrateReason() 
    {
        return registrateReason;
    }

    public void setBrandId(String brandId) 
    {
        this.brandId = brandId;
    }

    public String getBrandId() 
    {
        return brandId;
    }

    public void setBrandName(String brandName) 
    {
        this.brandName = brandName;
    }

    public String getBrandName() 
    {
        return brandName;
    }

    public void setSeriesId(String seriesId) 
    {
        this.seriesId = seriesId;
    }

    public String getSeriesId() 
    {
        return seriesId;
    }

    public void setSeriesName(String seriesName) 
    {
        this.seriesName = seriesName;
    }

    public String getSeriesName() 
    {
        return seriesName;
    }

    public void setModelId(String modelId) 
    {
        this.modelId = modelId;
    }

    public String getModelId() 
    {
        return modelId;
    }

    public void setModelName(String modelName) 
    {
        this.modelName = modelName;
    }

    public String getModelName() 
    {
        return modelName;
    }

    public void setCityId(String cityId) 
    {
        this.cityId = cityId;
    }

    public String getCityId() 
    {
        return cityId;
    }

    public void setCityName(String cityName) 
    {
        this.cityName = cityName;
    }

    public String getCityName() 
    {
        return cityName;
    }

    public void setMileAge(Long mileAge) 
    {
        this.mileAge = mileAge;
    }

    public Long getMileAge() 
    {
        return mileAge;
    }

    public void setRegDate(String regDate) 
    {
        this.regDate = regDate;
    }

    public String getRegDate() 
    {
        return regDate;
    }

    public void setModelPrice(BigDecimal modelPrice) 
    {
        this.modelPrice = modelPrice;
    }

    public BigDecimal getModelPrice() 
    {
        return modelPrice;
    }

    public void setDealerPrice(BigDecimal dealerPrice) 
    {
        this.dealerPrice = dealerPrice;
    }

    public BigDecimal getDealerPrice() 
    {
        return dealerPrice;
    }

    public void setHighDealerPrice(BigDecimal highDealerPrice) 
    {
        this.highDealerPrice = highDealerPrice;
    }

    public BigDecimal getHighDealerPrice() 
    {
        return highDealerPrice;
    }

    public void setIndividualPrice(BigDecimal individualPrice) 
    {
        this.individualPrice = individualPrice;
    }

    public BigDecimal getIndividualPrice() 
    {
        return individualPrice;
    }

    public void setDealerBuyPrice(BigDecimal dealerBuyPrice) 
    {
        this.dealerBuyPrice = dealerBuyPrice;
    }

    public BigDecimal getDealerBuyPrice() 
    {
        return dealerBuyPrice;
    }

    public void setIndividualLowSoldPrice(BigDecimal individualLowSoldPrice) 
    {
        this.individualLowSoldPrice = individualLowSoldPrice;
    }

    public BigDecimal getIndividualLowSoldPrice() 
    {
        return individualLowSoldPrice;
    }

    public void setDealerLowBuyPrice(BigDecimal dealerLowBuyPrice) 
    {
        this.dealerLowBuyPrice = dealerLowBuyPrice;
    }

    public BigDecimal getDealerLowBuyPrice() 
    {
        return dealerLowBuyPrice;
    }

    public void setDealerHighSoldPrice(BigDecimal dealerHighSoldPrice) 
    {
        this.dealerHighSoldPrice = dealerHighSoldPrice;
    }

    public BigDecimal getDealerHighSoldPrice() 
    {
        return dealerHighSoldPrice;
    }

    public void setDealerLowSoldPrice(BigDecimal dealerLowSoldPrice) 
    {
        this.dealerLowSoldPrice = dealerLowSoldPrice;
    }

    public BigDecimal getDealerLowSoldPrice() 
    {
        return dealerLowSoldPrice;
    }

    public void setUrl(String url) 
    {
        this.url = url;
    }

    public String getUrl() 
    {
        return url;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setManProvince(String manProvince) 
    {
        this.manProvince = manProvince;
    }

    public String getManProvince() 
    {
        return manProvince;
    }

    public void setManCity(String manCity) 
    {
        this.manCity = manCity;
    }

    public String getManCity() 
    {
        return manCity;
    }

    public void setManBorough(String manBorough) 
    {
        this.manBorough = manBorough;
    }

    public String getManBorough() 
    {
        return manBorough;
    }

    public void setManAddress(String manAddress) 
    {
        this.manAddress = manAddress;
    }

    public String getManAddress() 
    {
        return manAddress;
    }

    public void setManDetailAddress(String manDetailAddress) 
    {
        this.manDetailAddress = manDetailAddress;
    }

    public String getManDetailAddress() 
    {
        return manDetailAddress;
    }

    public void setCarProvince(String carProvince) 
    {
        this.carProvince = carProvince;
    }

    public String getCarProvince() 
    {
        return carProvince;
    }

    public void setCarCity(String carCity) 
    {
        this.carCity = carCity;
    }

    public String getCarCity() 
    {
        return carCity;
    }

    public void setCarBorough(String carBorough) 
    {
        this.carBorough = carBorough;
    }

    public String getCarBorough() 
    {
        return carBorough;
    }

    public void setCarAddress(String carAddress) 
    {
        this.carAddress = carAddress;
    }

    public String getCarAddress() 
    {
        return carAddress;
    }

    public void setCarDetailAddress(String carDetailAddress) 
    {
        this.carDetailAddress = carDetailAddress;
    }

    public String getCarDetailAddress() 
    {
        return carDetailAddress;
    }

    public void setCarLandType(String carLandType) 
    {
        this.carLandType = carLandType;
    }

    public String getCarLandType() 
    {
        return carLandType;
    }

    public void setCarStatus(String carStatus) 
    {
        this.carStatus = carStatus;
    }

    public String getCarStatus() 
    {
        return carStatus;
    }

    public void setGpsStatus(String gpsStatus) 
    {
        this.gpsStatus = gpsStatus;
    }

    public String getGpsStatus() 
    {
        return gpsStatus;
    }

    public void setCarColor(String carColor) 
    {
        this.carColor = carColor;
    }

    public String getCarColor() 
    {
        return carColor;
    }

    public void setModelYear(Long modelYear) 
    {
        this.modelYear = modelYear;
    }

    public Long getModelYear() 
    {
        return modelYear;
    }

    public void setModelLiter(String modelLiter) 
    {
        this.modelLiter = modelLiter;
    }

    public String getModelLiter() 
    {
        return modelLiter;
    }

    public void setModelGear(String modelGear) 
    {
        this.modelGear = modelGear;
    }

    public String getModelGear() 
    {
        return modelGear;
    }

    public void setModelEmissionStandard(String modelEmissionStandard) 
    {
        this.modelEmissionStandard = modelEmissionStandard;
    }

    public String getModelEmissionStandard() 
    {
        return modelEmissionStandard;
    }

    public void setMinRegYear(Long minRegYear) 
    {
        this.minRegYear = minRegYear;
    }

    public Long getMinRegYear() 
    {
        return minRegYear;
    }

    public void setMaxRegYear(Long maxRegYear) 
    {
        this.maxRegYear = maxRegYear;
    }

    public Long getMaxRegYear() 
    {
        return maxRegYear;
    }

    public void setSeriesGroupName(String seriesGroupName) 
    {
        this.seriesGroupName = seriesGroupName;
    }

    public String getSeriesGroupName() 
    {
        return seriesGroupName;
    }

    public void setReportUrl(String reportUrl) 
    {
        this.reportUrl = reportUrl;
    }

    public String getReportUrl() 
    {
        return reportUrl;
    }

    public void setVehicleType(String vehicleType) 
    {
        this.vehicleType = vehicleType;
    }

    public String getVehicleType() 
    {
        return vehicleType;
    }

    public void setSeriesType(String seriesType) 
    {
        this.seriesType = seriesType;
    }

    public String getSeriesType() 
    {
        return seriesType;
    }

    public void setCarState(String carState) 
    {
        this.carState = carState;
    }

    public String getCarState() 
    {
        return carState;
    }

    public void setJzgOrderNo(String jzgOrderNo) 
    {
        this.jzgOrderNo = jzgOrderNo;
    }

    public String getJzgOrderNo() 
    {
        return jzgOrderNo;
    }

    public void setJzgAssessmentPriceSold(BigDecimal jzgAssessmentPriceSold) 
    {
        this.jzgAssessmentPriceSold = jzgAssessmentPriceSold;
    }

    public BigDecimal getJzgAssessmentPriceSold() 
    {
        return jzgAssessmentPriceSold;
    }

    public void setJzgAssessmentPriceBuy(BigDecimal jzgAssessmentPriceBuy) 
    {
        this.jzgAssessmentPriceBuy = jzgAssessmentPriceBuy;
    }

    public BigDecimal getJzgAssessmentPriceBuy() 
    {
        return jzgAssessmentPriceBuy;
    }

    public void setJzgManufacturerPrice(BigDecimal jzgManufacturerPrice) 
    {
        this.jzgManufacturerPrice = jzgManufacturerPrice;
    }

    public BigDecimal getJzgManufacturerPrice() 
    {
        return jzgManufacturerPrice;
    }

    public void setJzgAssessmentReportPdf(String jzgAssessmentReportPdf) 
    {
        this.jzgAssessmentReportPdf = jzgAssessmentReportPdf;
    }

    public String getJzgAssessmentReportPdf() 
    {
        return jzgAssessmentReportPdf;
    }

    public void setJzgStatus(String jzgStatus) 
    {
        this.jzgStatus = jzgStatus;
    }

    public String getJzgStatus() 
    {
        return jzgStatus;
    }

    public void setJzgStatusDes(String jzgStatusDes) 
    {
        this.jzgStatusDes = jzgStatusDes;
    }

    public String getJzgStatusDes() 
    {
        return jzgStatusDes;
    }

    public void setDepartureDate(String departureDate) 
    {
        this.departureDate = departureDate;
    }

    public String getDepartureDate() 
    {
        return departureDate;
    }

    public void setTransferCount(Long transferCount) 
    {
        this.transferCount = transferCount;
    }

    public Long getTransferCount() 
    {
        return transferCount;
    }

    public void setInsuranceCondition(String insuranceCondition) 
    {
        this.insuranceCondition = insuranceCondition;
    }

    public String getInsuranceCondition() 
    {
        return insuranceCondition;
    }

    public void setRegistrationNo(String registrationNo) 
    {
        this.registrationNo = registrationNo;
    }

    public String getRegistrationNo() 
    {
        return registrationNo;
    }

    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }

    public void setOrderStatus(String orderStatus) 
    {
        this.orderStatus = orderStatus;
    }

    public String getOrderStatus() 
    {
        return orderStatus;
    }

    public void setOrderReportUrl(String orderReportUrl) 
    {
        this.orderReportUrl = orderReportUrl;
    }

    public String getOrderReportUrl() 
    {
        return orderReportUrl;
    }

    public void setOrderReportWeb(String orderReportWeb) 
    {
        this.orderReportWeb = orderReportWeb;
    }

    public String getOrderReportWeb() 
    {
        return orderReportWeb;
    }

    public void setBeforePlateNo(String beforePlateNo) 
    {
        this.beforePlateNo = beforePlateNo;
    }

    public String getBeforePlateNo() 
    {
        return beforePlateNo;
    }

    public void setManufacturer(String manufacturer) 
    {
        this.manufacturer = manufacturer;
    }

    public String getManufacturer() 
    {
        return manufacturer;
    }

    public void setSeatNumber(Long seatNumber) 
    {
        this.seatNumber = seatNumber;
    }

    public Long getSeatNumber() 
    {
        return seatNumber;
    }

    public void setWxBrandCode(String wxBrandCode) 
    {
        this.wxBrandCode = wxBrandCode;
    }

    public String getWxBrandCode() 
    {
        return wxBrandCode;
    }

    public void setWxBrandName(String wxBrandName) 
    {
        this.wxBrandName = wxBrandName;
    }

    public String getWxBrandName() 
    {
        return wxBrandName;
    }

    public void setWxSeriesCode(String wxSeriesCode) 
    {
        this.wxSeriesCode = wxSeriesCode;
    }

    public String getWxSeriesCode() 
    {
        return wxSeriesCode;
    }

    public void setWxSeriesName(String wxSeriesName) 
    {
        this.wxSeriesName = wxSeriesName;
    }

    public String getWxSeriesName() 
    {
        return wxSeriesName;
    }

    public void setWxModelCode(String wxModelCode) 
    {
        this.wxModelCode = wxModelCode;
    }

    public String getWxModelCode() 
    {
        return wxModelCode;
    }

    public void setWxModelName(String wxModelName) 
    {
        this.wxModelName = wxModelName;
    }

    public String getWxModelName() 
    {
        return wxModelName;
    }

    public void setWxGuidancePrice(BigDecimal wxGuidancePrice) 
    {
        this.wxGuidancePrice = wxGuidancePrice;
    }

    public BigDecimal getWxGuidancePrice() 
    {
        return wxGuidancePrice;
    }

    public void setWxAssessmentPrice(BigDecimal wxAssessmentPrice) 
    {
        this.wxAssessmentPrice = wxAssessmentPrice;
    }

    public BigDecimal getWxAssessmentPrice() 
    {
        return wxAssessmentPrice;
    }

    public void setWxReportOrderNo(String wxReportOrderNo) 
    {
        this.wxReportOrderNo = wxReportOrderNo;
    }

    public String getWxReportOrderNo() 
    {
        return wxReportOrderNo;
    }

    public void setWxReportUrl(String wxReportUrl) 
    {
        this.wxReportUrl = wxReportUrl;
    }

    public String getWxReportUrl() 
    {
        return wxReportUrl;
    }

    public void setIsAccident(String isAccident) 
    {
        this.isAccident = isAccident;
    }

    public String getIsAccident() 
    {
        return isAccident;
    }

    public void setAvpId(String avpId) 
    {
        this.avpId = avpId;
    }

    public String getAvpId() 
    {
        return avpId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("applyNo", getApplyNo())
            .append("customerId", getCustomerId())
            .append("plateProvince", getPlateProvince())
            .append("plateCity", getPlateCity())
            .append("plateAddress", getPlateAddress())
            .append("mileage", getMileage())
            .append("color", getColor())
            .append("nature", getNature())
            .append("brandType", getBrandType())
            .append("energy", getEnergy())
            .append("regIssueDate", getRegIssueDate())
            .append("registrationDate", getRegistrationDate())
            .append("registrateFlag", getRegistrateFlag())
            .append("registrateReason", getRegistrateReason())
            .append("brandId", getBrandId())
            .append("brandName", getBrandName())
            .append("seriesId", getSeriesId())
            .append("seriesName", getSeriesName())
            .append("modelId", getModelId())
            .append("modelName", getModelName())
            .append("cityId", getCityId())
            .append("cityName", getCityName())
            .append("mileAge", getMileAge())
            .append("regDate", getRegDate())
            .append("modelPrice", getModelPrice())
            .append("dealerPrice", getDealerPrice())
            .append("highDealerPrice", getHighDealerPrice())
            .append("individualPrice", getIndividualPrice())
            .append("dealerBuyPrice", getDealerBuyPrice())
            .append("individualLowSoldPrice", getIndividualLowSoldPrice())
            .append("dealerLowBuyPrice", getDealerLowBuyPrice())
            .append("dealerHighSoldPrice", getDealerHighSoldPrice())
            .append("dealerLowSoldPrice", getDealerLowSoldPrice())
            .append("url", getUrl())
            .append("createBy", getCreateBy())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .append("delFlag", getDelFlag())
            .append("manProvince", getManProvince())
            .append("manCity", getManCity())
            .append("manBorough", getManBorough())
            .append("manAddress", getManAddress())
            .append("manDetailAddress", getManDetailAddress())
            .append("carProvince", getCarProvince())
            .append("carCity", getCarCity())
            .append("carBorough", getCarBorough())
            .append("carAddress", getCarAddress())
            .append("carDetailAddress", getCarDetailAddress())
            .append("carLandType", getCarLandType())
            .append("carStatus", getCarStatus())
            .append("gpsStatus", getGpsStatus())
            .append("carColor", getCarColor())
            .append("modelYear", getModelYear())
            .append("modelLiter", getModelLiter())
            .append("modelGear", getModelGear())
            .append("modelEmissionStandard", getModelEmissionStandard())
            .append("minRegYear", getMinRegYear())
            .append("maxRegYear", getMaxRegYear())
            .append("seriesGroupName", getSeriesGroupName())
            .append("reportUrl", getReportUrl())
            .append("vehicleType", getVehicleType())
            .append("seriesType", getSeriesType())
            .append("carState", getCarState())
            .append("jzgOrderNo", getJzgOrderNo())
            .append("jzgAssessmentPriceSold", getJzgAssessmentPriceSold())
            .append("jzgAssessmentPriceBuy", getJzgAssessmentPriceBuy())
            .append("jzgManufacturerPrice", getJzgManufacturerPrice())
            .append("jzgAssessmentReportPdf", getJzgAssessmentReportPdf())
            .append("jzgStatus", getJzgStatus())
            .append("jzgStatusDes", getJzgStatusDes())
            .append("departureDate", getDepartureDate())
            .append("transferCount", getTransferCount())
            .append("insuranceCondition", getInsuranceCondition())
            .append("registrationNo", getRegistrationNo())
            .append("orderNo", getOrderNo())
            .append("orderStatus", getOrderStatus())
            .append("orderReportUrl", getOrderReportUrl())
            .append("orderReportWeb", getOrderReportWeb())
            .append("beforePlateNo", getBeforePlateNo())
            .append("manufacturer", getManufacturer())
            .append("seatNumber", getSeatNumber())
            .append("wxBrandCode", getWxBrandCode())
            .append("wxBrandName", getWxBrandName())
            .append("wxSeriesCode", getWxSeriesCode())
            .append("wxSeriesName", getWxSeriesName())
            .append("wxModelCode", getWxModelCode())
            .append("wxModelName", getWxModelName())
            .append("wxGuidancePrice", getWxGuidancePrice())
            .append("wxAssessmentPrice", getWxAssessmentPrice())
            .append("wxReportOrderNo", getWxReportOrderNo())
            .append("wxReportUrl", getWxReportUrl())
            .append("isAccident", getIsAccident())
            .append("avpId", getAvpId())
            .toString();
    }
}
