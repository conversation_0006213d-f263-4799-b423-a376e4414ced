package com.ruoyi.findcar.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.findcar.mapper.FindCarMapper;
import com.ruoyi.findcar.domain.FindCar;
import com.ruoyi.findcar.service.IFindCarService;

/**
 * 找车结果上报Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
@Service
public class FindCarServiceImpl implements IFindCarService 
{
    @Autowired
    private FindCarMapper findCarMapper;

    /**
     * 查询找车结果上报
     * 
     * @param id 找车结果上报主键
     * @return 找车结果上报
     */
    @Override
    public FindCar selectFindCarById(String id)
    {
        return findCarMapper.selectFindCarById(id);
    }

    /**
     * 查询找车结果上报列表
     * 
     * @param findCar 找车结果上报
     * @return 找车结果上报
     */
    @Override
    public List<FindCar> selectFindCarList(FindCar findCar)
    {
        return findCarMapper.selectFindCarList(findCar);
    }

    /**
     * 新增找车结果上报
     * 
     * @param findCar 找车结果上报
     * @return 结果
     */
    @Override
    public int insertFindCar(FindCar findCar)
    {
        return findCarMapper.insertFindCar(findCar);
    }

    /**
     * 修改找车结果上报
     * 
     * @param findCar 找车结果上报
     * @return 结果
     */
    @Override
    public int updateFindCar(FindCar findCar)
    {
        return findCarMapper.updateFindCar(findCar);
    }

    /**
     * 批量删除找车结果上报
     * 
     * @param ids 需要删除的找车结果上报主键
     * @return 结果
     */
    @Override
    public int deleteFindCarByIds(String[] ids)
    {
        return findCarMapper.deleteFindCarByIds(ids);
    }

    /**
     * 删除找车结果上报信息
     * 
     * @param id 找车结果上报主键
     * @return 结果
     */
    @Override
    public int deleteFindCarById(String id)
    {
        return findCarMapper.deleteFindCarById(id);
    }
}
