package com.ruoyi.findcar.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.findcar.domain.FindCar;
import com.ruoyi.findcar.service.IFindCarService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 找车结果上报Controller
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
@RestController
@RequestMapping("/findcar/findcar")
public class FindCarController extends BaseController
{
    @Autowired
    private IFindCarService findCarService;

    /**
     * 查询找车结果上报列表
     */
    @PreAuthorize("@ss.hasPermi('findcar:findcar:list')")
    @GetMapping("/list")
    public TableDataInfo list(FindCar findCar)
    {
        startPage();
        List<FindCar> list = findCarService.selectFindCarList(findCar);
        return getDataTable(list);
    }

    /**
     * 导出找车结果上报列表
     */
    @PreAuthorize("@ss.hasPermi('findcar:findcar:export')")
    @Log(title = "找车结果上报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FindCar findCar)
    {
        List<FindCar> list = findCarService.selectFindCarList(findCar);
        ExcelUtil<FindCar> util = new ExcelUtil<FindCar>(FindCar.class);
        util.exportExcel(response, list, "找车结果上报数据");
    }

    /**
     * 获取找车结果上报详细信息
     */
    @PreAuthorize("@ss.hasPermi('findcar:findcar:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(findCarService.selectFindCarById(id));
    }

    /**
     * 新增找车结果上报
     */
    @PreAuthorize("@ss.hasPermi('findcar:findcar:add')")
    @Log(title = "找车结果上报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FindCar findCar)
    {
        return toAjax(findCarService.insertFindCar(findCar));
    }

    /**
     * 修改找车结果上报
     */
    @PreAuthorize("@ss.hasPermi('findcar:findcar:edit')")
    @Log(title = "找车结果上报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FindCar findCar)
    {
        return toAjax(findCarService.updateFindCar(findCar));
    }

    /**
     * 删除找车结果上报
     */
    @PreAuthorize("@ss.hasPermi('findcar:findcar:remove')")
    @Log(title = "找车结果上报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(findCarService.deleteFindCarByIds(ids));
    }
}
