package com.ruoyi.ind_work.mapper;

import java.util.List;
import com.ruoyi.ind_work.domain.IndWork;

/**
 * 个人客户工作信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
public interface IndWorkMapper 
{
    /**
     * 查询个人客户工作信息
     * 
     * @param id 个人客户工作信息主键
     * @return 个人客户工作信息
     */
    public IndWork selectIndWorkById(String id);

    /**
     * 查询个人客户工作信息列表
     * 
     * @param indWork 个人客户工作信息
     * @return 个人客户工作信息集合
     */
    public List<IndWork> selectIndWorkList(IndWork indWork);

    /**
     * 新增个人客户工作信息
     * 
     * @param indWork 个人客户工作信息
     * @return 结果
     */
    public int insertIndWork(IndWork indWork);

    /**
     * 修改个人客户工作信息
     * 
     * @param indWork 个人客户工作信息
     * @return 结果
     */
    public int updateIndWork(IndWork indWork);

    /**
     * 删除个人客户工作信息
     * 
     * @param id 个人客户工作信息主键
     * @return 结果
     */
    public int deleteIndWorkById(String id);

    /**
     * 批量删除个人客户工作信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIndWorkByIds(String[] ids);
}
