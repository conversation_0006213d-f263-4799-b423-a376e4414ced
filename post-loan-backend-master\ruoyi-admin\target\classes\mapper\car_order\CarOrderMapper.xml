<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.car_order.mapper.CarOrderMapper">
    
    <resultMap type="CarOrder" id="CarOrderResult">
        <result property="id"    column="id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="loanId" column="loan_id" />
        <result property="teamId"    column="team_id"    />
        <result property="garageId"    column="garage_id"    />
        <result property="locatingCommission"    column="locating_commission"    />
        <result property="keyStatus"    column="key_status"    />
        <result property="keyTime"    column="key_time"    />
        <result property="collectionMethod"    column="collection_method"    />
        <result property="status"    column="status"    />
        <result property="allocationTime"    column="allocation_time"    />
        <result property="keyProvince"    column="key_province"    />
        <result property="keyCity"    column="key_city"    />
        <result property="keyBorough"    column="key_borough"    />
        <result property="keyAddress"    column="key_address"    />
        <result property="keyDetailAddress"    column="key_detail_address"    />
        <result property="dispatcher"    column="dispatcher"    />
    </resultMap>

    <sql id="selectCarOrderVo">
        select * from car_order
    </sql>

    <select id="selectCarOrderList" parameterType="CarOrder" resultMap="CarOrderResult">
        <include refid="selectCarOrderVo"/>
        <where>  
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="loanId != null "> and loan_id = #{loanId}</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="garageId != null "> and garage_id = #{garageId}</if>
            <if test="inboundTime != null "> and inbound_time = #{inboundTime}</if>
            <if test="outboundTime != null "> and outbound_time = #{outboundTime}</if>
            <if test="locatingCommission != null "> and locating_commission = #{locatingCommission}</if>
            <if test="keyStatus != null "> and key_status = #{keyStatus}</if>
            <if test="keyTime != null "> and key_time = #{keyTime}</if>
            <if test="collectionMethod != null "> and collection_method = #{collectionMethod}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="allocationTime != null "> and allocation_time = #{allocationTime}</if>
            <if test="dispatcher != null"> and dispatcher = #{dispatcher}</if>
        </where>
    </select>
    
    <select id="selectCarOrderById" parameterType="String" resultMap="CarOrderResult">
        <include refid="selectCarOrderVo"/>
        where id = #{id}
    </select>

    <insert id="insertCarOrder" parameterType="CarOrder">
        insert into car_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="applyNo != null">apply_no,</if>
            <if test="loanId != null">loan_id,</if>
            <if test="teamId != null">team_id,</if>
            <if test="garageId != null">garage_id,</if>
            <if test="inboundTime != null">inbound_time,</if>
            <if test="outboundTime != null">outbound_time,</if>
            <if test="locatingCommission != null">locating_commission,</if>
            <if test="keyStatus != null">key_status,</if>
            <if test="keyTime != null">key_time,</if>
            <if test="collectionMethod != null">collection_method,</if>
            <if test="status != null">status,</if>
            <if test="allocationTime != null">allocation_time,</if>
            <if test="dispatcher != null">dispatcher,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="loanId != null">#{loanId},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="garageId != null">#{garageId},</if>
            <if test="inboundTime != null">#{inboundTime},</if>
            <if test="outboundTime != null">#{outboundTime},</if>
            <if test="locatingCommission != null">#{locatingCommission},</if>
            <if test="keyStatus != null">#{keyStatus},</if>
            <if test="keyTime != null">#{keyTime},</if>
            <if test="collectionMethod != null">#{collectionMethod},</if>
            <if test="status != null">#{status},</if>
            <if test="allocationTime != null">#{allocationTime},</if>
            <if test="dispatcher != null">#{dispatcher},</if>
         </trim>
    </insert>

    <update id="updateCarOrder" parameterType="CarOrder">
        update car_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="applyNo != null">apply_no = #{applyNo},</if>
            <if test="loanId != null">loan_id = #{loanId},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="garageId != null">garage_id = #{garageId},</if>
            <if test="inboundTime != null">inbound_time = #{inboundTime},</if>
            <if test="outboundTime != null">outbound_time = #{outboundTime},</if>
            <if test="locatingCommission != null">locating_commission = #{locatingCommission},</if>
            <if test="keyStatus != null">key_status = #{keyStatus},</if>
            <if test="keyTime != null">key_time = #{keyTime},</if>
            <if test="collectionMethod != null">collection_method = #{collectionMethod},</if>
            <if test="status != null">status = #{status},</if>
            <if test="allocationTime != null">allocation_time = #{allocationTime},</if>
            <if test="dispatcher != null">dispatcher = #{dispatcher},</if>
            <if test="keyProvince != null">key_province = #{keyProvince},</if>
            <if test="keyCity != null">key_city = #{keyCity},</if>
            <if test="keyBorough != null">key_borough = #{keyBorough},</if>
            <if test="keyAddress != null">key_address = #{keyAddress},</if>
            <if test="keyDetailAddress != null">key_detail_address = #{keyDetailAddress},</if>

        </trim>
        where id = #{id}
    </update>

    <update id="updateDispatcherByLoanId">
        update car_order set dispatcher = #{dispatcher} where loan_id = #{loanId}
    </update>

    <delete id="deleteCarOrderById" parameterType="String">
        delete from car_order where id = #{id}
    </delete>

    <delete id="deleteCarOrderByIds" parameterType="String">
        delete from car_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>