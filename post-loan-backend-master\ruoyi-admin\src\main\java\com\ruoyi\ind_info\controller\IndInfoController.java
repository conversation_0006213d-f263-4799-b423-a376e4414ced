package com.ruoyi.ind_info.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.ind_info.domain.IndInfo;
import com.ruoyi.ind_info.service.IIndInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 个人客户信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/ind_info/ind_info")
public class IndInfoController extends BaseController
{
    @Autowired
    private IIndInfoService indInfoService;

    /**
     * 查询个人客户信息列表
     */
    @PreAuthorize("@ss.hasPermi('ind_info:ind_info:list')")
    @GetMapping("/list")
    public TableDataInfo list(IndInfo indInfo)
    {
        startPage();
        List<IndInfo> list = indInfoService.selectIndInfoList(indInfo);
        return getDataTable(list);
    }

    /**
     * 导出个人客户信息列表
     */
    @PreAuthorize("@ss.hasPermi('ind_info:ind_info:export')")
    @Log(title = "个人客户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IndInfo indInfo)
    {
        List<IndInfo> list = indInfoService.selectIndInfoList(indInfo);
        ExcelUtil<IndInfo> util = new ExcelUtil<IndInfo>(IndInfo.class);
        util.exportExcel(response, list, "个人客户信息数据");
    }

    /**
     * 获取个人客户信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('ind_info:ind_info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(indInfoService.selectIndInfoById(id));
    }

    /**
     * 新增个人客户信息
     */
    @PreAuthorize("@ss.hasPermi('ind_info:ind_info:add')")
    @Log(title = "个人客户信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IndInfo indInfo)
    {
        return toAjax(indInfoService.insertIndInfo(indInfo));
    }

    /**
     * 修改个人客户信息
     */
    @PreAuthorize("@ss.hasPermi('ind_info:ind_info:edit')")
    @Log(title = "个人客户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IndInfo indInfo)
    {
        return toAjax(indInfoService.updateIndInfo(indInfo));
    }

    /**
     * 删除个人客户信息
     */
    @PreAuthorize("@ss.hasPermi('ind_info:ind_info:remove')")
    @Log(title = "个人客户信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(indInfoService.deleteIndInfoByIds(ids));
    }
}
