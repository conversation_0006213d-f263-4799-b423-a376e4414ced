package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ZgcTrialRepay;

/**
 * 中关村还款试算Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
public interface ZgcTrialRepayMapper 
{
    /**
     * 查询中关村还款试算
     * 
     * @param id 中关村还款试算主键
     * @return 中关村还款试算
     */
    public ZgcTrialRepay selectZgcTrialRepayById(String id);

    /**
     * 查询中关村还款试算列表
     * 
     * @param zgcTrialRepay 中关村还款试算
     * @return 中关村还款试算集合
     */
    public List<ZgcTrialRepay> selectZgcTrialRepayList(ZgcTrialRepay zgcTrialRepay);

    public ZgcTrialRepay selectZgcTrialRepayLists(ZgcTrialRepay zgcTrialRepay);

    /**
     * 新增中关村还款试算
     * 
     * @param zgcTrialRepay 中关村还款试算
     * @return 结果
     */
    public int insertZgcTrialRepay(ZgcTrialRepay zgcTrialRepay);

    /**
     * 修改中关村还款试算
     * 
     * @param zgcTrialRepay 中关村还款试算
     * @return 结果
     */
    public int updateZgcTrialRepay(ZgcTrialRepay zgcTrialRepay);

    /**
     * 删除中关村还款试算
     * 
     * @param id 中关村还款试算主键
     * @return 结果
     */
    public int deleteZgcTrialRepayById(String id);

    /**
     * 批量删除中关村还款试算
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZgcTrialRepayByIds(String[] ids);
}
