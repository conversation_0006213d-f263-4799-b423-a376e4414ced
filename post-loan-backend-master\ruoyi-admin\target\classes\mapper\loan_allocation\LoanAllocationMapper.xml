<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.loan_allocation.mapper.LoanAllocationMapper">
    
    <resultMap type="LoanAllocation" id="LoanAllocationResult">
        <result property="id"    column="id"    />
        <result property="loanId"    column="loan_id"    />
        <result property="assignPerson"    column="assign_person"    />
        <result property="followUp"    column="follow_up"    />
        <result property="followRole"    column="follow_role"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectLoanAllocationVo">
        select id, loan_id, assign_person, follow_up, follow_role, status, del_flag, create_by, create_time, update_by, update_time from loan_allocation
    </sql>

    <select id="selectLoanAllocationList" parameterType="LoanAllocation" resultMap="LoanAllocationResult">
        <include refid="selectLoanAllocationVo"/>
        <where>  
            <if test="loanId != null "> and loan_id = #{loanId}</if>
            <if test="assignPerson != null "> and assign_person = #{assignPerson}</if>
            <if test="followUp != null "> and follow_up = #{followUp}</if>
            <if test="followRole != null  and followRole != ''"> and follow_role = #{followRole}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectLoanAllocationById" parameterType="String" resultMap="LoanAllocationResult">
        <include refid="selectLoanAllocationVo"/>
        where id = #{id}
    </select>

    <insert id="insertLoanAllocation" parameterType="LoanAllocation" useGeneratedKeys="true" keyProperty="id">
        insert into loan_allocation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="loanId != null">loan_id,</if>
            <if test="assignPerson != null">assign_person,</if>
            <if test="followUp != null">follow_up,</if>
            <if test="followRole != null">follow_role,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="loanId != null">#{loanId},</if>
            <if test="assignPerson != null">#{assignPerson},</if>
            <if test="followUp != null">#{followUp},</if>
            <if test="followRole != null">#{followRole},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateLoanAllocation" parameterType="LoanAllocation">
        update loan_allocation
        <trim prefix="SET" suffixOverrides=",">
            <if test="loanId != null">loan_id = #{loanId},</if>
            <if test="assignPerson != null">assign_person = #{assignPerson},</if>
            <if test="followUp != null">follow_up = #{followUp},</if>
            <if test="followRole != null">follow_role = #{followRole},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLoanAllocationById" parameterType="String">
        delete from loan_allocation where id = #{id}
    </delete>

    <delete id="deleteLoanAllocationByIds" parameterType="String">
        delete from loan_allocation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>