package com.ruoyi.ind_car_info.service;

import java.util.List;
import com.ruoyi.ind_car_info.domain.IndCarInfo;

/**
 * 车辆信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IIndCarInfoService 
{
    /**
     * 查询车辆信息
     * 
     * @param id 车辆信息主键
     * @return 车辆信息
     */
    public IndCarInfo selectIndCarInfoById(String applyNo);

    /**
     * 查询车辆信息列表
     * 
     * @param indCarInfo 车辆信息
     * @return 车辆信息集合
     */
    public List<IndCarInfo> selectIndCarInfoList(IndCarInfo indCarInfo);

    /**
     * 新增车辆信息
     * 
     * @param indCarInfo 车辆信息
     * @return 结果
     */
    public int insertIndCarInfo(IndCarInfo indCarInfo);

    /**
     * 修改车辆信息
     * 
     * @param indCarInfo 车辆信息
     * @return 结果
     */
    public int updateIndCarInfo(IndCarInfo indCarInfo);

    /**
     * 批量删除车辆信息
     * 
     * @param ids 需要删除的车辆信息主键集合
     * @return 结果
     */
    public int deleteIndCarInfoByIds(String[] ids);

    /**
     * 删除车辆信息信息
     * 
     * @param id 车辆信息主键
     * @return 结果
     */
    public int deleteIndCarInfoById(String id);
}
