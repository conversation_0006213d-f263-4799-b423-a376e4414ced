package com.ruoyi.account_loan.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.account_loan.domain.AccountLoan;
import com.ruoyi.account_loan.service.IAccountLoanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
// import me.chanjar.weixin.miniapp.config.WxMaConfigStorage;

/**
 * 贷款-贷款信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/account_loan/account_loan")
public class AccountLoanController extends BaseController
{
    @Autowired
    private IAccountLoanService accountLoanService;

    /**
     * 查询贷款-贷款信息列表
     */
    @PreAuthorize("@ss.hasPermi('account_loan:account_loan:list')")
    @GetMapping("/list")
    public TableDataInfo list(AccountLoan accountLoan)
    {
        startPage();
        List<AccountLoan> list = accountLoanService.selectAccountLoanList(accountLoan);
        return getDataTable(list);
    }

    /**
     * 导出贷款-贷款信息列表
     */
    @PreAuthorize("@ss.hasPermi('account_loan:account_loan:export')")
    @Log(title = "贷款-贷款信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AccountLoan accountLoan)
    {
        List<AccountLoan> list = accountLoanService.selectAccountLoanList(accountLoan);
        ExcelUtil<AccountLoan> util = new ExcelUtil<AccountLoan>(AccountLoan.class);
        util.exportExcel(response, list, "贷款-贷款信息数据");
    }

    /**
     * 获取贷款-贷款信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('account_loan:account_loan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(accountLoanService.selectAccountLoanById(id));
    }

    /**
     * 新增贷款-贷款信息
     */
    @PreAuthorize("@ss.hasPermi('account_loan:account_loan:add')")
    @Log(title = "贷款-贷款信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AccountLoan accountLoan)
    {
        return toAjax(accountLoanService.insertAccountLoan(accountLoan));
    }

    /**
     * 修改贷款-贷款信息
     */
    @PreAuthorize("@ss.hasPermi('account_loan:account_loan:edit')")
    @Log(title = "贷款-贷款信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AccountLoan accountLoan)
    {
        return toAjax(accountLoanService.updateAccountLoan(accountLoan));
    }

    /**
     * 删除贷款-贷款信息
     */
    @PreAuthorize("@ss.hasPermi('account_loan:account_loan:remove')")
    @Log(title = "贷款-贷款信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(accountLoanService.deleteAccountLoanByIds(ids));
    }
}
