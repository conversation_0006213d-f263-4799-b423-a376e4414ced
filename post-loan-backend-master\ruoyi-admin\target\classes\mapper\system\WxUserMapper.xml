<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.WxUserMapper">
    
    <resultMap type="WxUser" id="WxUserResult">
        <result property="id"    column="id"    />
        <result property="avatar"    column="avatar"    />
        <result property="monicker"    column="monicker"    />
        <result property="name"    column="name"    />
        <result property="mobile"    column="mobile"    />
        <result property="password"    column="password"    />
        <result property="auth"    column="auth"    />
        <result property="salt"    column="salt"    />
        <result property="gid"    column="gid"    />
        <result property="unionid"    column="unionid"    />
        <result property="openidMiniapp"    column="openid_miniapp"    />
        <result property="openidService"    column="openid_service"    />
        <result property="openidPublic"    column="openid_public"    />
        <result property="openidApp"    column="openid_app"    />
        <result property="openidWeb"    column="openid_web"    />
        <result property="sex"    column="sex"    />
        <result property="deptId"    column="dept_id"    />
        <result property="no"    column="no"    />
        <result property="addip"    column="addip"    />
        <result property="logintime"    column="logintime"    />
        <result property="loginip"    column="loginip"    />
        <result property="loginnum"    column="loginnum"    />
        <result property="attention"    column="attention"    />
        <result property="jointime"    column="jointime"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="area"    column="area"    />
        <result property="provinceCn"    column="province_cn"    />
        <result property="cityCn"    column="city_cn"    />
        <result property="areaCn"    column="area_cn"    />
        <result property="createUid"    column="create_uid"    />
        <result property="lastEditUid"    column="last_edit_uid"    />
        <result property="addtime"    column="addtime"    />
        <result property="edittime"    column="edittime"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectWxUserVo">
        select id, avatar, monicker, name, mobile, password, auth, salt, gid, unionid, openid_miniapp, openid_service, openid_public, openid_app, openid_web, sex, dept_id, no, addip, logintime, loginip, loginnum, attention, jointime, province, city, area, province_cn, city_cn, area_cn, create_uid, last_edit_uid, addtime, edittime, status, del_flag from wx_user
    </sql>

    <select id="selectWxUserList" parameterType="WxUser" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        <where>  
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="monicker != null  and monicker != ''"> and monicker = #{monicker}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="auth != null "> and auth = #{auth}</if>
            <if test="salt != null  and salt != ''"> and salt = #{salt}</if>
            <if test="gid != null "> and gid = #{gid}</if>
            <if test="unionid != null  and unionid != ''"> and unionid = #{unionid}</if>
            <if test="openidMiniapp != null  and openidMiniapp != ''"> and openid_miniapp = #{openidMiniapp}</if>
            <if test="openidService != null  and openidService != ''"> and openid_service = #{openidService}</if>
            <if test="openidPublic != null  and openidPublic != ''"> and openid_public = #{openidPublic}</if>
            <if test="openidApp != null  and openidApp != ''"> and openid_app = #{openidApp}</if>
            <if test="openidWeb != null  and openidWeb != ''"> and openid_web = #{openidWeb}</if>
            <if test="sex != null "> and sex = #{sex}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="no != null  and no != ''"> and no = #{no}</if>
            <if test="addip != null  and addip != ''"> and addip = #{addip}</if>
            <if test="logintime != null "> and logintime = #{logintime}</if>
            <if test="loginip != null  and loginip != ''"> and loginip = #{loginip}</if>
            <if test="loginnum != null "> and loginnum = #{loginnum}</if>
            <if test="attention != null "> and attention = #{attention}</if>
            <if test="jointime != null "> and jointime = #{jointime}</if>
            <if test="province != null "> and province = #{province}</if>
            <if test="city != null "> and city = #{city}</if>
            <if test="area != null "> and area = #{area}</if>
            <if test="provinceCn != null  and provinceCn != ''"> and province_cn = #{provinceCn}</if>
            <if test="cityCn != null  and cityCn != ''"> and city_cn = #{cityCn}</if>
            <if test="areaCn != null  and areaCn != ''"> and area_cn = #{areaCn}</if>
            <if test="createUid != null "> and create_uid = #{createUid}</if>
            <if test="lastEditUid != null "> and last_edit_uid = #{lastEditUid}</if>
            <if test="addtime != null "> and addtime = #{addtime}</if>
            <if test="edittime != null "> and edittime = #{edittime}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectWxUserById" parameterType="Long" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        where id = #{id}
    </select>

    <insert id="insertWxUser" parameterType="WxUser" useGeneratedKeys="true" keyProperty="id">
        insert into wx_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="avatar != null">avatar,</if>
            <if test="monicker != null">monicker,</if>
            <if test="name != null">name,</if>
            <if test="mobile != null">mobile,</if>
            <if test="password != null">password,</if>
            <if test="auth != null">auth,</if>
            <if test="salt != null">salt,</if>
            <if test="gid != null">gid,</if>
            <if test="unionid != null">unionid,</if>
            <if test="openidMiniapp != null">openid_miniapp,</if>
            <if test="openidService != null">openid_service,</if>
            <if test="openidPublic != null">openid_public,</if>
            <if test="openidApp != null">openid_app,</if>
            <if test="openidWeb != null">openid_web,</if>
            <if test="sex != null">sex,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="no != null">no,</if>
            <if test="addip != null">addip,</if>
            <if test="logintime != null">logintime,</if>
            <if test="loginip != null">loginip,</if>
            <if test="loginnum != null">loginnum,</if>
            <if test="attention != null">attention,</if>
            <if test="jointime != null">jointime,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="area != null">area,</if>
            <if test="provinceCn != null">province_cn,</if>
            <if test="cityCn != null">city_cn,</if>
            <if test="areaCn != null">area_cn,</if>
            <if test="createUid != null">create_uid,</if>
            <if test="lastEditUid != null">last_edit_uid,</if>
            <if test="addtime != null">addtime,</if>
            <if test="edittime != null">edittime,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="avatar != null">#{avatar},</if>
            <if test="monicker != null">#{monicker},</if>
            <if test="name != null">#{name},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="password != null">#{password},</if>
            <if test="auth != null">#{auth},</if>
            <if test="salt != null">#{salt},</if>
            <if test="gid != null">#{gid},</if>
            <if test="unionid != null">#{unionid},</if>
            <if test="openidMiniapp != null">#{openidMiniapp},</if>
            <if test="openidService != null">#{openidService},</if>
            <if test="openidPublic != null">#{openidPublic},</if>
            <if test="openidApp != null">#{openidApp},</if>
            <if test="openidWeb != null">#{openidWeb},</if>
            <if test="sex != null">#{sex},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="no != null">#{no},</if>
            <if test="addip != null">#{addip},</if>
            <if test="logintime != null">#{logintime},</if>
            <if test="loginip != null">#{loginip},</if>
            <if test="loginnum != null">#{loginnum},</if>
            <if test="attention != null">#{attention},</if>
            <if test="jointime != null">#{jointime},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="area != null">#{area},</if>
            <if test="provinceCn != null">#{provinceCn},</if>
            <if test="cityCn != null">#{cityCn},</if>
            <if test="areaCn != null">#{areaCn},</if>
            <if test="createUid != null">#{createUid},</if>
            <if test="lastEditUid != null">#{lastEditUid},</if>
            <if test="addtime != null">#{addtime},</if>
            <if test="edittime != null">#{edittime},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateWxUser" parameterType="WxUser">
        update wx_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="monicker != null">monicker = #{monicker},</if>
            <if test="name != null">name = #{name},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="password != null">password = #{password},</if>
            <if test="auth != null">auth = #{auth},</if>
            <if test="salt != null">salt = #{salt},</if>
            <if test="gid != null">gid = #{gid},</if>
            <if test="unionid != null">unionid = #{unionid},</if>
            <if test="openidMiniapp != null">openid_miniapp = #{openidMiniapp},</if>
            <if test="openidService != null">openid_service = #{openidService},</if>
            <if test="openidPublic != null">openid_public = #{openidPublic},</if>
            <if test="openidApp != null">openid_app = #{openidApp},</if>
            <if test="openidWeb != null">openid_web = #{openidWeb},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="no != null">no = #{no},</if>
            <if test="addip != null">addip = #{addip},</if>
            <if test="logintime != null">logintime = #{logintime},</if>
            <if test="loginip != null">loginip = #{loginip},</if>
            <if test="loginnum != null">loginnum = #{loginnum},</if>
            <if test="attention != null">attention = #{attention},</if>
            <if test="jointime != null">jointime = #{jointime},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="provinceCn != null">province_cn = #{provinceCn},</if>
            <if test="cityCn != null">city_cn = #{cityCn},</if>
            <if test="areaCn != null">area_cn = #{areaCn},</if>
            <if test="createUid != null">create_uid = #{createUid},</if>
            <if test="lastEditUid != null">last_edit_uid = #{lastEditUid},</if>
            <if test="addtime != null">addtime = #{addtime},</if>
            <if test="edittime != null">edittime = #{edittime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWxUserById" parameterType="Long">
        delete from wx_user where id = #{id}
    </delete>

    <delete id="deleteWxUserByIds" parameterType="String">
        delete from wx_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>