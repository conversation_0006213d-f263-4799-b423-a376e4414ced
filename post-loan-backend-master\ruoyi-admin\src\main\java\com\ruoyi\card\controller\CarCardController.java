package com.ruoyi.card.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.card.domain.CarCard;
import com.ruoyi.card.service.ICarCardService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 行驶证Controller
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
@RestController
@RequestMapping("/card/card")
public class CarCardController extends BaseController
{
    @Autowired
    private ICarCardService carCardService;

    /**
     * 查询行驶证列表
     */
    @PreAuthorize("@ss.hasPermi('card:card:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarCard carCard)
    {
        startPage();
        List<CarCard> list = carCardService.selectCarCardList(carCard);
        return getDataTable(list);
    }

    /**
     * 导出行驶证列表
     */
    @PreAuthorize("@ss.hasPermi('card:card:export')")
    @Log(title = "行驶证", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarCard carCard)
    {
        List<CarCard> list = carCardService.selectCarCardList(carCard);
        ExcelUtil<CarCard> util = new ExcelUtil<CarCard>(CarCard.class);
        util.exportExcel(response, list, "行驶证数据");
    }

    /**
     * 获取行驶证详细信息
     */
    @PreAuthorize("@ss.hasPermi('card:card:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(carCardService.selectCarCardById(id));
    }

    /**
     * 新增行驶证
     */
    @PreAuthorize("@ss.hasPermi('card:card:add')")
    @Log(title = "行驶证", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CarCard carCard)
    {
        return toAjax(carCardService.insertCarCard(carCard));
    }

    /**
     * 修改行驶证
     */
    @PreAuthorize("@ss.hasPermi('card:card:edit')")
    @Log(title = "行驶证", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CarCard carCard)
    {
        return toAjax(carCardService.updateCarCard(carCard));
    }

    /**
     * 删除行驶证
     */
    @PreAuthorize("@ss.hasPermi('card:card:remove')")
    @Log(title = "行驶证", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(carCardService.deleteCarCardByIds(ids));
    }
}
