package com.ruoyi.litigation_cost_submission.mapper;

import java.util.List;
import com.ruoyi.litigation_cost_submission.domain.LitigationCostSubmissionLimit;

/**
 * 法诉费用提交限制记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface LitigationCostSubmissionLimitMapper 
{
    /**
     * 查询法诉费用提交限制记录
     * 
     * @param id 法诉费用提交限制记录主键
     * @return 法诉费用提交限制记录
     */
    public LitigationCostSubmissionLimit selectLitigationCostSubmissionLimitById(Long id);

    /**
     * 查询法诉费用提交限制记录列表
     * 
     * @param litigationCostSubmissionLimit 法诉费用提交限制记录
     * @return 法诉费用提交限制记录集合
     */
    public List<LitigationCostSubmissionLimit> selectLitigationCostSubmissionLimitList(LitigationCostSubmissionLimit litigationCostSubmissionLimit);

    /**
     * 新增法诉费用提交限制记录
     * 
     * @param litigationCostSubmissionLimit 法诉费用提交限制记录
     * @return 结果
     */
    public int insertLitigationCostSubmissionLimit(LitigationCostSubmissionLimit litigationCostSubmissionLimit);

    /**
     * 修改法诉费用提交限制记录
     * 
     * @param litigationCostSubmissionLimit 法诉费用提交限制记录
     * @return 结果
     */
    public int updateLitigationCostSubmissionLimit(LitigationCostSubmissionLimit litigationCostSubmissionLimit);

    /**
     * 删除法诉费用提交限制记录
     * 
     * @param id 法诉费用提交限制记录主键
     * @return 结果
     */
    public int deleteLitigationCostSubmissionLimitById(Long id);

    /**
     * 批量删除法诉费用提交限制记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLitigationCostSubmissionLimitByIds(Long[] ids);
}
