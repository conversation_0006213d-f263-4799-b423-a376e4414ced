package com.ruoyi.loan_list.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 贷后逾期记录对象 loan_list
 *
 * <AUTHOR>
 * @date 2025-06-21
 */
@Data
public class LoanList extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 催回人 */
    @Excel(name = "催回人")
    private String urgeName;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyId;

    /** 客户编号 */
    @Excel(name = "客户编号")
    private String customerId;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 资金方 银行Id */
    @Excel(name = "资金方 银行Id")
    private String partnerId;

    /** 渠道机构 */
    @Excel(name = "渠道机构")
    private String orgId;

    /** 跟催人姓名 */
    @Excel(name = "跟催人姓名")
    private String followUp;

    /** 贷后文员 */
    @Excel(name = "贷后文员")
    private String dhUser;

    /** 贷后文员指派时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "贷后文员指派时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dhTime;

    /** 贷后文员指派类型 */
    @Excel(name = "贷后文员指派类型  1-未指派  2-已撤销  3-已指派")
    private Integer dhAssignmentType;

    /** 电催员 */
    @Excel(name = "电催员")
    private String urgeUser;

    /** 电催员指派时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "电催员指派时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date urgeTime;

    /** 电催员指派类型 */
    @Excel(name = "电催员指派类型  1-未指派  2-已撤销  3-已指派")
    private Integer urgeAssignmentType;

    /** 上访员 */
    @Excel(name = "上访员")
    private String petitionUser;

    /** 上访员指派时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上访员指派时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date petitionTime;

    /** 上访员指派类型 */
    @Excel(name = "上访员指派类型  1-未指派  2-已撤销  3-已指派")
    private Integer petitionAssignmentType;

    /** 法诉员 */
    @Excel(name = "法诉员")
    private String lawUser;

    /** 法诉员指派时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "法诉员指派时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lawTime;

    /** 法诉员指派类型 */
    @Excel(name = "法诉员指派类型  1-未指派  2-已撤销  3-已指派")
    private Integer lawAssignmentType;

    /** 逾期期数 */
    @Excel(name = "逾期期数")
    private String period;

    /** 实还金额 */
    @Excel(name = "实还金额")
    private BigDecimal realReturnMoney;

    /** 催回日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "催回日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date reminderDate;

    /** 是否强制上访  0-否，1-是 */
    @Excel(name = "是否强制上访  0-否，1-是")
    private String isPetition;

    /** 是否呆账 0-否，1-是 */
    @Excel(name = "是否呆账 0-否，1-是")
    private String badDebt;

    /** 1-已催回，2-部分催回，3-未催回 */
    @Excel(name = "1-已催回，2-部分催回，3-未催回")
    private String status;

    /** 账单类型（1-银行逾期 ，2 代扣逾期 3-银行加代扣逾期 4-代偿逾期） */
    @Excel(name = "账单类型", readConverterExp = "1=-银行逾期,，=2,代=扣逾期,3=-银行加代扣逾期,4=-代偿逾期")
    private String billStatus;

    /** 逾期状态 （
     1、提醒 0-3
     2、电催 3-15
     3、上访  15-30
     4、30-60
     5、60+
     ） */
    @Excel(name = "逾期状态 ", readConverterExp = "=        1、提醒,0=-3        2、电催,3=-15        3、上访,1=5-30        4、30-60        5、60+        ")
    private String slippageStatus;

    /** 催记类型（1-继续联系，2、约定还款，3、无法跟进） */
    @Excel(name = "催记类型", readConverterExp = "1=-继续联系，2、约定还款，3、无法跟进")
    private String followStatus;

    /** 是否延期  0-否，1-是 */
    @Excel(name = "是否延期  0-未申请，1-申请中，2-通过，3-拒绝")
    private String isExtension;

    /** 延期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "延期日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date extensionDate;

    /** 催收人指派时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "催收人指派时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date followTime;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;
}
