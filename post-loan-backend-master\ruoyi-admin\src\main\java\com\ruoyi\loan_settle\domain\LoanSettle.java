package com.ruoyi.loan_settle.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.trial_balance.domain.TrialBalance;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 流程结清对象 loan_settle
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
public class LoanSettle extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    private String startTime;
    private String endTime;

    private TrialBalance trialBalance;

    /** 其他欠款 */
    @Excel(name = "其他欠款")
    private BigDecimal otherDebt;

    /** 总结清金额 */
    @Excel(name = "总结清金额")
    private BigDecimal totalMoney;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyId;

    /** 流程id */
    @Excel(name = "流程id")
    private Long loanId;

    /** 贷款人 */
    @Excel(name = "贷款人")
    private String customerName;

    /** 业务员 */
    @Excel(name = "业务员")
    private String salesman;

    /** 出单渠道 */
    @Excel(name = "出单渠道")
    private String orgName;

    /** 银行id */
    @Excel(name = "银行id")
    private String partnerId;

    /** 放款银行 */
    @Excel(name = "放款银行")
    private String bank;

    private BigDecimal loanAmount;

    /** 1-全额结清，2-减免结清 */
    @Excel(name = "1-全额结清，2-减免结清")
    private Integer status;

    /** 0-跟催员发起，1-贷后试算，2-跟催员提交凭据，3-同意，4-拒绝，5-核对完成 */
    @Excel(name = "0-跟催员发起，1-贷后试算，2-跟催员提交凭据，3-同意，4-拒绝，5-核对完成")
    private Integer examineStatus;

    /** 拒绝理由 */
    @Excel(name = "拒绝理由")
    private String reason;

    /** 银行账户 */
    @Excel(name = "银行账户")
    private String accountNumber1;

    /** 代扣账户 */
    @Excel(name = "代扣账户")
    private String accountNumber2;

    /** 违约金账户 */
    @Excel(name = "违约金账户")
    private String accountNumber3;

    /** 其他欠款账户 */
    @Excel(name = "其他欠款账户")
    private String accountNumber4;

    /** 单期代偿账户 */
    @Excel(name = "单期代偿账户")
    private String accountNumber5;

    /** 银行账户凭据 */
    @Excel(name = "银行账户凭据")
    private String accountImg1;

    /** 代扣账户凭据 */
    @Excel(name = "代扣账户凭据")
    private String accountImg2;

    /** 违约金账户凭据 */
    @Excel(name = "违约金账户凭据")
    private String accountImg3;

    /** 其他欠款账户凭据 */
    @Excel(name = "其他欠款账户凭据")
    private String accountImg4;

    /** 单期代偿账户凭据 */
    @Excel(name = "单期代偿账户凭据")
    private String accountImg5;

    /** 减免申请凭据 */
    @Excel(name = "减免申请凭据")
    private String reductionImg;

    /** 贷款状态 */
    @Excel(name = "1-代偿结清  2-法诉结清  3-贷后结清")
    private Integer loanStatus;

    /** 结清剩余金额 */
    @Excel(name = "结清剩余金额")
    private BigDecimal remainingAmount;

    /** 扣减金额 */
    @Excel(name = "扣减金额")
    private BigDecimal deductionAmount;

    /** 银行账户金额 */
    @Excel(name = "银行账户金额")
    private BigDecimal accountMoney1;

    /** 代扣账户金额 */
    @Excel(name = "代扣账户金额")
    private BigDecimal accountMoney2;

    /** 违约金账户金额 */
    @Excel(name = "违约金账户金额")
    private BigDecimal accountMoney3;

    /** 其他欠款账户金额 */
    @Excel(name = "其他欠款账户金额")
    private BigDecimal accountMoney4;

    /** 违约金还款金额 */
    @Excel(name = "违约金还款金额")
    private BigDecimal pMoney;

    /** 违约金还款凭据 */
    @Excel(name = "违约金还款凭据")
    private String pPrepaymentImg;

    /** 违约金还款账号 */
    @Excel(name = "违约金还款账号")
    private String pAccount;

    /** 客户编号 */
    private String customerId;

    /** 车牌号 */
    private String plateNo;

    /** 逾期天数 */
    @Excel(name = "逾期天数")
    private Integer overdueDays;

    /** 银行还款金额（account_loan.b_repayment_amounts） */
    @Excel(name = "银行还款金额")
    private java.math.BigDecimal bRepaymentAmounts;

    /** 逾期金额（account_loan.overdue_amt） */
    @Excel(name = "逾期金额")
    private java.math.BigDecimal overdueAmt;

    /** 证件号（customer_info.cert_id） */
    @Excel(name = "证件号")
    private String certId;

    /** 创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 修改日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    // ========== Getter and Setter methods ==========
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public TrialBalance getTrialBalance() {
        return trialBalance;
    }

    public void setTrialBalance(TrialBalance trialBalance) {
        this.trialBalance = trialBalance;
    }

    public BigDecimal getOtherDebt() {
        return otherDebt;
    }

    public void setOtherDebt(BigDecimal otherDebt) {
        this.otherDebt = otherDebt;
    }

    public BigDecimal getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(BigDecimal totalMoney) {
        this.totalMoney = totalMoney;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public Long getLoanId() {
        return loanId;
    }

    public void setLoanId(Long loanId) {
        this.loanId = loanId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getSalesman() {
        return salesman;
    }

    public void setSalesman(String salesman) {
        this.salesman = salesman;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public BigDecimal getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(BigDecimal loanAmount) {
        this.loanAmount = loanAmount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getExamineStatus() {
        return examineStatus;
    }

    public void setExamineStatus(Integer examineStatus) {
        this.examineStatus = examineStatus;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getAccountNumber1() {
        return accountNumber1;
    }

    public void setAccountNumber1(String accountNumber1) {
        this.accountNumber1 = accountNumber1;
    }

    public String getAccountNumber2() {
        return accountNumber2;
    }

    public void setAccountNumber2(String accountNumber2) {
        this.accountNumber2 = accountNumber2;
    }

    public String getAccountNumber3() {
        return accountNumber3;
    }

    public void setAccountNumber3(String accountNumber3) {
        this.accountNumber3 = accountNumber3;
    }

    public String getAccountNumber4() {
        return accountNumber4;
    }

    public void setAccountNumber4(String accountNumber4) {
        this.accountNumber4 = accountNumber4;
    }

    public String getAccountNumber5() {
        return accountNumber5;
    }

    public void setAccountNumber5(String accountNumber5) {
        this.accountNumber5 = accountNumber5;
    }

    public String getAccountImg1() {
        return accountImg1;
    }

    public void setAccountImg1(String accountImg1) {
        this.accountImg1 = accountImg1;
    }

    public String getAccountImg2() {
        return accountImg2;
    }

    public void setAccountImg2(String accountImg2) {
        this.accountImg2 = accountImg2;
    }

    public String getAccountImg3() {
        return accountImg3;
    }

    public void setAccountImg3(String accountImg3) {
        this.accountImg3 = accountImg3;
    }

    public String getAccountImg4() {
        return accountImg4;
    }

    public void setAccountImg4(String accountImg4) {
        this.accountImg4 = accountImg4;
    }

    public String getAccountImg5() {
        return accountImg5;
    }

    public void setAccountImg5(String accountImg5) {
        this.accountImg5 = accountImg5;
    }

    public String getReductionImg() {
        return reductionImg;
    }

    public void setReductionImg(String reductionImg) {
        this.reductionImg = reductionImg;
    }

    public Integer getLoanStatus() {
        return loanStatus;
    }

    public void setLoanStatus(Integer loanStatus) {
        this.loanStatus = loanStatus;
    }

    public BigDecimal getRemainingAmount() {
        return remainingAmount;
    }

    public void setRemainingAmount(BigDecimal remainingAmount) {
        this.remainingAmount = remainingAmount;
    }

    public BigDecimal getDeductionAmount() {
        return deductionAmount;
    }

    public void setDeductionAmount(BigDecimal deductionAmount) {
        this.deductionAmount = deductionAmount;
    }

    public BigDecimal getAccountMoney1() {
        return accountMoney1;
    }
    public void setAccountMoney1(BigDecimal accountMoney1) {
        this.accountMoney1 = accountMoney1;
    }
    public BigDecimal getAccountMoney2() {
        return accountMoney2;
    }
    public void setAccountMoney2(BigDecimal accountMoney2) {
        this.accountMoney2 = accountMoney2;
    }
    public BigDecimal getAccountMoney3() {
        return accountMoney3;
    }
    public void setAccountMoney3(BigDecimal accountMoney3) {
        this.accountMoney3 = accountMoney3;
    }
    public BigDecimal getAccountMoney4() {
        return accountMoney4;
    }
    public void setAccountMoney4(BigDecimal accountMoney4) {
        this.accountMoney4 = accountMoney4;
    }

    public BigDecimal getPMoney() {
        return pMoney;
    }
    public void setPMoney(BigDecimal pMoney) {
        this.pMoney = pMoney;
    }
    public String getPPrepaymentImg() {
        return pPrepaymentImg;
    }
    public void setPPrepaymentImg(String pPrepaymentImg) {
        this.pPrepaymentImg = pPrepaymentImg;
    }
    public String getPAccount() {
        return pAccount;
    }
    public void setPAccount(String pAccount) {
        this.pAccount = pAccount;
    }

    public BigDecimal getBRepaymentAmounts() {
        return bRepaymentAmounts;
    }

    public void setBRepaymentAmounts(BigDecimal bRepaymentAmounts) {
        this.bRepaymentAmounts = bRepaymentAmounts;
    }

    public BigDecimal getOverdueAmt() {
        return overdueAmt;
    }

    public void setOverdueAmt(java.math.BigDecimal overdueAmt) {
        this.overdueAmt = overdueAmt;
    }

    public String getCustomerId() {
        return customerId;
    }
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getPlateNo() {
        return plateNo;
    }
    public void setPlateNo(String plateNo) {
        this.plateNo = plateNo;
    }

    public Integer getOverdueDays() {
        return overdueDays;
    }

    public void setOverdueDays(Integer overdueDays) {
        this.overdueDays = overdueDays;
    }

    public String getCertId() {
        return certId;
    }

    public void setCertId(String certId) {
        this.certId = certId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
}
