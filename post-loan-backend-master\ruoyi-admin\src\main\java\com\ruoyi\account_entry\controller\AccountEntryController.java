package com.ruoyi.account_entry.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.account_entry.domain.AccountEntry;
import com.ruoyi.account_entry.service.IAccountEntryService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 入账登记Controller
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/account_entry/account_entry")
public class AccountEntryController extends BaseController {
    @Autowired
    private IAccountEntryService accountEntryService;

    /**
     * 查询入账登记列表
     */
    @PreAuthorize("@ss.hasPermi('account_entry:account_entry:list')")
    @GetMapping("/list")
    public TableDataInfo list(AccountEntry accountEntry) {
        startPage();
        List<AccountEntry> list = accountEntryService.selectAccountEntryList(accountEntry);
        return getDataTable(list);
    }

    /**
     * 导出入账登记列表
     */
    @PreAuthorize("@ss.hasPermi('account_entry:account_entry:export')")
    @Log(title = "入账登记", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AccountEntry accountEntry) {
        List<AccountEntry> list = accountEntryService.selectAccountEntryList(accountEntry);
        ExcelUtil<AccountEntry> util = new ExcelUtil<AccountEntry>(AccountEntry.class);
        util.exportExcel(response, list, "入账登记数据");
    }

    /**
     * 获取入账登记详细信息
     */
    @PreAuthorize("@ss.hasPermi('account_entry:account_entry:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(accountEntryService.selectAccountEntryById(id));
    }

    /**
     * 新增入账登记
     */
    @PreAuthorize("@ss.hasPermi('account_entry:account_entry:add')")
    @Log(title = "入账登记", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AccountEntry accountEntry) {
        return toAjax(accountEntryService.insertAccountEntry(accountEntry));
    }

    /**
     * 批量新增入账登记
     */
    @PreAuthorize("@ss.hasPermi('account_entry:account_entry:add')")
    @Log(title = "入账登记", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public AjaxResult batchAdd(@RequestBody List<AccountEntry> accountEntryList) {
        return toAjax(accountEntryService.batchInsertAccountEntry(accountEntryList));
    }

    /**
     * 修改入账登记
     */
    @PreAuthorize("@ss.hasPermi('account_entry:account_entry:edit')")
    @Log(title = "入账登记", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AccountEntry accountEntry) {
        return toAjax(accountEntryService.updateAccountEntry(accountEntry));
    }

    /**
     * 删除入账登记
     */
    @PreAuthorize("@ss.hasPermi('account_entry:account_entry:remove')")
    @Log(title = "入账登记", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(accountEntryService.deleteAccountEntryByIds(ids));
    }
}
