<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.notification.mapper.NotificationMapper">
    
    <resultMap type="Notification" id="NotificationResult">
        <result property="id"    column="id"    />
        <result property="loanId"    column="loan_id"    />
        <result property="businessType"    column="business_type"    />
        <result property="preStatus"    column="pre_status"    />
        <result property="nodeName"    column="node_name"    />
        <result property="currentStatus"    column="current_status"    />
        <result property="action"    column="action"    />
        <result property="content"    column="content"    />
        <result property="senderName"    column="sender_name"    />
        <result property="pageUrl"    column="page_url"    />
        <result property="sendType"    column="send_type"    />
        <result property="msgType"    column="msg_type"    />
        <result property="eventType"    column="event_type"    />
        <result property="processType"    column="process_type"    />
        <result property="nextNotifyTime"    column="next_notify_time"    />
        <result property="handleStatus"    column="handle_status"    />
        <result property="handleBy"    column="handle_by"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="handleRemark"    column="handle_remark"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectNotificationVo">
        select id, loan_id, business_type, pre_status, node_name, current_status, action, content, sender_name, page_url, send_type, msg_type, event_type, process_type, next_notify_time, handle_status, handle_by, handle_time, handle_remark, create_time from notification
    </sql>

    <select id="selectNotificationList" parameterType="Notification" resultMap="NotificationResult">
        <include refid="selectNotificationVo"/>
        <where>  
            <if test="loanId != null "> and loan_id = #{loanId}</if>
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="preStatus != null  and preStatus != ''"> and pre_status = #{preStatus}</if>
            <if test="nodeName != null  and nodeName != ''"> and node_name like concat('%', #{nodeName}, '%')</if>
            <if test="currentStatus != null  and currentStatus != ''"> and current_status = #{currentStatus}</if>
            <if test="action != null  and action != ''"> and action = #{action}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="senderName != null  and senderName != ''"> and sender_name like concat('%', #{senderName}, '%')</if>
            <if test="pageUrl != null  and pageUrl != ''"> and page_url = #{pageUrl}</if>
            <if test="sendType != null  and sendType != ''"> and send_type = #{sendType}</if>
            <if test="msgType != null  and msgType != ''"> and msg_type = #{msgType}</if>
            <if test="eventType != null  and eventType != ''"> and event_type = #{eventType}</if>
            <if test="processType != null  and processType != ''"> and process_type = #{processType}</if>
            <if test="nextNotifyTime != null "> and next_notify_time = #{nextNotifyTime}</if>
            <if test="handleStatus != null  and handleStatus != ''"> and handle_status = #{handleStatus}</if>
        </where>
    </select>
    
    <select id="selectNotificationById" parameterType="Long" resultMap="NotificationResult">
        <include refid="selectNotificationVo"/>
        where id = #{id}
    </select>
    
    <!-- 查询需要发送通知的记录（下次通知时间已到且未处理的） -->
    <select id="selectNotificationByNextNotifyTime" resultMap="NotificationResult">
        <include refid="selectNotificationVo"/>
        where next_notify_time &lt;= sysdate() 
        and (handle_status is null or handle_status = '0')
    </select>

    <insert id="insertNotification" parameterType="Notification" useGeneratedKeys="true" keyProperty="id">
        insert into notification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="loanId != null">loan_id,</if>
            <if test="businessType != null">business_type,</if>
            <if test="preStatus != null">pre_status,</if>
            <if test="nodeName != null and nodeName != ''">node_name,</if>
            <if test="currentStatus != null">current_status,</if>
            <if test="action != null and action != ''">action,</if>
            <if test="content != null">content,</if>
            <if test="senderName != null and senderName != ''">sender_name,</if>
            <if test="pageUrl != null">page_url,</if>
            <if test="sendType != null">send_type,</if>
            <if test="msgType != null">msg_type,</if>
            <if test="eventType != null">event_type,</if>
            <if test="processType != null">process_type,</if>
            <if test="nextNotifyTime != null">next_notify_time,</if>
            <if test="handleStatus != null">handle_status,</if>
            <if test="handleBy != null">handle_by,</if>
            <if test="handleTime != null">handle_time,</if>
            <if test="handleRemark != null">handle_remark,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="loanId != null">#{loanId},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="preStatus != null">#{preStatus},</if>
            <if test="nodeName != null and nodeName != ''">#{nodeName},</if>
            <if test="currentStatus != null">#{currentStatus},</if>
            <if test="action != null and action != ''">#{action},</if>
            <if test="content != null">#{content},</if>
            <if test="senderName != null and senderName != ''">#{senderName},</if>
            <if test="pageUrl != null">#{pageUrl},</if>
            <if test="sendType != null">#{sendType},</if>
            <if test="msgType != null">#{msgType},</if>
            <if test="eventType != null">#{eventType},</if>
            <if test="processType != null">#{processType},</if>
            <if test="nextNotifyTime != null">#{nextNotifyTime},</if>
            <if test="handleStatus != null">#{handleStatus},</if>
            <if test="handleBy != null">#{handleBy},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="handleRemark != null">#{handleRemark},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateNotification" parameterType="Notification">
        update notification
        <trim prefix="SET" suffixOverrides=",">
            <if test="loanId != null">loan_id = #{loanId},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="preStatus != null">pre_status = #{preStatus},</if>
            <if test="nodeName != null and nodeName != ''">node_name = #{nodeName},</if>
            <if test="currentStatus != null">current_status = #{currentStatus},</if>
            <if test="action != null and action != ''">action = #{action},</if>
            <if test="content != null">content = #{content},</if>
            <if test="senderName != null and senderName != ''">sender_name = #{senderName},</if>
            <if test="pageUrl != null">page_url = #{pageUrl},</if>
            <if test="sendType != null">send_type = #{sendType},</if>
            <if test="msgType != null">msg_type = #{msgType},</if>
            <if test="eventType != null">event_type = #{eventType},</if>
            <if test="processType != null">process_type = #{processType},</if>
            <if test="nextNotifyTime != null">next_notify_time = #{nextNotifyTime},</if>
            <if test="nextNotifyTime == null">next_notify_time = null,</if>
            <if test="handleStatus != null">handle_status = #{handleStatus},</if>
            <if test="handleBy != null">handle_by = #{handleBy},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="handleRemark != null">handle_remark = #{handleRemark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNotificationById" parameterType="Long">
        delete from notification where id = #{id}
    </delete>

    <delete id="deleteNotificationByIds" parameterType="String">
        delete from notification where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>