package com.ruoyi.card.service;

import java.util.List;
import com.ruoyi.card.domain.CarCard;

/**
 * 行驶证Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ICarCardService 
{
    /**
     * 查询行驶证
     * 
     * @param id 行驶证主键
     * @return 行驶证
     */
    public CarCard selectCarCardById(String id);

    /**
     * 查询行驶证列表
     * 
     * @param carCard 行驶证
     * @return 行驶证集合
     */
    public List<CarCard> selectCarCardList(CarCard carCard);

    /**
     * 新增行驶证
     * 
     * @param carCard 行驶证
     * @return 结果
     */
    public int insertCarCard(CarCard carCard);

    /**
     * 修改行驶证
     * 
     * @param carCard 行驶证
     * @return 结果
     */
    public int updateCarCard(CarCard carCard);

    /**
     * 批量删除行驶证
     * 
     * @param ids 需要删除的行驶证主键集合
     * @return 结果
     */
    public int deleteCarCardByIds(String[] ids);

    /**
     * 删除行驶证信息
     * 
     * @param id 行驶证主键
     * @return 结果
     */
    public int deleteCarCardById(String id);
}
