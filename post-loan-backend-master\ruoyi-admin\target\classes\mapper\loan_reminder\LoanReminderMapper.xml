<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.loan_reminder.mapper.LoanReminderMapper">

    <resultMap type="LoanReminder" id="LoanReminderResult">
        <result property="id"    column="id"    />
        <result property="loanId"    column="loan_id"    />
        <result property="userId"    column="user_id"    />
        <result property="identity"    column="identity"    />
        <result property="customerName"    column="customer_name"    />
        <result property="customerMobile"    column="customer_mobile"    />
        <result property="carStatus"    column="car_status"    />
        <result property="repaymentStatus"    column="repayment_status"    />
        <result property="examineStatus"    column="examine_status"    />
        <result property="examineReason"    column="examine_reason"    />
        <result property="bMoney"    column="B_money"    />
        <result property="dMoney"    column="D_money"    />
        <result property="oMoney"    column="O_money"    />
        <result property="pMoney"    column="P_money"    />
        <result property="cMoney"    column="C_money"    />
        <result property="bRepaymentImg"    column="B_repayment_img"    />
        <result property="dRepaymentImg"    column="D_repayment_img"    />
        <result property="oRepaymentImg"    column="O_repayment_img"    />
        <result property="pRepaymentImg"    column="P_repayment_img"    />
        <result property="cRepaymentImg"    column="C_repayment_img"    />
        <result property="bAccount"    column="B_account"    />
        <result property="dAccount"    column="D_account"    />
        <result property="oAccount"    column="O_account"    />
        <result property="pAccount"    column="P_account"    />
        <result property="cAccount"    column="C_account"    />
        <result property="urgeStatus"    column="urge_status"    />
        <result property="urgeDescribe"    column="urge_describe"    />
        <result property="urgeMoney"    column="urge_money"    />
        <result property="appointedTime"    column="appointed_time"    />
        <result property="trackingTime"    column="tracking_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="fundsRepayment"    column="funds_repayment"    />
        <result property="fundsAmount"    column="funds_amount"    />
        <result property="fundsAccountType"    column="funds_account_type"    />
        <result property="fundsImage"    column="funds_image"    />
        <result property="litigationId"    column="litigation_id"    />
        <result property="borrower" column="borrower" />
        <result property="issuingChannel" column="issuing_channel" />
        <result property="lendingBank" column="lending_bank" />
        <!-- 新增account_loan相关字段 -->
        <result property="alLendingBank" column="al_lending_bank" />
        <result property="alBMoney" column="al_b_money" />
        <result property="alDMoney" column="al_d_money" />
        <result property="bOverdueAmount" column="b_overdue_amount" />
        <result property="dOverdueAmount" column="d_overdue_amount" />
        <!-- 新增前端需要的字段 -->
        <result property="customerId" column="customer_id" />
        <result property="applyId" column="apply_id" />
        <result property="certId" column="cert_id" />
        <result property="plateNo" column="plate_no" />
        <result property="salesman" column="salesman" />
        <result property="jgName" column="jg_name" />
        <result property="followUp" column="follow_up" />
        <result property="lendingBank" column="lending_bank" />
        <result property="bankyqMoney" column="bankyq_money" />
        <result property="dkyqMoney" column="dkyq_money" />
        <!-- 新增还款相关字段（只使用确实存在的字段） -->
        <result property="productName" column="product_name" />
        <result property="nextInstalmentAmt" column="next_instalment_amt" />
        <result property="bRepaymentAmounts" column="b_repayment_amounts" />
        <result property="dRepaymentAmounts" column="d_repayment_amounts" />
        <result property="bNowMoney" column="b_now_money" />
        <result property="dNowMoney" column="d_now_money" />
        <result property="bReturnTime" column="b_return_time" jdbcType="VARCHAR" />
        <result property="dReturnTime" column="d_return_time" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="selectLoanReminderVo">
        select lr.*,
               ll.customer_name as borrower,
               ll.customer_id,
               ll.apply_id,
               ll.follow_up,
               so.name as issuing_channel,
               so.name as jg_name,
               pi.org_name as al_lending_bank,
               pi.org_name as lending_bank,
               ci.cert_id,
               cc.plate_no,
               ll.create_by as salesman,
               al.b_overdue_amount as bankyq_money,
               al.d_overdue_amount as dkyq_money,
               -- 新增还款相关字段（只使用确实存在的字段）
               COALESCE(prod.product_name, '未知产品') as product_name,
               al.next_instalment_amt,
               al.b_repayment_amounts,
               al.d_repayment_amounts,
               al.b_now_money,
               al.d_now_money,
               CASE
                   WHEN al.b_return_time IS NOT NULL AND al.b_return_time != ''
                   THEN FROM_UNIXTIME(al.b_return_time/1000, '%Y-%m-%d')
                   ELSE NULL
               END as b_return_time,
               CASE
                   WHEN al.d_return_time IS NOT NULL AND al.d_return_time != ''
                   THEN FROM_UNIXTIME(al.d_return_time/1000, '%Y-%m-%d')
                   ELSE NULL
               END as d_return_time
        from loan_reminder lr
                 left join loan_list ll on lr.loan_id = ll.id
                 left join account_loan al on ll.apply_id = al.apply_id
                 left join partner_info pi on al.partner_id = pi.id
                 left join sys_office so on al.org_id = so.id
                 left join customer_info ci on ll.customer_id = ci.id
                 left join car_card cc on ll.apply_id = cc.apply_no
                 left join product_info prod on al.product_id = prod.id
    </sql>

    <select id="selectLoanReminderList" parameterType="LoanReminder" resultMap="LoanReminderResult">
        <include refid="selectLoanReminderVo"/>
        <where>
            <if test="loanId != null "> and lr.loan_id = #{loanId}</if>
            <if test="userId != null  and userId != ''"> and lr.user_id = #{userId}</if>
            <if test="identity != null  and identity != ''"> and FIND_IN_SET(lr.identity,#{identity})</if>
            <if test="customerName != null  and customerName != ''"> and lr.customer_name like concat('%', #{customerName}, '%')</if>
            <if test="customerMobile != null  and customerMobile != ''"> and lr.customer_mobile = #{customerMobile}</if>
            <if test="certId != null  and certId != ''"> and ci.cert_id like concat('%', #{certId}, '%')</if>
            <if test="plateNo != null  and plateNo != ''"> and cc.plate_no like concat('%', #{plateNo}, '%')</if>
            <if test="salesman != null  and salesman != ''"> and ll.create_by like concat('%', #{salesman}, '%')</if>
            <if test="jgName != null  and jgName != ''"> and so.name like concat('%', #{jgName}, '%')</if>
            <if test="alLendingBank != null  and alLendingBank != ''"> and pi.org_name like concat('%', #{alLendingBank}, '%')</if>
            <if test="productName != null  and productName != ''"> and prod.product_name like concat('%', #{productName}, '%')</if>
            <if test="followUp != null  and followUp != ''"> and ll.follow_up like concat('%', #{followUp}, '%')</if>
            <if test="carStatus != null  and carStatus != ''"> and lr.car_status = #{carStatus}</if>
            <if test="repaymentStatus != null "> and lr.repayment_status = #{repaymentStatus}</if>
            <if test="examineStatus != null "> and lr.examine_status = #{examineStatus}</if>
            <if test="examineReason != null  and examineReason != ''"> and lr.examine_reason = #{examineReason}</if>
            <if test="bMoney != null "> and lr.B_money = #{bMoney}</if>
            <if test="dMoney != null "> and lr.D_money = #{dMoney}</if>
            <if test="oMoney != null "> and lr.O_money = #{oMoney}</if>
            <if test="pMoney != null "> and lr.P_money = #{pMoney}</if>
            <!-- P_money: 违约金还款金额 -->
            <if test="pRepaymentImg != null  and pRepaymentImg != ''"> and lr.P_repayment_img = #{pRepaymentImg}</if>
            <!-- P_repayment_img: 违约金还款凭据 -->
            <if test="pAccount != null  and pAccount != ''"> and lr.P_account = #{pAccount}</if>
            <!-- P_account: 违约金银行账户 -->
            <if test="cMoney != null "> and lr.C_money = #{cMoney}</if>
            <if test="bRepaymentImg != null  and bRepaymentImg != ''"> and lr.B_repayment_img = #{bRepaymentImg}</if>
            <if test="dRepaymentImg != null  and dRepaymentImg != ''"> and lr.D_repayment_img = #{dRepaymentImg}</if>
            <if test="oRepaymentImg != null  and oRepaymentImg != ''"> and lr.O_repayment_img = #{oRepaymentImg}</if>
            <if test="cRepaymentImg != null  and cRepaymentImg != ''"> and lr.C_repayment_img = #{cRepaymentImg}</if>
            <if test="bAccount != null  and bAccount != ''"> and lr.B_account = #{bAccount}</if>
            <if test="dAccount != null  and dAccount != ''"> and lr.D_account = #{dAccount}</if>
            <if test="oAccount != null  and oAccount != ''"> and lr.O_account = #{oAccount}</if>
            <if test="cAccount != null  and cAccount != ''"> and lr.C_account = #{cAccount}</if>
            <if test="urgeStatus != null "> and lr.urge_status = #{urgeStatus}</if>
            <if test="urgeDescribe != null  and urgeDescribe != ''"> and lr.urge_describe = #{urgeDescribe}</if>
            <if test="urgeMoney != null "> and lr.urge_money = #{urgeMoney}</if>
            <if test="appointedTime != null "> and lr.appointed_time = #{appointedTime}</if>
            <if test="trackingTime != null "> and lr.tracking_time = #{trackingTime}</if>
            <if test="status != null "> and FIND_IN_SET( lr.status  ,#{status})</if>
            <if test="fundsRepayment != null  and fundsRepayment != ''"> and lr.funds_repayment = #{fundsRepayment}</if>
            <if test="fundsAmount != null "> and lr.funds_amount = #{fundsAmount}</if>
            <if test="fundsAccountType != null  and fundsAccountType != ''"> and lr.funds_account_type = #{fundsAccountType}</if>
            <if test="fundsImage != null  and fundsImage != ''"> and lr.funds_image = #{fundsImage}</if>
            <if test="litigationId != null "> and lr.litigation_id = #{litigationId}</if>
            <if test="bOverdueAmount != null "> and lr.b_overdue_amount = #{bOverdueAmount}</if>
            <if test="dOverdueAmount != null "> and lr.d_overdue_amount = #{dOverdueAmount}</if>
            <if test="startTime != null and startTime != ''"> and date_format(lr.create_time,'%Y-%m-%d') &gt;= #{startTime}</if>
            <if test="endTime != null and endTime != ''"> and date_format(lr.create_time,'%Y-%m-%d') &lt;= #{endTime}</if>
        </where>
    </select>

    <select id="selectLoanReminderListDetail" parameterType="LoanReminder" resultMap="LoanReminderResult">
        <include refid="selectLoanReminderVo"/>
        <where>
            <if test="loanId != null "> and lr.loan_id = #{loanId}</if>
            <if test="status != null "> and lr.status = #{status}</if>
        </where>
    </select>

    <select id="selectLoanReminderById" parameterType="String" resultMap="LoanReminderResult">
        <include refid="selectLoanReminderVo"/>
        where lr.id = #{id}
    </select>
    
    <select id="selectLoanRemindersByLoanIds" parameterType="java.util.List" resultMap="LoanReminderResult">
        <include refid="selectLoanReminderVo"/>
        where lr.loan_id in
        <foreach collection="list" item="loanId" open="(" separator="," close=")">
            #{loanId}
        </foreach>
        <!-- 查询所有状态的催记，在代码中进行过滤 -->
    </select>

    <!-- 专门查询法诉日志的方法 - 每个loan_id只返回最新的一条记录 -->
    <select id="selectLitigationRemindersByLoanIds" parameterType="java.util.List" resultMap="LoanReminderResult">
        SELECT t1.* FROM (
            <include refid="selectLoanReminderVo"/>
            where lr.loan_id in
            <foreach collection="list" item="loanId" open="(" separator="," close=")">
                #{loanId}
            </foreach>
            and lr.status = 2 <!-- 只查询法诉日志 -->
        ) t1
        INNER JOIN (
            SELECT loan_id, MAX(create_time) as max_time
            FROM loan_reminder
            WHERE loan_id IN
            <foreach collection="list" item="loanId" open="(" separator="," close=")">
                #{loanId}
            </foreach>
            AND status = 2
            GROUP BY loan_id
        ) t2 ON t1.loan_id = t2.loan_id AND t1.create_time = t2.max_time
    </select>

    <select id="selectLatestLoanReminderByLitigationId" parameterType="Long" resultMap="LoanReminderResult">
        <include refid="selectLoanReminderVo"/>
        where lr.litigation_id = #{litigationId}
        order by lr.create_time desc
        limit 1
    </select>

    <!-- 根据法诉案件ID列表批量查询最新催记记录 -->
    <select id="selectLatestLoanRemindersByLitigationIds" parameterType="java.util.List" resultMap="LoanReminderResult">
        SELECT t1.* FROM (
            <include refid="selectLoanReminderVo"/>
            where lr.litigation_id in
            <foreach collection="list" item="litigationId" open="(" separator="," close=")">
                #{litigationId}
            </foreach>
        ) t1
        INNER JOIN (
            SELECT litigation_id, MAX(create_time) as max_time
            FROM loan_reminder
            WHERE litigation_id IN
            <foreach collection="list" item="litigationId" open="(" separator="," close=")">
                #{litigationId}
            </foreach>
            GROUP BY litigation_id
        ) t2 ON t1.litigation_id = t2.litigation_id AND t1.create_time = t2.max_time
    </select>

    <insert id="insertLoanReminder" parameterType="LoanReminder" useGeneratedKeys="true" keyProperty="id">
        insert into loan_reminder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="loanId != null">loan_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="identity != null">identity,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="customerMobile != null">customer_mobile,</if>
            <if test="carStatus != null">car_status,</if>
            <if test="repaymentStatus != null">repayment_status,</if>
            <if test="examineStatus != null">examine_status,</if>
            <if test="examineReason != null">examine_reason,</if>
            <if test="bMoney != null">B_money,</if>
            <if test="dMoney != null">D_money,</if>
            <if test="oMoney != null">O_money,</if>
            <if test="pMoney != null">P_money,</if>
            <if test="cMoney != null">C_money,</if>
            <if test="bRepaymentImg != null">B_repayment_img,</if>
            <if test="dRepaymentImg != null">D_repayment_img,</if>
            <if test="oRepaymentImg != null">O_repayment_img,</if>
            <if test="pRepaymentImg != null">P_repayment_img,</if>
            <if test="cRepaymentImg != null">C_repayment_img,</if>
            <if test="bAccount != null">B_account,</if>
            <if test="dAccount != null">D_account,</if>
            <if test="oAccount != null">O_account,</if>
            <if test="pAccount != null">P_account,</if>
            <if test="cAccount != null">C_account,</if>
            <if test="urgeStatus != null">urge_status,</if>
            <if test="urgeDescribe != null">urge_describe,</if>
            <if test="urgeMoney != null">urge_money,</if>
            <if test="appointedTime != null">appointed_time,</if>
            <if test="trackingTime != null">tracking_time,</if>
            <if test="status != null">status,</if>
            <if test="fundsRepayment != null">funds_repayment,</if>
            <if test="fundsAmount != null">funds_amount,</if>
            <if test="fundsAccountType != null">funds_account_type,</if>
            <if test="fundsImage != null">funds_image,</if>
            <if test="litigationId != null">litigation_id,</if>
            <if test="bOverdueAmount != null">b_overdue_amount,</if>
            <if test="dOverdueAmount != null">d_overdue_amount,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="loanId != null">#{loanId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="identity != null">#{identity},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="customerMobile != null">#{customerMobile},</if>
            <if test="carStatus != null">#{carStatus},</if>
            <if test="repaymentStatus != null">#{repaymentStatus},</if>
            <if test="examineStatus != null">#{examineStatus},</if>
            <if test="examineReason != null">#{examineReason},</if>
            <if test="bMoney != null">#{bMoney},</if>
            <if test="dMoney != null">#{dMoney},</if>
            <if test="oMoney != null">#{oMoney},</if>
            <if test="pMoney != null">#{pMoney},</if>
            <if test="cMoney != null">#{cMoney},</if>
            <if test="bRepaymentImg != null">#{bRepaymentImg},</if>
            <if test="dRepaymentImg != null">#{dRepaymentImg},</if>
            <if test="oRepaymentImg != null">#{oRepaymentImg},</if>
            <if test="pRepaymentImg != null">#{pRepaymentImg},</if>
            <if test="cRepaymentImg != null">#{cRepaymentImg},</if>
            <if test="bAccount != null">#{bAccount},</if>
            <if test="dAccount != null">#{dAccount},</if>
            <if test="oAccount != null">#{oAccount},</if>
            <if test="pAccount != null">#{pAccount},</if>
            <if test="cAccount != null">#{cAccount},</if>
            <if test="urgeStatus != null">#{urgeStatus},</if>
            <if test="urgeDescribe != null">#{urgeDescribe},</if>
            <if test="urgeMoney != null">#{urgeMoney},</if>
            <if test="appointedTime != null">#{appointedTime},</if>
            <if test="trackingTime != null">#{trackingTime},</if>
            <if test="status != null">#{status},</if>
            <if test="fundsRepayment != null">#{fundsRepayment},</if>
            <if test="fundsAmount != null">#{fundsAmount},</if>
            <if test="fundsAccountType != null">#{fundsAccountType},</if>
            <if test="fundsImage != null">#{fundsImage},</if>
            <if test="litigationId != null">#{litigationId},</if>
            <if test="bOverdueAmount != null">#{bOverdueAmount},</if>
            <if test="dOverdueAmount != null">#{dOverdueAmount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateLoanReminder" parameterType="LoanReminder">
        update loan_reminder
        <trim prefix="SET" suffixOverrides=",">
            <if test="loanId != null">loan_id = #{loanId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="identity != null">identity = #{identity},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="customerMobile != null">customer_mobile = #{customerMobile},</if>
            <if test="carStatus != null">car_status = #{carStatus},</if>
            <if test="repaymentStatus != null">repayment_status = #{repaymentStatus},</if>
            <if test="examineStatus != null">examine_status = #{examineStatus},</if>
            <if test="examineReason != null">examine_reason = #{examineReason},</if>
            <if test="bMoney != null">B_money = #{bMoney},</if>
            <if test="dMoney != null">D_money = #{dMoney},</if>
            <if test="oMoney != null">O_money = #{oMoney},</if>
            <if test="pMoney != null">P_money = #{pMoney},</if>
            <if test="cMoney != null">C_money = #{cMoney},</if>
            <if test="bRepaymentImg != null">B_repayment_img = #{bRepaymentImg},</if>
            <if test="dRepaymentImg != null">D_repayment_img = #{dRepaymentImg},</if>
            <if test="oRepaymentImg != null">O_repayment_img = #{oRepaymentImg},</if>
            <if test="pRepaymentImg != null">P_repayment_img = #{pRepaymentImg},</if>
            <if test="cRepaymentImg != null">C_repayment_img = #{cRepaymentImg},</if>
            <if test="bAccount != null">B_account = #{bAccount},</if>
            <if test="dAccount != null">D_account = #{dAccount},</if>
            <if test="oAccount != null">O_account = #{oAccount},</if>
            <if test="pAccount != null">P_account = #{pAccount},</if>
            <if test="cAccount != null">C_account = #{cAccount},</if>
            <if test="urgeStatus != null">urge_status = #{urgeStatus},</if>
            <if test="urgeDescribe != null">urge_describe = #{urgeDescribe},</if>
            <if test="urgeMoney != null">urge_money = #{urgeMoney},</if>
            <if test="appointedTime != null">appointed_time = #{appointedTime},</if>
            <if test="trackingTime != null">tracking_time = #{trackingTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="fundsRepayment != null">funds_repayment = #{fundsRepayment},</if>
            <if test="fundsAmount != null">funds_amount = #{fundsAmount},</if>
            <if test="fundsAccountType != null">funds_account_type = #{fundsAccountType},</if>
            <if test="fundsImage != null">funds_image = #{fundsImage},</if>
            <if test="litigationId != null">litigation_id = #{litigationId},</if>
            <if test="bOverdueAmount != null">b_overdue_amount = #{bOverdueAmount},</if>
            <if test="dOverdueAmount != null">d_overdue_amount = #{dOverdueAmount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLoanReminderById" parameterType="String">
        delete from loan_reminder where id = #{id}
    </delete>

    <delete id="deleteLoanReminderByIds" parameterType="String">
        delete from loan_reminder where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateLoanReminderApproval" parameterType="LoanReminder">
        update loan_reminder
        <trim prefix="SET" suffixOverrides=",">
            <if test="examineStatus != null">examine_status = #{examineStatus},</if>
            <if test="examineReason != null">examine_reason = #{examineReason},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 获取产品列表 -->
    <select id="selectProductList" resultType="map">
        SELECT DISTINCT
            prod.product_name as value,
            prod.product_name as label
        FROM product_info prod
        WHERE prod.product_name IS NOT NULL
        AND prod.product_name != ''
        ORDER BY prod.product_name
    </select>
</mapper>