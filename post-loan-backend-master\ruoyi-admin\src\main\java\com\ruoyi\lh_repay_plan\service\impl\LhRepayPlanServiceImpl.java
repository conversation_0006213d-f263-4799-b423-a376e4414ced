package com.ruoyi.lh_repay_plan.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.lh_repay_plan.mapper.LhRepayPlanMapper;
import com.ruoyi.lh_repay_plan.domain.LhRepayPlan;
import com.ruoyi.lh_repay_plan.service.ILhRepayPlanService;

/**
 * 蓝海还款计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
@DataSource(DataSourceType.SLAVE)

public class LhRepayPlanServiceImpl implements ILhRepayPlanService 
{
    @Autowired
    private LhRepayPlanMapper lhRepayPlanMapper;

    /**
     * 查询蓝海还款计划
     * 
     * @param id 蓝海还款计划主键
     * @return 蓝海还款计划
     */
    @Override
    public LhRepayPlan selectLhRepayPlanById(String id)
    {
        return lhRepayPlanMapper.selectLhRepayPlanById(id);
    }

    /**
     * 查询蓝海还款计划列表
     * 
     * @param lhRepayPlan 蓝海还款计划
     * @return 蓝海还款计划
     */
    @Override
    public List<LhRepayPlan> selectLhRepayPlanList(LhRepayPlan lhRepayPlan)
    {
        return lhRepayPlanMapper.selectLhRepayPlanList(lhRepayPlan);
    }

    /**
     * 新增蓝海还款计划
     * 
     * @param lhRepayPlan 蓝海还款计划
     * @return 结果
     */
    @Override
    public int insertLhRepayPlan(LhRepayPlan lhRepayPlan)
    {
        return lhRepayPlanMapper.insertLhRepayPlan(lhRepayPlan);
    }

    /**
     * 修改蓝海还款计划
     * 
     * @param lhRepayPlan 蓝海还款计划
     * @return 结果
     */
    @Override
    public int updateLhRepayPlan(LhRepayPlan lhRepayPlan)
    {
        return lhRepayPlanMapper.updateLhRepayPlan(lhRepayPlan);
    }

    /**
     * 批量删除蓝海还款计划
     * 
     * @param ids 需要删除的蓝海还款计划主键
     * @return 结果
     */
    @Override
    public int deleteLhRepayPlanByIds(String[] ids)
    {
        return lhRepayPlanMapper.deleteLhRepayPlanByIds(ids);
    }

    /**
     * 删除蓝海还款计划信息
     * 
     * @param id 蓝海还款计划主键
     * @return 结果
     */
    @Override
    public int deleteLhRepayPlanById(String id)
    {
        return lhRepayPlanMapper.deleteLhRepayPlanById(id);
    }
}
