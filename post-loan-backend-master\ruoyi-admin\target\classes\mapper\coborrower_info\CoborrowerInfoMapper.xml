<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.coborrower_info.mapper.CoborrowerInfoMapper">
    
    <resultMap type="CoborrowerInfo" id="CoborrowerInfoResult">
        <result property="id"    column="id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="certType"    column="cert_type"    />
        <result property="certId"    column="cert_id"    />
        <result property="creditissueOrg"    column="creditIssue_org"    />
        <result property="address"    column="address"    />
        <result property="certBeginDate"    column="cert_begin_date"    />
        <result property="certEndDate"    column="cert_end_date"    />
        <result property="sex"    column="sex"    />
        <result property="age"    column="age"    />
        <result property="mobileNo"    column="mobile_no"    />
        <result property="principalRelation"    column="principal_relation"    />
        <result property="guaranteeType"    column="guarantee_type"    />
        <result property="infoType"    column="info_type"    />
        <result property="borrowerType"    column="borrower_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectCoborrowerInfoVo">
        select id, apply_no, customer_id, customer_name, cert_type, cert_id, creditIssue_org, address, cert_begin_date, cert_end_date, sex, age, mobile_no, principal_relation, guarantee_type, info_type, borrower_type, create_by, create_date, update_by, update_date, del_flag from coborrower_info
    </sql>

    <select id="selectCoborrowerInfoList" parameterType="CoborrowerInfo" resultMap="CoborrowerInfoResult">
        <include refid="selectCoborrowerInfoVo"/>
        <where>  
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="customerId != null  and customerId != ''"> and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="certType != null  and certType != ''"> and cert_type = #{certType}</if>
            <if test="certId != null  and certId != ''"> and cert_id = #{certId}</if>
            <if test="creditissueOrg != null  and creditissueOrg != ''"> and creditIssue_org = #{creditissueOrg}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="certBeginDate != null "> and cert_begin_date = #{certBeginDate}</if>
            <if test="certEndDate != null "> and cert_end_date = #{certEndDate}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="age != null "> and age = #{age}</if>
            <if test="mobileNo != null  and mobileNo != ''"> and mobile_no = #{mobileNo}</if>
            <if test="principalRelation != null  and principalRelation != ''"> and principal_relation = #{principalRelation}</if>
            <if test="guaranteeType != null  and guaranteeType != ''"> and guarantee_type = #{guaranteeType}</if>
            <if test="infoType != null  and infoType != ''"> and info_type = #{infoType}</if>
            <if test="borrowerType != null  and borrowerType != ''"> and borrower_type = #{borrowerType}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
    </select>
    
    <select id="selectCoborrowerInfoById" parameterType="String" resultMap="CoborrowerInfoResult">
        <include refid="selectCoborrowerInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertCoborrowerInfo" parameterType="CoborrowerInfo">
        insert into coborrower_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applyNo != null">apply_no,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="certType != null">cert_type,</if>
            <if test="certId != null">cert_id,</if>
            <if test="creditissueOrg != null">creditIssue_org,</if>
            <if test="address != null">address,</if>
            <if test="certBeginDate != null">cert_begin_date,</if>
            <if test="certEndDate != null">cert_end_date,</if>
            <if test="sex != null">sex,</if>
            <if test="age != null">age,</if>
            <if test="mobileNo != null">mobile_no,</if>
            <if test="principalRelation != null">principal_relation,</if>
            <if test="guaranteeType != null">guarantee_type,</if>
            <if test="infoType != null">info_type,</if>
            <if test="borrowerType != null">borrower_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="certType != null">#{certType},</if>
            <if test="certId != null">#{certId},</if>
            <if test="creditissueOrg != null">#{creditissueOrg},</if>
            <if test="address != null">#{address},</if>
            <if test="certBeginDate != null">#{certBeginDate},</if>
            <if test="certEndDate != null">#{certEndDate},</if>
            <if test="sex != null">#{sex},</if>
            <if test="age != null">#{age},</if>
            <if test="mobileNo != null">#{mobileNo},</if>
            <if test="principalRelation != null">#{principalRelation},</if>
            <if test="guaranteeType != null">#{guaranteeType},</if>
            <if test="infoType != null">#{infoType},</if>
            <if test="borrowerType != null">#{borrowerType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateCoborrowerInfo" parameterType="CoborrowerInfo">
        update coborrower_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null">apply_no = #{applyNo},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="certType != null">cert_type = #{certType},</if>
            <if test="certId != null">cert_id = #{certId},</if>
            <if test="creditissueOrg != null">creditIssue_org = #{creditissueOrg},</if>
            <if test="address != null">address = #{address},</if>
            <if test="certBeginDate != null">cert_begin_date = #{certBeginDate},</if>
            <if test="certEndDate != null">cert_end_date = #{certEndDate},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="age != null">age = #{age},</if>
            <if test="mobileNo != null">mobile_no = #{mobileNo},</if>
            <if test="principalRelation != null">principal_relation = #{principalRelation},</if>
            <if test="guaranteeType != null">guarantee_type = #{guaranteeType},</if>
            <if test="infoType != null">info_type = #{infoType},</if>
            <if test="borrowerType != null">borrower_type = #{borrowerType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCoborrowerInfoById" parameterType="String">
        delete from coborrower_info where id = #{id}
    </delete>

    <delete id="deleteCoborrowerInfoByIds" parameterType="String">
        delete from coborrower_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>