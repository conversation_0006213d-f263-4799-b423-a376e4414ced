package com.ruoyi.loan_reminder.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 催记对象 loan_reminder
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LoanReminder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**  */
    private String id;

    /** 流程id */
    @Excel(name = "流程id")
    private Long loanId;

    /** 工作人员 */
    @Excel(name = "工作人员")
    private String userId;

    /** 权限身份： */
    @Excel(name = "权限身份：")
    private String identity;

    /** 客户姓名 */
    @Excel(name = "客户姓名")
    private String customerName;

    /** 客户联系方式 */
    @Excel(name = "客户联系方式")
    private String customerMobile;

    /**
     * 车辆状态
     * 1、省内正常行驶
     * 2、省外正常行驶
     * 3、抵押
     * 4、疑似抵押
     * 5、疑似黑车
     * 6、已入库
     * 7、车在法院
     * 8、已法拍
     * 9、协商卖车
     */
    @Excel(name = "车辆状态1、省内正常行驶2、省外正常行驶3、抵押4、疑似抵押5、疑似黑车6、已入库7、车在法院8、已法拍9、协商卖车")
    private String carStatus;

    /** 还款类型 0-未还款 ，1-还款，2-部分还款 */
    @Excel(name = "还款类型  0-未还款 ，1-还款，2-部分还款")
    private Integer repaymentStatus;

    /** 审批：0-未审批，1-贷后通过，2-法诉通过，3-拒绝 */
    @Excel(name = "审批")
    private Integer examineStatus;

    /** 拒绝原因 */
    @Excel(name = "拒绝原因")
    private String examineReason;

    /** 银行还款金额 */
    @Excel(name = "银行还款金额")
    private BigDecimal bMoney;

    /** 代扣还款金额 */
    @Excel(name = "代扣还款金额")
    private BigDecimal dMoney;

    /** 银行还款凭据 */
    @Excel(name = "银行还款凭据")
    private String bRepaymentImg;

    /** 代扣还款凭据 */
    @Excel(name = "代扣还款凭据")
    private String dRepaymentImg;

    /** 还款银行账户 */
    @Excel(name = "还款银行账户")
    private String bAccount;

    /** 代扣银行账户 */
    @Excel(name = "代扣银行账户")
    private String dAccount;

    /** 催记类型 1-继续跟踪，2-约定还款，3-无法跟进 */
    @Excel(name = "催记类型 1-继续跟踪，2-约定还款，3-无法跟进")
    private Integer urgeStatus;

    /** 催记描述 */
    @Excel(name = "催记描述")
    private String urgeDescribe;

    /** 催回金额 */
    @Excel(name = "催回金额")
    private BigDecimal urgeMoney;

    /** 约定时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "约定时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date appointedTime;

    /** 下次跟踪时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "下次跟踪时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date trackingTime;

    /** 其他还款金额 */
    @Excel(name = "其他还款金额")
    private BigDecimal oMoney;

    /** 违约金还款金额 */
    @Excel(name = "违约金还款金额")
    private BigDecimal pMoney;

    /** 违约金还款凭据 */
    @Excel(name = "违约金还款凭据")
    private String pRepaymentImg;

    /** 违约金银行账户 */
    @Excel(name = "违约金银行账户")
    private String pAccount;

    /** 代偿还款金额 */
    @Excel(name = "代偿还款金额")
    private BigDecimal cMoney;

    /** 其他还款凭据 */
    @Excel(name = "其他还款凭据")
    private String oRepaymentImg;

    /** 代偿还款凭据 */
    @Excel(name = "代偿还款凭据")
    private String cRepaymentImg;

    /** 其他银行账户 */
    @Excel(name = "其他银行账户")
    private String oAccount;

    /** 代偿银行账户 */
    @Excel(name = "代偿银行账户")
    private String cAccount;

    /** 款项明细类型 */
    @Excel(name = "款项明细类型")
    private String fundsRepayment;

    /** 款项明细金额 */
    @Excel(name = "款项明细金额")
    private BigDecimal fundsAmount;

    /** 款项明细银行卡类型 */
    @Excel(name = "款项明细银行卡类型")
    private String fundsAccountType;

    /** 款项明细凭证图片 */
    @Excel(name = "款项明细凭证图片")
    private String fundsImage;

    /** 法诉id */
    @Excel(name = "法诉id")
    private Long litigationId;

    /** 如果为1是代偿催记 2是法诉日志 3贷后日志*/
    @Excel(name = "如果为1是代偿催记 2是法诉日志 3贷后日志")
    private String status;

    /** 贷款人 */
    @Excel(name = "贷款人")
    private String borrower;
    /** 出单渠道 */
    @Excel(name = "出单渠道")
    private String issuingChannel;
    /** 放款银行 */
    @Excel(name = "放款银行")
    private String lendingBank;

    /** account_loan表-放款银行 */
    private String alLendingBank;
    /** account_loan表-银行通脚金额 */
    private java.math.BigDecimal alBMoney;
    /** account_loan表-代扣通脚金额 */
    private java.math.BigDecimal alDMoney;

    /** 银行逾期金额 */
    @Excel(name = "银行逾期金额")
    @JsonProperty("bOverdueAmount")
    private BigDecimal bOverdueAmount;

    /** 代扣逾期金额 */
    @Excel(name = "代扣逾期金额")
    @JsonProperty("dOverdueAmount")
    private BigDecimal dOverdueAmount;

    // ========== 新增前端需要的字段 ==========

    /** 客户ID */
    @Excel(name = "客户ID")
    private String customerId;

    /** 申请ID */
    @Excel(name = "申请ID")
    private String applyId;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String certId;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String plateNo;

    /** 业务员 */
    @Excel(name = "业务员")
    private String salesman;

    /** 录单渠道名称 */
    @Excel(name = "录单渠道名称")
    private String jgName;

    /** 跟催员 */
    @Excel(name = "跟催员")
    private String followUp;

    /** 银行逾期金额 */
    @Excel(name = "银行逾期金额")
    private BigDecimal bankyqMoney;

    /** 代扣逾期金额 */
    @Excel(name = "代扣逾期金额")
    private BigDecimal dkyqMoney;

    // ========== 新增还款相关字段（只使用确实存在的字段） ==========

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String productName;

    /** 下期应还金额 */
    @Excel(name = "下期应还金额")
    private BigDecimal nextInstalmentAmt;

    /** 银行本期金额 */
    @Excel(name = "银行本期金额")
    private BigDecimal bNowMoney;

    /** 代扣本期金额 */
    @Excel(name = "代扣本期金额")
    private BigDecimal dNowMoney;

    /** 银行实还金额 */
    @Excel(name = "银行实还金额")
    private BigDecimal bRepaymentAmounts;

    /** 代扣实还金额 */
    @Excel(name = "代扣实还金额")
    private BigDecimal dRepaymentAmounts;

    /** 银行实还时间 */
    @Excel(name = "银行实还时间")
    private String bReturnTime;

    /** 代扣实还时间 */
    @Excel(name = "代扣实还时间")
    private String dReturnTime;

    // ========== 查询参数字段 ==========

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;

}
