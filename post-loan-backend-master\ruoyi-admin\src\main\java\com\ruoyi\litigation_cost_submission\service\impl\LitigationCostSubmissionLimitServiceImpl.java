package com.ruoyi.litigation_cost_submission.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.litigation_cost_submission.mapper.LitigationCostSubmissionLimitMapper;
import com.ruoyi.litigation_cost_submission.domain.LitigationCostSubmissionLimit;
import com.ruoyi.litigation_cost_submission.service.ILitigationCostSubmissionLimitService;

/**
 * 法诉费用提交限制记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class LitigationCostSubmissionLimitServiceImpl implements ILitigationCostSubmissionLimitService 
{
    @Autowired
    private LitigationCostSubmissionLimitMapper litigationCostSubmissionLimitMapper;

    /**
     * 查询法诉费用提交限制记录
     * 
     * @param id 法诉费用提交限制记录主键
     * @return 法诉费用提交限制记录
     */
    @Override
    public LitigationCostSubmissionLimit selectLitigationCostSubmissionLimitById(Long id)
    {
        return litigationCostSubmissionLimitMapper.selectLitigationCostSubmissionLimitById(id);
    }

    /**
     * 查询法诉费用提交限制记录列表
     * 
     * @param litigationCostSubmissionLimit 法诉费用提交限制记录
     * @return 法诉费用提交限制记录
     */
    @Override
    public List<LitigationCostSubmissionLimit> selectLitigationCostSubmissionLimitList(LitigationCostSubmissionLimit litigationCostSubmissionLimit)
    {
        return litigationCostSubmissionLimitMapper.selectLitigationCostSubmissionLimitList(litigationCostSubmissionLimit);
    }

    /**
     * 新增法诉费用提交限制记录
     * 
     * @param litigationCostSubmissionLimit 法诉费用提交限制记录
     * @return 结果
     */
    @Override
    public int insertLitigationCostSubmissionLimit(LitigationCostSubmissionLimit litigationCostSubmissionLimit)
    {
        litigationCostSubmissionLimit.setCreateTime(DateUtils.getNowDate());
        return litigationCostSubmissionLimitMapper.insertLitigationCostSubmissionLimit(litigationCostSubmissionLimit);
    }

    /**
     * 修改法诉费用提交限制记录
     * 
     * @param litigationCostSubmissionLimit 法诉费用提交限制记录
     * @return 结果
     */
    @Override
    public int updateLitigationCostSubmissionLimit(LitigationCostSubmissionLimit litigationCostSubmissionLimit)
    {
        litigationCostSubmissionLimit.setUpdateTime(DateUtils.getNowDate());
        return litigationCostSubmissionLimitMapper.updateLitigationCostSubmissionLimit(litigationCostSubmissionLimit);
    }

    /**
     * 批量删除法诉费用提交限制记录
     * 
     * @param ids 需要删除的法诉费用提交限制记录主键
     * @return 结果
     */
    @Override
    public int deleteLitigationCostSubmissionLimitByIds(Long[] ids)
    {
        return litigationCostSubmissionLimitMapper.deleteLitigationCostSubmissionLimitByIds(ids);
    }

    /**
     * 删除法诉费用提交限制记录信息
     * 
     * @param id 法诉费用提交限制记录主键
     * @return 结果
     */
    @Override
    public int deleteLitigationCostSubmissionLimitById(Long id)
    {
        return litigationCostSubmissionLimitMapper.deleteLitigationCostSubmissionLimitById(id);
    }
}
