package com.ruoyi.installment_application.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 分期申请对象 installment_application
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public class InstallmentApplication extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 分期期数 */
    @Excel(name = "分期期数")
    private Long periodCount;

    /** 每期账单金额 */
    @Excel(name = "每期账单金额")
    private BigDecimal billAmount;

    /** 每期还款日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @org.springframework.format.annotation.DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "每期还款日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date repayDay;

    /** 实际支付金额 */
    @Excel(name = "实际支付金额")
    private BigDecimal actualPaymentAmount;

    /** 账号类型 */
    @Excel(name = "账号类型")
    private String accountType;

    /** 贷款ID */
    @Excel(name = "贷款ID")
    private Long loanId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 关联审核中间表ID */
    private Long installmentApplicationId;

    /** 每月还款日（几号），由前端传入 */
    private Integer repayDayOfMonth;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setPeriodCount(Long periodCount) {
        this.periodCount = periodCount;
    }

    public Long getPeriodCount() {
        return periodCount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setRepayDay(Date repayDay) {
        this.repayDay = repayDay;
    }

    public Date getRepayDay() {
        return repayDay;
    }

    public void setActualPaymentAmount(BigDecimal actualPaymentAmount) {
        this.actualPaymentAmount = actualPaymentAmount;
    }

    public BigDecimal getActualPaymentAmount() {
        return actualPaymentAmount;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setLoanId(Long loanId) {
        this.loanId = loanId;
    }

    public Long getLoanId() {
        return loanId;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setInstallmentApplicationId(Long installmentApplicationId) {
        this.installmentApplicationId = installmentApplicationId;
    }

    public Long getInstallmentApplicationId() {
        return installmentApplicationId;
    }

    public void setRepayDayOfMonth(Integer repayDayOfMonth) {
        this.repayDayOfMonth = repayDayOfMonth;
    }

    public Integer getRepayDayOfMonth() {
        return repayDayOfMonth;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("periodCount", getPeriodCount())
                .append("billAmount", getBillAmount())
                .append("repayDay", getRepayDay())
                .append("actualPaymentAmount", getActualPaymentAmount())
                .append("accountType", getAccountType())
                .append("createDate", getCreateDate())
                .append("updateDate", getUpdateDate())
                .append("installmentApplicationId", getInstallmentApplicationId())
                .append("repayDayOfMonth", getRepayDayOfMonth())
                .append("createBy", getCreateBy())
                .append("updateBy", getUpdateBy())
                .toString();
    }
}
