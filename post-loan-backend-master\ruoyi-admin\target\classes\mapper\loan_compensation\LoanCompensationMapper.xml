<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.loan_compensation.mapper.LoanCompensationMapper">
    
    <resultMap type="LoanCompensation" id="LoanCompensationResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="loanId"    column="loan_id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="salesman"    column="salesman"    />
        <result property="orgName"    column="org_name"    />
        <result property="partnerId"    column="partner_id"    />
        <result property="bank"    column="bank"    />
        <result property="loanAmount"    column="loan_amount"    />
        <result property="otherDebt"    column="other_debt"    />
        <result property="totalMoney"    column="total_money"    />
        <result property="examineStatus"    column="examine_status"    />
        <result property="reason"    column="reason"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="fxjProportion"    column="fxj_proportion"    />
        <result property="qdProportion"    column="qd_proportion"    />
        <result property="gmjProportion"    column="gmj_proportion"    />
        <result property="kjjProportion"    column="kjj_proportion"    />
        <result property="kjczProportion"    column="kjcz_proportion"    />
        <result property="sbczProportion"    column="sbcz_proportion"    />
        <result property="fxjMoney"    column="fxj_money"    />
        <result property="qdMoney"    column="qd_money"    />
        <result property="gmjMoney"    column="gmj_money"    />
        <result property="kjjMoney"    column="kjj_money"    />
        <result property="kjczMoney"    column="kjcz_money"    />
        <result property="sbczMoney"    column="sbcz_money"    />
        <result property="fxjAccount"    column="fxj_account"    />
        <result property="qdAccount"    column="qd_account"    />
        <result property="gmjAccount"    column="gmj_account"    />
        <result property="kjjAccount"    column="kjj_account"    />
        <result property="kjczAccount"    column="kjcz_account"    />
        <result property="sbczAccount"    column="sbcz_account"    />
        <result property="image"    column="image"    />
        <result property="payouts"    column="payouts"    />
        <result property="payoutsTime"    column="payouts_time"    />
        <result property="approveTime"    column="approve_time"    />
    </resultMap>

    <sql id="selectLoanCompensationVo">
        select * from loan_compensation
    </sql>

    <select id="selectLoanCompensationList" parameterType="LoanCompensation" resultMap="LoanCompensationResult">
        <include refid="selectLoanCompensationVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="loanId != null "> and loan_id = #{loanId}</if>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="salesman != null  and salesman != ''"> and salesman = #{salesman}</if>
            <if test="orgName != null  and orgName != ''"> and org_name like concat('%', #{orgName}, '%')</if>
            <if test="partnerId != null  and partnerId != ''"> and partner_id = #{partnerId}</if>
            <if test="bank != null  and bank != ''"> and bank = #{bank}</if>
            <if test="loanAmount != null "> and loan_amount = #{loanAmount}</if>
            <if test="otherDebt != null "> and other_debt = #{otherDebt}</if>
            <if test="totalMoney != null "> and total_money = #{totalMoney}</if>
            <if test="examineStatus != null "> and examine_status = #{examineStatus}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="fxjProportion != null "> and fxj_proportion = #{fxjProportion}</if>
            <if test="qdProportion != null "> and qd_proportion = #{qdProportion}</if>
            <if test="gmjProportion != null "> and gmj_proportion = #{gmjProportion}</if>
            <if test="kjjProportion != null "> and kjj_proportion = #{kjjProportion}</if>
            <if test="kjczProportion != null "> and kjcz_proportion = #{kjczProportion}</if>
            <if test="sbczProportion != null "> and sbcz_proportion = #{sbczProportion}</if>
            <if test="fxjMoney != null "> and fxj_money = #{fxjMoney}</if>
            <if test="qdMoney != null "> and qd_money = #{qdMoney}</if>
            <if test="gmjMoney != null "> and gmj_money = #{gmjMoney}</if>
            <if test="kjjMoney != null "> and kjj_money = #{kjjMoney}</if>
            <if test="kjczMoney != null "> and kjcz_money = #{kjczMoney}</if>
            <if test="sbczMoney != null "> and sbcz_money = #{sbczMoney}</if>
            <if test="fxjAccount != null  and fxjAccount != ''"> and fxj_account = #{fxjAccount}</if>
            <if test="qdAccount != null  and qdAccount != ''"> and qd_account = #{qdAccount}</if>
            <if test="gmjAccount != null  and gmjAccount != ''"> and gmj_account = #{gmjAccount}</if>
            <if test="kjjAccount != null  and kjjAccount != ''"> and kjj_account = #{kjjAccount}</if>
            <if test="kjczAccount != null  and kjczAccount != ''"> and kjcz_account = #{kjczAccount}</if>
            <if test="sbczAccount != null  and sbczAccount != ''"> and sbcz_account = #{sbczAccount}</if>
            <if test="image != null  and image != ''"> and image = #{image}</if>
            <if test="payouts != null "> and payouts = #{payouts}</if>
            <if test="payoutsTime != null "> and payouts_time = #{payoutsTime}</if>
            <if test="approveTime != null "> and approve_time = #{approveTime}</if>
        </where>
    </select>
    
    <select id="selectLoanCompensationById" parameterType="String" resultMap="LoanCompensationResult">
        <include refid="selectLoanCompensationVo"/>
        where id = #{id}
    </select>

    <select id="selectLoanCompensationByIds" parameterType="String" resultMap="LoanCompensationResult">
        <include refid="selectLoanCompensationVo"/>
        where loan_id = #{id}
    </select>

    <insert id="insertLoanCompensation" parameterType="LoanCompensation" useGeneratedKeys="true" keyProperty="id">
        insert into loan_compensation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyId != null">apply_id,</if>
            <if test="loanId != null">loan_id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="salesman != null">salesman,</if>
            <if test="orgName != null">org_name,</if>
            <if test="partnerId != null">partner_id,</if>
            <if test="bank != null">bank,</if>
            <if test="loanAmount != null">loan_amount,</if>
            <if test="otherDebt != null">other_debt,</if>
            <if test="totalMoney != null">total_money,</if>
            <if test="examineStatus != null">examine_status,</if>
            <if test="reason != null">reason,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="fxjProportion != null">fxj_proportion,</if>
            <if test="qdProportion != null">qd_proportion,</if>
            <if test="gmjProportion != null">gmj_proportion,</if>
            <if test="kjjProportion != null">kjj_proportion,</if>
            <if test="kjczProportion != null">kjcz_proportion,</if>
            <if test="sbczProportion != null">sbcz_proportion,</if>
            <if test="fxjMoney != null">fxj_money,</if>
            <if test="qdMoney != null">qd_money,</if>
            <if test="gmjMoney != null">gmj_money,</if>
            <if test="kjjMoney != null">kjj_money,</if>
            <if test="kjczMoney != null">kjcz_money,</if>
            <if test="sbczMoney != null">sbcz_money,</if>
            <if test="fxjAccount != null">fxj_account,</if>
            <if test="qdAccount != null">qd_account,</if>
            <if test="gmjAccount != null">gmj_account,</if>
            <if test="kjjAccount != null">kjj_account,</if>
            <if test="kjczAccount != null">kjcz_account,</if>
            <if test="sbczAccount != null">sbcz_account,</if>
            <if test="image != null">image,</if>
            <if test="payouts != null">payouts,</if>
            <if test="payoutsTime != null">payouts_time,</if>
            <if test="approveTime != null">approve_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyId != null">#{applyId},</if>
            <if test="loanId != null">#{loanId},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="salesman != null">#{salesman},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="partnerId != null">#{partnerId},</if>
            <if test="bank != null">#{bank},</if>
            <if test="loanAmount != null">#{loanAmount},</if>
            <if test="otherDebt != null">#{otherDebt},</if>
            <if test="totalMoney != null">#{totalMoney},</if>
            <if test="examineStatus != null">#{examineStatus},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="fxjProportion != null">#{fxjProportion},</if>
            <if test="qdProportion != null">#{qdProportion},</if>
            <if test="gmjProportion != null">#{gmjProportion},</if>
            <if test="kjjProportion != null">#{kjjProportion},</if>
            <if test="kjczProportion != null">#{kjczProportion},</if>
            <if test="sbczProportion != null">#{sbczProportion},</if>
            <if test="fxjMoney != null">#{fxjMoney},</if>
            <if test="qdMoney != null">#{qdMoney},</if>
            <if test="gmjMoney != null">#{gmjMoney},</if>
            <if test="kjjMoney != null">#{kjjMoney},</if>
            <if test="kjczMoney != null">#{kjczMoney},</if>
            <if test="sbczMoney != null">#{sbczMoney},</if>
            <if test="fxjAccount != null">#{fxjAccount},</if>
            <if test="qdAccount != null">#{qdAccount},</if>
            <if test="gmjAccount != null">#{gmjAccount},</if>
            <if test="kjjAccount != null">#{kjjAccount},</if>
            <if test="kjczAccount != null">#{kjczAccount},</if>
            <if test="sbczAccount != null">#{sbczAccount},</if>
            <if test="image != null">#{image},</if>
            <if test="payouts != null">#{payouts},</if>
            <if test="payoutsTime != null">#{payoutsTime},</if>
            <if test="approveTime != null">#{approveTime},</if>
         </trim>
    </insert>

    <update id="updateLoanCompensation" parameterType="LoanCompensation">
        update loan_compensation
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyId != null">apply_id = #{applyId},</if>
            <if test="loanId != null">loan_id = #{loanId},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="salesman != null">salesman = #{salesman},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="partnerId != null">partner_id = #{partnerId},</if>
            <if test="bank != null">bank = #{bank},</if>
            <if test="loanAmount != null">loan_amount = #{loanAmount},</if>
            <if test="otherDebt != null">other_debt = #{otherDebt},</if>
            <if test="totalMoney != null">total_money = #{totalMoney},</if>
            <if test="examineStatus != null">examine_status = #{examineStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="fxjProportion != null">fxj_proportion = #{fxjProportion},</if>
            <if test="qdProportion != null">qd_proportion = #{qdProportion},</if>
            <if test="gmjProportion != null">gmj_proportion = #{gmjProportion},</if>
            <if test="kjjProportion != null">kjj_proportion = #{kjjProportion},</if>
            <if test="kjczProportion != null">kjcz_proportion = #{kjczProportion},</if>
            <if test="sbczProportion != null">sbcz_proportion = #{sbczProportion},</if>
            <if test="fxjMoney != null">fxj_money = #{fxjMoney},</if>
            <if test="qdMoney != null">qd_money = #{qdMoney},</if>
            <if test="gmjMoney != null">gmj_money = #{gmjMoney},</if>
            <if test="kjjMoney != null">kjj_money = #{kjjMoney},</if>
            <if test="kjczMoney != null">kjcz_money = #{kjczMoney},</if>
            <if test="sbczMoney != null">sbcz_money = #{sbczMoney},</if>
            <if test="fxjAccount != null">fxj_account = #{fxjAccount},</if>
            <if test="qdAccount != null">qd_account = #{qdAccount},</if>
            <if test="gmjAccount != null">gmj_account = #{gmjAccount},</if>
            <if test="kjjAccount != null">kjj_account = #{kjjAccount},</if>
            <if test="kjczAccount != null">kjcz_account = #{kjczAccount},</if>
            <if test="sbczAccount != null">sbcz_account = #{sbczAccount},</if>
            <if test="image != null">image = #{image},</if>
            <if test="payouts != null">payouts = #{payouts},</if>
            <if test="payoutsTime != null">payouts_time = #{payoutsTime},</if>
            <if test="approveTime != null">approve_time = #{approveTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLoanCompensationById" parameterType="String">
        delete from loan_compensation where id = #{id}
    </delete>

    <delete id="deleteLoanCompensationByIds" parameterType="String">
        delete from loan_compensation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>