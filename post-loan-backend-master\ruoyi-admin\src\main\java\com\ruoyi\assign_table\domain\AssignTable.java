package com.ruoyi.assign_table.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 指派对象 assign_table
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
public class AssignTable extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 被指派人 */
    @Excel(name = "被指派人")
    private String assignUser;

    /** 指派时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "指派时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date assignTime;

    /** 指派状态（assigned/auto_revoked/manual_revoked） */
    @Excel(name = "指派状态", readConverterExp = "a=ssigned/auto_revoked/manual_revoked")
    private String status;

    /** 撤销时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "撤销时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date revokeTime;

    /** 流程id */
    @Excel(name = "流程id")
    private Long loanId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setAssignUser(String assignUser) 
    {
        this.assignUser = assignUser;
    }

    public String getAssignUser() 
    {
        return assignUser;
    }

    public void setAssignTime(Date assignTime) 
    {
        this.assignTime = assignTime;
    }

    public Date getAssignTime() 
    {
        return assignTime;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setRevokeTime(Date revokeTime) 
    {
        this.revokeTime = revokeTime;
    }

    public Date getRevokeTime() 
    {
        return revokeTime;
    }

    public void setLoanId(Long loanId) 
    {
        this.loanId = loanId;
    }

    public Long getLoanId() 
    {
        return loanId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("assignUser", getAssignUser())
            .append("assignTime", getAssignTime())
            .append("status", getStatus())
            .append("revokeTime", getRevokeTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("loanId", getLoanId())
            .toString();
    }
}
