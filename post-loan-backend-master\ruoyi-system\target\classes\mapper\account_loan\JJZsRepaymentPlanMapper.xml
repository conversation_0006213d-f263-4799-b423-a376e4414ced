<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.JJZsRepaymentPlanMapper">
    
    <resultMap type="JJZsRepaymentPlan" id="ZsRepaymentPlanResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="period"    column="period"    />
        <result property="repayDate"    column="repay_date"    />
        <result property="actualRepayDate"    column="actual_repay_date"    />
        <result property="repayAmount"    column="repay_amount"    />
        <result property="capital"    column="capital"    />
        <result property="actualCapital"    column="actual_capital"    />
        <result property="interest"    column="interest"    />
        <result property="actualInterest"    column="actual_interest"    />
        <result property="defInterest"    column="def_interest"    />
        <result property="startBalance"    column="start_balance"    />
        <result property="endBalance"    column="end_balance"    />
        <result property="status"    column="status"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
    </resultMap>

    <sql id="selectZsRepaymentPlanVo">
        select * from zs_repayment_plan
    </sql>

    <select id="selectZsRepaymentPlanList" parameterType="JJZsRepaymentPlan" resultMap="ZsRepaymentPlanResult">
        <include refid="selectZsRepaymentPlanVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="period != null  and period != ''"> and period = #{period}</if>
            <if test="repayDate != null  and repayDate != ''"> and repay_date = #{repayDate}</if>
            <if test="actualRepayDate != null  and actualRepayDate != ''"> and actual_repay_date = #{actualRepayDate}</if>
            <if test="repayAmount != null  and repayAmount != ''"> and repay_amount = #{repayAmount}</if>
            <if test="capital != null  and capital != ''"> and capital = #{capital}</if>
            <if test="actualCapital != null  and actualCapital != ''"> and actual_capital = #{actualCapital}</if>
            <if test="interest != null  and interest != ''"> and interest = #{interest}</if>
            <if test="actualInterest != null  and actualInterest != ''"> and actual_interest = #{actualInterest}</if>
            <if test="defInterest != null  and defInterest != ''"> and def_interest = #{defInterest}</if>
            <if test="startBalance != null  and startBalance != ''"> and start_balance = #{startBalance}</if>
            <if test="endBalance != null  and endBalance != ''"> and end_balance = #{endBalance}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
    </select>

    <select id="selectZsRepaymentPlanListGroup" parameterType="JJZsRepaymentPlan" resultMap="ZsRepaymentPlanResult">
        <include refid="selectZsRepaymentPlanVo"/>
        <where>
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="period != null  and period != ''"> and period = #{period}</if>
            <if test="repayDate != null  and repayDate != ''"> and repay_date = #{repayDate}</if>
            <if test="actualRepayDate != null  and actualRepayDate != ''"> and actual_repay_date = #{actualRepayDate}</if>
            <if test="repayAmount != null  and repayAmount != ''"> and repay_amount = #{repayAmount}</if>
            <if test="capital != null  and capital != ''"> and capital = #{capital}</if>
            <if test="actualCapital != null  and actualCapital != ''"> and actual_capital = #{actualCapital}</if>
            <if test="interest != null  and interest != ''"> and interest = #{interest}</if>
            <if test="actualInterest != null  and actualInterest != ''"> and actual_interest = #{actualInterest}</if>
            <if test="defInterest != null  and defInterest != ''"> and def_interest = #{defInterest}</if>
            <if test="startBalance != null  and startBalance != ''"> and start_balance = #{startBalance}</if>
            <if test="endBalance != null  and endBalance != ''"> and end_balance = #{endBalance}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
        group by apply_id
    </select>


    <select id="selectZsRepayNow" parameterType="String" resultMap="ZsRepaymentPlanResult">
        select * from zs_repayment_plan where apply_id = #{applyId} and  YEAR(repay_date) = YEAR(CURDATE())  and MONTH(repay_date) = MONTH(CURDATE()) limit 1
    </select>
</mapper>