package com.ruoyi.coborrower_info.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.coborrower_info.mapper.CoborrowerInfoMapper;
import com.ruoyi.coborrower_info.domain.CoborrowerInfo;
import com.ruoyi.coborrower_info.service.ICoborrowerInfoService;

/**
 * 担保人/共借人Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class CoborrowerInfoServiceImpl implements ICoborrowerInfoService 
{
    @Autowired
    private CoborrowerInfoMapper coborrowerInfoMapper;

    /**
     * 查询担保人/共借人
     * 
     * @param id 担保人/共借人主键
     * @return 担保人/共借人
     */
    @Override
    public CoborrowerInfo selectCoborrowerInfoById(String id)
    {
        return coborrowerInfoMapper.selectCoborrowerInfoById(id);
    }

    /**
     * 查询担保人/共借人列表
     * 
     * @param coborrowerInfo 担保人/共借人
     * @return 担保人/共借人
     */
    @Override
    public List<CoborrowerInfo> selectCoborrowerInfoList(CoborrowerInfo coborrowerInfo)
    {
        return coborrowerInfoMapper.selectCoborrowerInfoList(coborrowerInfo);
    }

    /**
     * 新增担保人/共借人
     * 
     * @param coborrowerInfo 担保人/共借人
     * @return 结果
     */
    @Override
    public int insertCoborrowerInfo(CoborrowerInfo coborrowerInfo)
    {
        return coborrowerInfoMapper.insertCoborrowerInfo(coborrowerInfo);
    }

    /**
     * 修改担保人/共借人
     * 
     * @param coborrowerInfo 担保人/共借人
     * @return 结果
     */
    @Override
    public int updateCoborrowerInfo(CoborrowerInfo coborrowerInfo)
    {
        return coborrowerInfoMapper.updateCoborrowerInfo(coborrowerInfo);
    }

    /**
     * 批量删除担保人/共借人
     * 
     * @param ids 需要删除的担保人/共借人主键
     * @return 结果
     */
    @Override
    public int deleteCoborrowerInfoByIds(String[] ids)
    {
        return coborrowerInfoMapper.deleteCoborrowerInfoByIds(ids);
    }

    /**
     * 删除担保人/共借人信息
     * 
     * @param id 担保人/共借人主键
     * @return 结果
     */
    @Override
    public int deleteCoborrowerInfoById(String id)
    {
        return coborrowerInfoMapper.deleteCoborrowerInfoById(id);
    }
}
