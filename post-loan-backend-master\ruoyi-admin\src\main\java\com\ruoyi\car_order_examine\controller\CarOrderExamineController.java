package com.ruoyi.car_order_examine.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.car_order_examine.domain.CarOrderExamine;
import com.ruoyi.car_order_examine.service.ICarOrderExamineService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.Date;

/**
 * 找车费用审批Controller
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/car_order_examine/car_order_examine")
public class CarOrderExamineController extends BaseController
{
    @Autowired
    private ICarOrderExamineService carOrderExamineService;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 查询找车费用审批列表
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarOrderExamine carOrderExamine)
    {
        startPage();
        List<CarOrderExamine> list = carOrderExamineService.selectCarOrderExamineList(carOrderExamine);
        return getDataTable(list);
    }

    /**
     * 查询待审批列表
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:list')")
    @GetMapping("/list/pending")
    public TableDataInfo listPending()
    {
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        startPage();
        List<CarOrderExamine> list = carOrderExamineService.selectPendingApprovalList(userRole);
        return getDataTable(list);
    }

    /**
     * 导出找车费用审批列表
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:export')")
    @Log(title = "找车费用审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarOrderExamine carOrderExamine)
    {
        List<CarOrderExamine> list = carOrderExamineService.selectCarOrderExamineList(carOrderExamine);
        ExcelUtil<CarOrderExamine> util = new ExcelUtil<CarOrderExamine>(CarOrderExamine.class);
        util.exportExcel(response, list, "找车费用审批数据");
    }

    /**
     * 获取找车费用审批详细信息
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(carOrderExamineService.selectCarOrderExamineById(id));
    }

    /**
     * 新增找车费用审批
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:add')")
    @Log(title = "找车费用审批", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CarOrderExamine carOrderExamine)
    {
        return toAjax(carOrderExamineService.insertCarOrderExamine(carOrderExamine));
    }

    /**
     * 修改找车费用审批
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:edit')")
    @Log(title = "找车费用审批", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CarOrderExamine carOrderExamine)
    {
        return toAjax(carOrderExamineService.updateCarOrderExamine(carOrderExamine));
    }

    /**
     * 删除找车费用审批
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:remove')")
    @Log(title = "找车费用审批", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(carOrderExamineService.deleteCarOrderExamineByIds(ids));
    }

    /**
     * 审批找车费用申请
     */
    @PreAuthorize("@ss.hasPermi('car_order_examine:car_order_examine:approve')")
    @Log(title = "找车费用审批", businessType = BusinessType.UPDATE)
    @PutMapping("/approve")
    public AjaxResult approve(@RequestBody CarOrderExamine carOrderExamine)
    {
        if (carOrderExamine.getId() == null || carOrderExamine.getId().isEmpty()) {
            return error("审批ID不能为空");
        }

        // 获取当前用户角色
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        // 获取原始审批信息
        CarOrderExamine existingExamine = carOrderExamineService.selectCarOrderExamineById(carOrderExamine.getId());
        if (existingExamine == null) {
            return error("找不到对应的审批信息");
        }

        // 检查是否可以审批
        if (!existingExamine.canApprove(userRole)) {
            return error("您没有权限进行当前阶段的审批，当前状态为【" + existingExamine.getStatusDescription() + "】");
        }

        // 检查审批结果
        Integer newStatus = carOrderExamine.getStatus();
        if (newStatus == null) {
            return error("请选择审批结果");
        }

        // 如果是拒绝，设置为拒绝状态
        if (newStatus == CarOrderExamine.STATUS_REJECTED) {
            if (carOrderExamine.getRejectReason() == null || carOrderExamine.getRejectReason().trim().isEmpty()) {
                return error("拒绝时必须提供拒绝原因");
            }
            existingExamine.setStatus(CarOrderExamine.STATUS_REJECTED);
            existingExamine.setRejectReason(carOrderExamine.getRejectReason());
        } else {
            // 通过审批，进入下一个状态
            Integer nextStatus = existingExamine.getNextApprovalStatus();
            existingExamine.setStatus(nextStatus);
            existingExamine.setRejectReason(null);
        }

        // 更新审批信息
        existingExamine.setExamineTime(new Date());
        existingExamine.setCurrentApprover(currentUser);
        existingExamine.setUpdateBy(currentUser);
        existingExamine.setUpdateDate(new Date());

        // 记录审批历史
        String approvalRecord = String.format("[%s] %s %s - %s",
            new Date(), userRole, currentUser,
            existingExamine.getStatus() == CarOrderExamine.STATUS_REJECTED ?
                "拒绝：" + existingExamine.getRejectReason() : "通过");

        String currentHistory = existingExamine.getApprovalHistory();
        if (currentHistory == null || currentHistory.trim().isEmpty()) {
            existingExamine.setApprovalHistory(approvalRecord);
        } else {
            existingExamine.setApprovalHistory(currentHistory + "\n" + approvalRecord);
        }

        return toAjax(carOrderExamineService.updateCarOrderExamine(existingExamine));
    }
}
