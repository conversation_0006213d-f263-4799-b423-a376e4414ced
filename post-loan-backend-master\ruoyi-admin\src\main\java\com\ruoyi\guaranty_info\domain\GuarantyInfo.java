package com.ruoyi.guaranty_info.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 抵押登记对象 guaranty_info
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public class GuarantyInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyNo;

    /** 抵押方式 */
    @Excel(name = "抵押方式")
    private String guarantyWay;

    /** 抵押日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "抵押日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date guarantyDate;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date createDate;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date updateDate;

    /** $column.columnComment */
    private String delFlag;

    /** 快递单号(皖新) */
    @Excel(name = "快递单号(皖新)")
    private String expressNumber;

    /** 寄出日期(皖新) */
    @Excel(name = "寄出日期(皖新)")
    private String expressDate;

    /** 快递公司名称(皖新) */
    @Excel(name = "快递公司名称(皖新)")
    private String expressCompany;

    /** 结果(皖新) */
    @Excel(name = "结果(皖新)")
    private String result;

    /** 解押代理人ID(蓝海) */
    @Excel(name = "解押代理人ID(蓝海)")
    private String releaseAgentId;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setApplyNo(String applyNo) 
    {
        this.applyNo = applyNo;
    }

    public String getApplyNo() 
    {
        return applyNo;
    }

    public void setGuarantyWay(String guarantyWay) 
    {
        this.guarantyWay = guarantyWay;
    }

    public String getGuarantyWay() 
    {
        return guarantyWay;
    }

    public void setGuarantyDate(Date guarantyDate) 
    {
        this.guarantyDate = guarantyDate;
    }

    public Date getGuarantyDate() 
    {
        return guarantyDate;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setExpressNumber(String expressNumber) 
    {
        this.expressNumber = expressNumber;
    }

    public String getExpressNumber() 
    {
        return expressNumber;
    }

    public void setExpressDate(String expressDate) 
    {
        this.expressDate = expressDate;
    }

    public String getExpressDate() 
    {
        return expressDate;
    }

    public void setExpressCompany(String expressCompany) 
    {
        this.expressCompany = expressCompany;
    }

    public String getExpressCompany() 
    {
        return expressCompany;
    }

    public void setResult(String result) 
    {
        this.result = result;
    }

    public String getResult() 
    {
        return result;
    }

    public void setReleaseAgentId(String releaseAgentId) 
    {
        this.releaseAgentId = releaseAgentId;
    }

    public String getReleaseAgentId() 
    {
        return releaseAgentId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("applyNo", getApplyNo())
            .append("guarantyWay", getGuarantyWay())
            .append("guarantyDate", getGuarantyDate())
            .append("createBy", getCreateBy())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .append("delFlag", getDelFlag())
            .append("expressNumber", getExpressNumber())
            .append("expressDate", getExpressDate())
            .append("expressCompany", getExpressCompany())
            .append("result", getResult())
            .append("releaseAgentId", getReleaseAgentId())
            .toString();
    }
}
