package com.ruoyi.loan_reminder.controller;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.account_loan.service.IAccountLoanService;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.loan_reminder.domain.LoanReminder;
import com.ruoyi.loan_reminder.service.ILoanReminderService;
import com.ruoyi.system.service.impl.SysUserServiceImpl;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 催记Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Slf4j
@RestController
@RequestMapping("/loan_reminder/loan_reminder")
public class LoanReminderController extends BaseController {
    @Autowired
    private ILoanReminderService loanReminderService;

    @Autowired
    private SysUserServiceImpl sysUserService;

    @Autowired
    private IAccountLoanService accountLoanService;

    /**
     * 查询催记列表
     */
    @PreAuthorize("@ss.hasPermi('loan_reminder:loan_reminder:list')")
    @GetMapping("/list")
    public TableDataInfo list(LoanReminder loanReminder) {
        startPage();
        List<LoanReminder> list = loanReminderService.selectLoanReminderList(loanReminder);
        return getDataTable(list);
    }

    /**
     * 查询催记审批列表
     */
    @PreAuthorize("@ss.hasPermi('loan_reminder:loan_reminder:list')")
    @GetMapping("/list/approve")
    public TableDataInfo listApprove(LoanReminder loanReminder) {
        String userRole = sysUserService.selectUserRoleGroup(getUsername());
        if (userRole.equals("贷后文员")) {
            loanReminder.setIdentity("出单员工,电催员工,上访员工,强制上访会员,找车员");
        } else if (userRole.equals("贷后主管")) {
            loanReminder.setIdentity("贷后文员");
        } else if (userRole.equals("法诉主管")) {
            loanReminder.setIdentity("法诉文员");
        }
        startPage();

        List<LoanReminder> list = loanReminderService.selectLoanReminderList(loanReminder);
        return getDataTable(list);
    }

    /**
     * 查询催记详情
     */
    @PreAuthorize("@ss.hasPermi('loan_reminder:loan_reminder:list')")
    @GetMapping("/detail")
    public AjaxResult detail(LoanReminder loanReminder) {
        startPage();
        LoanReminder list = loanReminderService.selectLoanReminderListDetail(loanReminder);
        return success(list);
    }

    /**
     * 导出催记列表
     */
    @PreAuthorize("@ss.hasPermi('loan_reminder:loan_reminder:export')")
    @Log(title = "催记", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LoanReminder loanReminder) {
        List<LoanReminder> list = loanReminderService.selectLoanReminderList(loanReminder);
        ExcelUtil<LoanReminder> util = new ExcelUtil<LoanReminder>(LoanReminder.class);
        util.exportExcel(response, list, "催记数据");
    }

    /**
     * 获取催记详细信息
     */
    @PreAuthorize("@ss.hasPermi('loan_reminder:loan_reminder:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(loanReminderService.selectLoanReminderById(id));
    }

    /**
     * 新增催记
     */
    @PreAuthorize("@ss.hasPermi('loan_reminder:loan_reminder:add')")
    @Log(title = "催记", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LoanReminder loanReminder) {
        log.info("新增催记: {}", loanReminder);
        String userRole = sysUserService.selectUserRoleGroup(getUsername());
        loanReminder.setIdentity(userRole);
        loanReminder.setCreateBy(getUsername());
        loanReminder.setCreateTime(new java.util.Date());
        return toAjax(loanReminderService.insertLoanReminder(loanReminder));
    }

    /**
     * 修改催记
     */
    @PreAuthorize("@ss.hasPermi('loan_reminder:loan_reminder:edit')")
    @Log(title = "催记", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LoanReminder loanReminder) {
        return toAjax(loanReminderService.updateLoanReminder(loanReminder));
    }

    /**
     * 删除催记
     */
    @PreAuthorize("@ss.hasPermi('loan_reminder:loan_reminder:remove')")
    @Log(title = "催记", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(loanReminderService.deleteLoanReminderByIds(ids));
    }

    /**
     * 审核催记
     */
    @PreAuthorize("@ss.hasPermi('loan_reminder:loan_reminder:approve')")
    @Log(title = "催记审核", businessType = BusinessType.UPDATE)
    @PutMapping("/approve")
    public AjaxResult approve(@RequestBody LoanReminder loanReminder) {
        if (loanReminder.getId() == null || loanReminder.getId().isEmpty()) {
            return error("催记ID不能为空");
        }
        if (loanReminder.getExamineStatus() == null) {
            return error("审核状态不能为空");
        }
        // 如果是拒绝状态，必须提供拒绝原因
        if (loanReminder.getExamineStatus() == 2 &&
                (loanReminder.getExamineReason() == null || loanReminder.getExamineReason().isEmpty())) {
            return error("拒绝时必须提供拒绝原因");
        }

        // 先获取催记信息，以便获取贷款ID
        LoanReminder existingReminder = loanReminderService.selectLoanReminderById(loanReminder.getId());
        log.info("existingReminder: {}", existingReminder);
        if (existingReminder == null) {
            return error("找不到对应的催记信息");
        }

        // 如果是通过审核
        if (loanReminder.getExamineStatus() == 1) {
            log.info("贷款ID: {}", existingReminder.getLoanId());

            // 判断是否为代偿催记（状态为1）
            if (existingReminder.getStatus() != null && existingReminder.getStatus().equals("1")) {
                // 是代偿催记，检查account_loan表中的还款状态
                Integer repaymentStatus = loanReminderService
                        .getAccountLoanRepaymentStatus(existingReminder.getLoanId());
                if (repaymentStatus != null && (repaymentStatus == 6 || repaymentStatus == 7 || repaymentStatus == 8)) {
                    // 还款状态为6、7或8，将还款状态更新为9
                    log.info("代偿催记审核通过，account_loan中还款状态为{}，更新还款状态为9", repaymentStatus);
                    int updateResult = loanReminderService.updateAccountLoanRepaymentStatus(
                            existingReminder.getLoanId(), 9);

                    if (updateResult <= 0) {
                        log.error("更新代偿催记的还款状态失败");
                        return error("更新代偿催记的还款状态失败");
                    }
                } else {
                    log.info("代偿催记审核通过，但account_loan中还款状态为{}，不满足更新还款状态的条件", repaymentStatus);
                }
            } else {
                // 非代偿催记，使用原有的状态检查和更新方法（只允许从6-逾期未还款 更新为 7-逾期还款中）
                Integer[] allowedStatuses = { 6 }; // 只允许从状态6更新
                log.info("非代偿催记，检查并更新还款状态");
                int updateResult = accountLoanService.checkAndUpdateRepaymentStatus(
                        existingReminder.getLoanId(), 7, allowedStatuses);

                if (updateResult == -1) {
                    return error("无法更新贷后还款状态");
                } else if (updateResult == 0) {
                    return error("更新贷后还款状态失败");
                }
            }
        }

        loanReminder.setUpdateBy(getUsername());
        return toAjax(loanReminderService.approveLoanReminder(loanReminder));
    }

    /**
     * 获取产品列表
     */
    @GetMapping("/products")
    public AjaxResult getProductList() {
        List<java.util.Map<String, Object>> productList = loanReminderService.getProductList();
        return success(productList);
    }
}
