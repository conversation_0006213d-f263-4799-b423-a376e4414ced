<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.loan_extension.mapper.LoanExtensionMapper">
    
    <resultMap type="LoanExtension" id="LoanExtensionResult">
        <result property="id"    column="id"    />
        <result property="loanId"    column="loan_id"    />
        <result property="extensionDate"    column="extension_date"    />
        <result property="status"    column="status"    />
        <result property="reason"    column="reason"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectLoanExtensionVo">
        select id, loan_id, extension_date, status, reason, create_by, create_time, update_by, update_time from loan_extension
    </sql>

    <select id="selectLoanExtensionList" parameterType="LoanExtension" resultMap="LoanExtensionResult">
        <include refid="selectLoanExtensionVo"/>
        <where>  
            <if test="loanId != null "> and loan_id = #{loanId}</if>
            <if test="extensionDate != null "> and extension_date = #{extensionDate}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
        </where>
    </select>
    
    <select id="selectLoanExtensionById" parameterType="Integer" resultMap="LoanExtensionResult">
        <include refid="selectLoanExtensionVo"/>
        where id = #{id}
    </select>

    <insert id="insertLoanExtension" parameterType="LoanExtension" useGeneratedKeys="true" keyProperty="id">
        insert into loan_extension
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="loanId != null">loan_id,</if>
            <if test="extensionDate != null">extension_date,</if>
            <if test="status != null">status,</if>
            <if test="reason != null">reason,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="loanId != null">#{loanId},</if>
            <if test="extensionDate != null">#{extensionDate},</if>
            <if test="status != null">#{status},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateLoanExtension" parameterType="LoanExtension">
        update loan_extension
        <trim prefix="SET" suffixOverrides=",">
            <if test="loanId != null">loan_id = #{loanId},</if>
            <if test="extensionDate != null">extension_date = #{extensionDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLoanExtensionById" parameterType="Integer">
        delete from loan_extension where id = #{id}
    </delete>

    <delete id="deleteLoanExtensionByIds" parameterType="String">
        delete from loan_extension where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>