package com.ruoyi.litigation_cost.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.litigation_cost.mapper.LitigationCostMapper;
import com.ruoyi.litigation_cost.domain.LitigationCost;
import com.ruoyi.litigation_cost.service.ILitigationCostService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.common.utils.SecurityUtils;
import java.util.Date;
import java.math.BigDecimal;
import com.ruoyi.litigation_cost_submission.domain.LitigationCostSubmissionLimit;
import com.ruoyi.litigation_cost_submission.service.ILitigationCostSubmissionLimitService;

/**
 * 法诉费用明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
@Service
public class LitigationCostServiceImpl implements ILitigationCostService 
{
    @Autowired
    private LitigationCostMapper litigationCostMapper;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ILitigationCostSubmissionLimitService submissionLimitService;

    /**
     * 查询法诉费用明细
     * 
     * @param id 法诉费用明细主键
     * @return 法诉费用明细
     */
    @Override
    public LitigationCost selectLitigationCostById(Long id)
    {
        return litigationCostMapper.selectLitigationCostById(id);
    }

    /**
     * 查询法诉费用明细列表
     * 
     * @param litigationCost 法诉费用明细
     * @return 法诉费用明细
     */
    @Override
    public List<LitigationCost> selectLitigationCostList(LitigationCost litigationCost)
    {
        return litigationCostMapper.selectLitigationCostList(litigationCost);
    }

    /**
     * 新增法诉费用明细
     *
     * @param litigationCost 法诉费用明细
     * @return 结果
     */
    @Override
    public int insertLitigationCost(LitigationCost litigationCost)
    {
        // 验证限制性费用类型（判决金额和利息只能提交一次）
        List<String> submittedTypes = getSubmittedLimitedFeeTypes(litigationCost.getLitigationCaseId());

        if (litigationCost.getJudgmentAmount() != null && litigationCost.getJudgmentAmount().compareTo(BigDecimal.ZERO) > 0) {
            if (submittedTypes.contains("judgmentAmount")) {
                throw new RuntimeException("判决金额已经提交过，每个案件只能提交一次");
            }
        }

        if (litigationCost.getInterest() != null && litigationCost.getInterest().compareTo(BigDecimal.ZERO) > 0) {
            if (submittedTypes.contains("interest")) {
                throw new RuntimeException("利息已经提交过，每个案件只能提交一次");
            }
        }

        String username = SecurityUtils.getUsername();
        String roleName = sysUserService.selectUserRoleGroup(username);
        litigationCost.setApproveBy(username);
        litigationCost.setApproveRole(roleName);
        litigationCost.setApplicationTime(new Date());
        litigationCost.setApplicationBy(username);

        int result = litigationCostMapper.insertLitigationCost(litigationCost);

        // 记录限制性费用的提交状态
        if (result > 0) {
            recordLimitedFeeSubmission(litigationCost, username);
        }

        return result;
    }

    /**
     * 修改法诉费用明细
     * 
     * @param litigationCost 法诉费用明细
     * @return 结果
     */
    @Override
    public int updateLitigationCost(LitigationCost litigationCost)
    {
        return litigationCostMapper.updateLitigationCost(litigationCost);
    }

    /**
     * 批量删除法诉费用明细
     * 
     * @param ids 需要删除的法诉费用明细主键
     * @return 结果
     */
    @Override
    public int deleteLitigationCostByIds(Long[] ids)
    {
        return litigationCostMapper.deleteLitigationCostByIds(ids);
    }

    /**
     * 删除法诉费用明细信息
     *
     * @param id 法诉费用明细主键
     * @return 结果
     */
    @Override
    public int deleteLitigationCostById(Long id)
    {
        return litigationCostMapper.deleteLitigationCostById(id);
    }

    /**
     * 获取已提交的限制性费用类型（判决金额和利息）
     *
     * @param litigationCaseId 法诉案件ID
     * @return 已提交的限制性费用类型列表
     */
    @Override
    public List<String> getSubmittedLimitedFeeTypes(Long litigationCaseId)
    {
        List<String> submittedTypes = new ArrayList<>();

        // 优先从提交限制记录表查询
        LitigationCostSubmissionLimit queryParam = new LitigationCostSubmissionLimit();
        queryParam.setLitigationCaseId(litigationCaseId);
        queryParam.setIsSubmitted("1");
        List<LitigationCostSubmissionLimit> limitList = submissionLimitService.selectLitigationCostSubmissionLimitList(queryParam);

        for (LitigationCostSubmissionLimit limit : limitList) {
            submittedTypes.add(limit.getCostType());
        }

        // 如果提交限制记录表没有数据，则从费用记录表查询（兼容旧数据）
        if (submittedTypes.isEmpty()) {
            LitigationCost costQueryParam = new LitigationCost();
            costQueryParam.setLitigationCaseId(litigationCaseId);
            List<LitigationCost> costList = litigationCostMapper.selectLitigationCostList(costQueryParam);

            // 检查是否已提交判决金额和利息
            for (LitigationCost cost : costList) {
                if (cost.getJudgmentAmount() != null && cost.getJudgmentAmount().compareTo(BigDecimal.ZERO) > 0) {
                    if (!submittedTypes.contains("judgmentAmount")) {
                        submittedTypes.add("judgmentAmount");
                    }
                }
                if (cost.getInterest() != null && cost.getInterest().compareTo(BigDecimal.ZERO) > 0) {
                    if (!submittedTypes.contains("interest")) {
                        submittedTypes.add("interest");
                    }
                }
            }
        }

        return submittedTypes;
    }

    /**
     * 记录限制性费用的提交状态
     *
     * @param litigationCost 法诉费用对象
     * @param username 提交人
     */
    private void recordLimitedFeeSubmission(LitigationCost litigationCost, String username) {
        // 如果提交了判决金额，记录提交状态
        if (litigationCost.getJudgmentAmount() != null && litigationCost.getJudgmentAmount().compareTo(BigDecimal.ZERO) > 0) {
            LitigationCostSubmissionLimit limit = new LitigationCostSubmissionLimit();
            limit.setLitigationCaseId(litigationCost.getLitigationCaseId());
            limit.setCostType("judgmentAmount");
            limit.setIsSubmitted("1");
            limit.setSubmissionTime(new Date());
            limit.setSubmittedBy(username);
            submissionLimitService.insertLitigationCostSubmissionLimit(limit);
        }

        // 如果提交了利息，记录提交状态
        if (litigationCost.getInterest() != null && litigationCost.getInterest().compareTo(BigDecimal.ZERO) > 0) {
            LitigationCostSubmissionLimit limit = new LitigationCostSubmissionLimit();
            limit.setLitigationCaseId(litigationCost.getLitigationCaseId());
            limit.setCostType("interest");
            limit.setIsSubmitted("1");
            limit.setSubmissionTime(new Date());
            limit.setSubmittedBy(username);
            submissionLimitService.insertLitigationCostSubmissionLimit(limit);
        }
    }

    /**
     * 根据案件ID列表获取费用汇总数据
     *
     * @param caseIds 案件ID列表
     * @return 费用汇总数据，key为案件ID，value为费用汇总信息
     */
    @Override
    public Map<Long, Map<String, Object>> getCostSummaryByCaseIds(List<Long> caseIds) {
        Map<Long, Map<String, Object>> result = new HashMap<>();

        if (caseIds == null || caseIds.isEmpty()) {
            return result;
        }

        // 查询所有相关的法诉费用数据
        for (Long caseId : caseIds) {
            try {
                LitigationCost queryParam = new LitigationCost();
                queryParam.setLitigationCaseId(caseId);
                List<LitigationCost> costs = litigationCostMapper.selectLitigationCostList(queryParam);

            Map<String, Object> summary = new HashMap<>();

            // 初始化所有费用类型为0
            summary.put("judgmentAmount", BigDecimal.ZERO);
            summary.put("interest", BigDecimal.ZERO);
            summary.put("lawyerFee", BigDecimal.ZERO);
            summary.put("litigationFee", BigDecimal.ZERO);
            summary.put("preservationFee", BigDecimal.ZERO);
            summary.put("surveillanceFee", BigDecimal.ZERO);
            summary.put("announcementFee", BigDecimal.ZERO);
            summary.put("appraisalFee", BigDecimal.ZERO);
            summary.put("executionFee", BigDecimal.ZERO);
            summary.put("penalty", BigDecimal.ZERO);
            summary.put("guaranteeFee", BigDecimal.ZERO);
            summary.put("intermediaryFee", BigDecimal.ZERO);
            summary.put("compensity", BigDecimal.ZERO);
            summary.put("otherAmountsOwed", BigDecimal.ZERO);
            summary.put("insurance", BigDecimal.ZERO);

            // 汇总各种费用
            for (LitigationCost cost : costs) {
                if (cost.getJudgmentAmount() != null) {
                    summary.put("judgmentAmount", ((BigDecimal) summary.get("judgmentAmount")).add(cost.getJudgmentAmount()));
                }
                if (cost.getInterest() != null) {
                    summary.put("interest", ((BigDecimal) summary.get("interest")).add(cost.getInterest()));
                }
                if (cost.getLawyerFee() != null) {
                    summary.put("lawyerFee", ((BigDecimal) summary.get("lawyerFee")).add(cost.getLawyerFee()));
                }
                if (cost.getLitigationFee() != null) {
                    summary.put("litigationFee", ((BigDecimal) summary.get("litigationFee")).add(cost.getLitigationFee()));
                }
                if (cost.getPreservationFee() != null) {
                    summary.put("preservationFee", ((BigDecimal) summary.get("preservationFee")).add(cost.getPreservationFee()));
                }
                if (cost.getSurveillanceFee() != null) {
                    summary.put("surveillanceFee", ((BigDecimal) summary.get("surveillanceFee")).add(cost.getSurveillanceFee()));
                }
                if (cost.getAnnouncementFee() != null) {
                    summary.put("announcementFee", ((BigDecimal) summary.get("announcementFee")).add(cost.getAnnouncementFee()));
                }
                if (cost.getAppraisalFee() != null) {
                    summary.put("appraisalFee", ((BigDecimal) summary.get("appraisalFee")).add(cost.getAppraisalFee()));
                }
                if (cost.getExecutionFee() != null) {
                    summary.put("executionFee", ((BigDecimal) summary.get("executionFee")).add(cost.getExecutionFee()));
                }
                if (cost.getPenalty() != null) {
                    summary.put("penalty", ((BigDecimal) summary.get("penalty")).add(cost.getPenalty()));
                }
                if (cost.getGuaranteeFee() != null) {
                    summary.put("guaranteeFee", ((BigDecimal) summary.get("guaranteeFee")).add(cost.getGuaranteeFee()));
                }
                if (cost.getIntermediaryFee() != null) {
                    summary.put("intermediaryFee", ((BigDecimal) summary.get("intermediaryFee")).add(cost.getIntermediaryFee()));
                }
                if (cost.getCompensity() != null) {
                    summary.put("compensity", ((BigDecimal) summary.get("compensity")).add(cost.getCompensity()));
                }
                if (cost.getOtherAmountsOwed() != null) {
                    summary.put("otherAmountsOwed", ((BigDecimal) summary.get("otherAmountsOwed")).add(cost.getOtherAmountsOwed()));
                }
                if (cost.getInsurance() != null) {
                    summary.put("insurance", ((BigDecimal) summary.get("insurance")).add(cost.getInsurance()));
                }
            }

            result.put(caseId, summary);
            } catch (Exception e) {
                // 如果某个案件查询失败，记录错误但继续处理其他案件
                System.err.println("查询案件ID " + caseId + " 的费用数据失败: " + e.getMessage());
                // 为失败的案件创建空的汇总数据
                Map<String, Object> emptySummary = new HashMap<>();
                emptySummary.put("judgmentAmount", BigDecimal.ZERO);
                emptySummary.put("interest", BigDecimal.ZERO);
                emptySummary.put("lawyerFee", BigDecimal.ZERO);
                emptySummary.put("litigationFee", BigDecimal.ZERO);
                emptySummary.put("preservationFee", BigDecimal.ZERO);
                emptySummary.put("surveillanceFee", BigDecimal.ZERO);
                emptySummary.put("announcementFee", BigDecimal.ZERO);
                emptySummary.put("appraisalFee", BigDecimal.ZERO);
                emptySummary.put("executionFee", BigDecimal.ZERO);
                emptySummary.put("penalty", BigDecimal.ZERO);
                emptySummary.put("guaranteeFee", BigDecimal.ZERO);
                emptySummary.put("intermediaryFee", BigDecimal.ZERO);
                emptySummary.put("compensity", BigDecimal.ZERO);
                emptySummary.put("otherAmountsOwed", BigDecimal.ZERO);
                emptySummary.put("insurance", BigDecimal.ZERO);
                result.put(caseId, emptySummary);
            }
        }

        return result;
    }
}
