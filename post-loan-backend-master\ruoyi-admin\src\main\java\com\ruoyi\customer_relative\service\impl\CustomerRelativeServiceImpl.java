package com.ruoyi.customer_relative.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.customer_relative.mapper.CustomerRelativeMapper;
import com.ruoyi.customer_relative.domain.CustomerRelative;
import com.ruoyi.customer_relative.service.ICustomerRelativeService;

/**
 * 客户联系人信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class CustomerRelativeServiceImpl implements ICustomerRelativeService 
{
    @Autowired
    private CustomerRelativeMapper customerRelativeMapper;

    /**
     * 查询客户联系人信息
     * 
     * @param id 客户联系人信息主键
     * @return 客户联系人信息
     */
    @Override
    public CustomerRelative selectCustomerRelativeById(String id)
    {
        return customerRelativeMapper.selectCustomerRelativeById(id);
    }

    /**
     * 查询客户联系人信息列表
     * 
     * @param customerRelative 客户联系人信息
     * @return 客户联系人信息
     */
    @Override
    public List<CustomerRelative> selectCustomerRelativeList(CustomerRelative customerRelative)
    {
        return customerRelativeMapper.selectCustomerRelativeList(customerRelative);
    }

    /**
     * 新增客户联系人信息
     * 
     * @param customerRelative 客户联系人信息
     * @return 结果
     */
    @Override
    public int insertCustomerRelative(CustomerRelative customerRelative)
    {
        return customerRelativeMapper.insertCustomerRelative(customerRelative);
    }

    /**
     * 修改客户联系人信息
     * 
     * @param customerRelative 客户联系人信息
     * @return 结果
     */
    @Override
    public int updateCustomerRelative(CustomerRelative customerRelative)
    {
        return customerRelativeMapper.updateCustomerRelative(customerRelative);
    }

    /**
     * 批量删除客户联系人信息
     * 
     * @param ids 需要删除的客户联系人信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerRelativeByIds(String[] ids)
    {
        return customerRelativeMapper.deleteCustomerRelativeByIds(ids);
    }

    /**
     * 删除客户联系人信息信息
     * 
     * @param id 客户联系人信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerRelativeById(String id)
    {
        return customerRelativeMapper.deleteCustomerRelativeById(id);
    }
}
