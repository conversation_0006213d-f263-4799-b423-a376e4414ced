package com.ruoyi.ind_work.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.ind_work.domain.IndWork;
import com.ruoyi.ind_work.service.IIndWorkService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 个人客户工作信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@RestController
@RequestMapping("/ind_work/ind_work")
public class IndWorkController extends BaseController
{
    @Autowired
    private IIndWorkService indWorkService;

    /**
     * 查询个人客户工作信息列表
     */
    @PreAuthorize("@ss.hasPermi('ind_work:ind_work:list')")
    @GetMapping("/list")
    public TableDataInfo list(IndWork indWork)
    {
        startPage();
        List<IndWork> list = indWorkService.selectIndWorkList(indWork);
        return getDataTable(list);
    }

    /**
     * 导出个人客户工作信息列表
     */
    @PreAuthorize("@ss.hasPermi('ind_work:ind_work:export')")
    @Log(title = "个人客户工作信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IndWork indWork)
    {
        List<IndWork> list = indWorkService.selectIndWorkList(indWork);
        ExcelUtil<IndWork> util = new ExcelUtil<IndWork>(IndWork.class);
        util.exportExcel(response, list, "个人客户工作信息数据");
    }

    /**
     * 获取个人客户工作信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('ind_work:ind_work:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(indWorkService.selectIndWorkById(id));
    }

    /**
     * 新增个人客户工作信息
     */
    @PreAuthorize("@ss.hasPermi('ind_work:ind_work:add')")
    @Log(title = "个人客户工作信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IndWork indWork)
    {
        return toAjax(indWorkService.insertIndWork(indWork));
    }

    /**
     * 修改个人客户工作信息
     */
    @PreAuthorize("@ss.hasPermi('ind_work:ind_work:edit')")
    @Log(title = "个人客户工作信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IndWork indWork)
    {
        return toAjax(indWorkService.updateIndWork(indWork));
    }

    /**
     * 删除个人客户工作信息
     */
    @PreAuthorize("@ss.hasPermi('ind_work:ind_work:remove')")
    @Log(title = "个人客户工作信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(indWorkService.deleteIndWorkByIds(ids));
    }
}
