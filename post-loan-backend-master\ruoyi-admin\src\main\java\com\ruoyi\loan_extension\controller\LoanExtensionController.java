package com.ruoyi.loan_extension.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.loan_extension.domain.LoanExtension;
import com.ruoyi.loan_extension.service.ILoanExtensionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.loan_list.service.ILoanListService;
import com.ruoyi.loan_list.domain.LoanList;
import static com.ruoyi.common.utils.DateUtils.getNowDate;

/**
 * 流程延期申请Controller
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/loan_extension/loan_extension")
public class LoanExtensionController extends BaseController {
    @Autowired
    private ILoanExtensionService loanExtensionService;

    @Autowired
    private ILoanListService loanListService;

    /**
     * 查询流程延期申请列表
     */
    @PreAuthorize("@ss.hasPermi('loan_extension:loan_extension:list')")
    @GetMapping("/list")
    public TableDataInfo list(LoanExtension loanExtension) {
        startPage();
        List<LoanExtension> list = loanExtensionService.selectLoanExtensionList(loanExtension);
        return getDataTable(list);
    }

    /**
     * 导出流程延期申请列表
     */
    @PreAuthorize("@ss.hasPermi('loan_extension:loan_extension:export')")
    @Log(title = "流程延期申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LoanExtension loanExtension) {
        List<LoanExtension> list = loanExtensionService.selectLoanExtensionList(loanExtension);
        ExcelUtil<LoanExtension> util = new ExcelUtil<LoanExtension>(LoanExtension.class);
        util.exportExcel(response, list, "流程延期申请数据");
    }

    /**
     * 获取流程延期申请详细信息
     */
//    @PreAuthorize("@ss.hasPermi('loan_extension:loan_extension:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Integer id) {
//        return success(loanExtensionService.selectLoanExtensionById(id));
//    }

    /**
     * 根据loan_id获取流程延期申请详细信息（状态必须为申请中）
     */

    @PreAuthorize("@ss.hasPermi('loan_extension:loan_extension:query')")
    @GetMapping("/extension_detail")
    public AjaxResult getInfoByLoanId(@RequestParam("loan_id") Long loanId) {
        // 创建查询条件对象
        LoanExtension queryParam = new LoanExtension();
        queryParam.setLoanId(loanId);
        queryParam.setStatus(1); // 状态为申请中

        // 查询符合条件的列表（理论上只会有一条）
        List<LoanExtension> list = loanExtensionService.selectLoanExtensionList(queryParam);

        // 返回第一条记录或null
        return success(list.isEmpty() ? null : list.get(0));
    }

    /**
     * 新增流程延期申请
     */
    @PreAuthorize("@ss.hasPermi('loan_extension:loan_extension:add')")
    @Log(title = "流程延期申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LoanExtension loanExtension) {
        return toAjax(loanExtensionService.insertLoanExtension(loanExtension));
    }

    /**
     * 修改流程延期申请
     */
    @PreAuthorize("@ss.hasPermi('loan_extension:loan_extension:edit')")
    @Log(title = "流程延期申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LoanExtension loanExtension) {
        return toAjax(loanExtensionService.updateLoanExtension(loanExtension));
    }

    /**
     * 删除流程延期申请
     */
    @PreAuthorize("@ss.hasPermi('loan_extension:loan_extension:remove')")
    @Log(title = "流程延期申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(loanExtensionService.deleteLoanExtensionByIds(ids));
    }

    /**
     * 流程延期申请审核结果提交
     */
    @PreAuthorize("@ss.hasPermi('loan_extension:loan_extension:approve')")
    @Log(title = "流程延期申请审核结果提交", businessType = BusinessType.UPDATE)
    @PutMapping("/approve")
    public AjaxResult approve(@RequestBody LoanExtension loanExtension) {
        // 更新延期申请状态和原因
        loanExtension.setUpdateBy(getUsername());
        loanExtension.setUpdateTime(getNowDate());
        int rows = loanExtensionService.updateLoanExtension(loanExtension);
        if (rows > 0) {
            // 更新loan_list表的延期状态
            LoanList loanList = new LoanList();
            loanList.setId(loanExtension.getLoanId());
            loanList.setIsExtension(String.valueOf(loanExtension.getStatus()));
            if (loanExtension.getStatus() == 2){
                loanList.setExtensionDate(loanExtension.getExtensionDate());
            }
            loanListService.updateLoanList(loanList);
            return success();
        }
        return error("审核失败");
    }
}
