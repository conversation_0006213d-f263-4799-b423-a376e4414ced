package com.ruoyi.loan_settle.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.trial_balance.domain.TrialBalance;
import com.ruoyi.trial_balance.service.ITrialBalanceService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.loadtime.Aj;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.loan_settle.domain.LoanSettle;
import com.ruoyi.loan_settle.service.ILoanSettleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.account_loan.service.IAccountLoanService;
import com.ruoyi.common.utils.SecurityUtils;

import static com.ruoyi.common.utils.DateUtils.getNowDate;

import java.util.Map;

/**
 * 流程结清Controller
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@Slf4j
@RestController
@RequestMapping("/loan_settle/loan_settle")
public class LoanSettleController extends BaseController {
    @Autowired
    private ILoanSettleService loanSettleService;

    @Autowired
    private ITrialBalanceService trialBalanceService;

    @Autowired
    private IAccountLoanService accountLoanService;

    /**
     * 查询流程结清列表
     */
    @PreAuthorize("@ss.hasPermi('loan_settle:loan_settle:list')")
    @GetMapping("/list")
    // @Anonymous
    public TableDataInfo list(LoanSettle loanSettle) {
        startPage();
//        Long roleId = SecurityUtils.getLoginUser().getRoleId();
//        if (roleId != 103L) {
//            loanSettle.setStatus(1);
//        }
        List<LoanSettle> list = loanSettleService.selectLoanSettleList(loanSettle);
        return getDataTable(list);
    }

    /**
     * 查询流程结清详情
     */
    @PreAuthorize("@ss.hasPermi('loan_settle:loan_settle:list')")
    @GetMapping("/detail")
    // @Anonymous
    public AjaxResult detail(LoanSettle loanSettle) {
        startPage();
        LoanSettle list = loanSettleService.selectLoanSettleListDetail(loanSettle);
        if (list != null) {
            list.setTrialBalance(trialBalanceService.selectTrialBalanceByIdjq(list.getId()));
        }
        return success(list);
    }

    /**
     * 导出流程结清列表
     */
    @PreAuthorize("@ss.hasPermi('loan_settle:loan_settle:export')")
    @Log(title = "流程结清", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LoanSettle loanSettle) {
        List<LoanSettle> list = loanSettleService.selectLoanSettleList(loanSettle);
        ExcelUtil<LoanSettle> util = new ExcelUtil<LoanSettle>(LoanSettle.class);
        util.exportExcel(response, list, "流程结清数据");
    }

    /**
     * 获取流程结清详细信息
     */
    @PreAuthorize("@ss.hasPermi('loan_settle:loan_settle:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        LoanSettle settle = loanSettleService.selectLoanSettleById(id);
        settle.setTrialBalance(trialBalanceService.selectTrialBalanceByIdjq(settle.getId()));
        return success(settle);
    }

    /**
     * 新增流程结清
     */
    @PreAuthorize("@ss.hasPermi('loan_settle:loan_settle:add')")
    @Log(title = "流程结清", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LoanSettle loanSettle) {
        loanSettle.setCreateBy(getUsername());
        loanSettle.setCreateDate(getNowDate());
        loanSettleService.insertLoanSettle(loanSettle);
        return success(loanSettle);
    }

    /**
     * 修改流程结清
     */
    @PreAuthorize("@ss.hasPermi('loan_settle:loan_settle:edit')")
    @Log(title = "流程结清", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LoanSettle loanSettle) {
        return toAjax(loanSettleService.updateLoanSettle(loanSettle));
    }

    /**
     * 删除流程结清
     */
    @PreAuthorize("@ss.hasPermi('loan_settle:loan_settle:remove')")
    @Log(title = "流程结清", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(loanSettleService.deleteLoanSettleByIds(ids));
    }

    /**
     * 修改流程结清
     */
    // @PreAuthorize("@ss.hasPermi('loan_settle:loan_settle:examine')")
    @Log(title = "流程结清审核", businessType = BusinessType.UPDATE)
    @PutMapping("/examine")
    @Anonymous
    public AjaxResult examine(@RequestBody LoanSettle loanSettle) {
        return toAjax(loanSettleService.updateLoanSettle(loanSettle));
    }

    /**
     * 代偿结清处理（支持全额/减免，按status区分，支持body传参）
     */
    @PreAuthorize("@ss.hasPermi('loan_settle:loan_settle:process')")
    @Log(title = "代偿结清处理", businessType = BusinessType.INSERT)
    @PostMapping("/process")
    public AjaxResult processSettlement(@RequestBody Map<String, Object> param) {
        Long loanId = Long.valueOf(param.get("loanId").toString());
        Integer status = Integer.valueOf(param.get("status").toString());
        // 查询该贷款下所有未被拒绝的结清流程
        List<LoanSettle> processingSettles = loanSettleService.selectProcessingSettlesByLoanId(loanId);
        if (processingSettles != null && !processingSettles.isEmpty()) {
            for (LoanSettle settle : processingSettles) {
                if (!settle.getStatus().equals(status)) {
                    // 存在不同类型的结清，报错
                    return error("该贷款已存在另一类型的结清流程，不能同时存在全额结清和减免结清");
                } else {
                    // 存在相同类型的结清，直接返回
                    return AjaxResult.success("已存在该类型结清流程", settle);
                }
            }
        }
        // 没有未被拒绝的结清流程，允许新建
        return loanSettleService.processCompensationSettlement(loanId, status);
    }

    /**
     * 审核代偿结清
     */
    @PreAuthorize("@ss.hasPermi('loan_settle:loan_settle:approve')")
    @Log(title = "代偿结清审核", businessType = BusinessType.UPDATE)
    @PutMapping("/approve")
    public AjaxResult approveCompensationSettlement(@RequestBody LoanSettle loanSettle) {
        if (loanSettle.getId() == null || loanSettle.getId().isEmpty()) {
            return error("结清ID不能为空");
        }
        if (loanSettle.getExamineStatus() == null
                || (loanSettle.getExamineStatus() != 3 && loanSettle.getExamineStatus() != 4)) {
            return error("审核状态必须为同意或拒绝");
        }

        // 如果是拒绝，需要校验拒绝理由
        if (loanSettle.getExamineStatus() == 4
                && (loanSettle.getReason() == null || loanSettle.getReason().isEmpty())) {
            return error("拒绝时必须提供拒绝理由");
        }

        // 获取原始结清信息
        LoanSettle existingSettle = loanSettleService.selectLoanSettleById(loanSettle.getId());
        if (existingSettle == null) {
            return error("找不到对应的结清信息");
        }

        // 如果是拒绝，直接更新状态
        if (loanSettle.getExamineStatus() == 4) {
            log.info("拒绝代偿结清申请，loanId={}", existingSettle.getLoanId());
            loanSettle.setUpdateBy(getUsername());
            loanSettle.setUpdateDate(getNowDate());
            return toAjax(loanSettleService.updateLoanSettle(loanSettle));
        }

        // 以下是同意审核的处理逻辑
        // 检查是否为代偿
        if (existingSettle.getLoanStatus() == null || existingSettle.getLoanStatus() != 1) {
            log.info("非代偿结清，不执行更新repayment_status操作，loanId={}", existingSettle.getLoanId());
            loanSettle.setUpdateBy(getUsername());
            loanSettle.setUpdateDate(getNowDate());
            return toAjax(loanSettleService.updateLoanSettle(loanSettle));
        }

        // 获取account_loan中的还款状态
        Integer repaymentStatus = accountLoanService.getRepaymentStatusByLoanId(existingSettle.getLoanId());
        if (repaymentStatus == null) {
            return error("获取还款状态失败");
        }

        // 检查是否为允许的状态(8或9)
        if (repaymentStatus != 8 && repaymentStatus != 9) {
            log.info("当前还款状态为{}，不满足审核条件(必须是8或9)", repaymentStatus);
            return error("当前还款状态不满足审核条件");
        }

        // 根据结清类型更新不同的状态
        int updateResult;
        if (existingSettle.getStatus() == 1) {
            // 全额结清 -> 状态改为11
            log.info("全额结清，更新还款状态为11，loanId={}", existingSettle.getLoanId());
            updateResult = accountLoanService.updateRepaymentStatus(existingSettle.getLoanId(), 11);
        } else if (existingSettle.getStatus() == 2) {
            // 减免结清 -> 状态改为10
            log.info("减免结清，更新还款状态为10，loanId={}", existingSettle.getLoanId());
            updateResult = accountLoanService.updateRepaymentStatus(existingSettle.getLoanId(), 10);
        } else {
            return error("无效的结清类型");
        }

        if (updateResult <= 0) {
            log.error("更新还款状态失败，loanId={}", existingSettle.getLoanId());
            return error("更新还款状态失败");
        }

        // 更新结清审核状态
        loanSettle.setUpdateBy(getUsername());
        loanSettle.setUpdateDate(getNowDate());
        return toAjax(loanSettleService.updateLoanSettle(loanSettle));
    }
}
