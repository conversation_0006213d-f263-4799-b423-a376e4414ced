package com.ruoyi.installment_application.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.installment_application.domain.InstallmentApplication;
import com.ruoyi.installment_application.service.IInstallmentApplicationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.installment_application_audit.service.IInstallmentApplicationAuditService;
import com.ruoyi.installment_application_audit.domain.InstallmentApplicationAudit;

/**
 * 分期申请Controller
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/installment_application/installment_application")
public class InstallmentApplicationController extends BaseController {
    @Autowired
    private IInstallmentApplicationService installmentApplicationService;

    @Autowired
    private com.ruoyi.installment_application_audit.service.IInstallmentApplicationAuditService installmentApplicationAuditService;

    /**
     * 查询分期申请列表
     */
    @PreAuthorize("@ss.hasPermi('installment_application:installment_application:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstallmentApplication installmentApplication) {
        startPage();
        List<InstallmentApplication> list = installmentApplicationService
                .selectInstallmentApplicationList(installmentApplication);
        return getDataTable(list);
    }

    /**
     * 导出分期申请列表
     */
    @PreAuthorize("@ss.hasPermi('installment_application:installment_application:export')")
    @Log(title = "分期申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstallmentApplication installmentApplication) {
        List<InstallmentApplication> list = installmentApplicationService
                .selectInstallmentApplicationList(installmentApplication);
        ExcelUtil<InstallmentApplication> util = new ExcelUtil<InstallmentApplication>(InstallmentApplication.class);
        util.exportExcel(response, list, "分期申请数据");
    }

    /**
     * 获取分期申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('installment_application:installment_application:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(installmentApplicationService.selectInstallmentApplicationById(id));
    }

    /**
     * 新增分期申请
     */
    @PreAuthorize("@ss.hasPermi('installment_application:installment_application:add')")
    @Log(title = "分期申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstallmentApplication installmentApplication) {
        // 1. 通过ID查审核中间表
        Long auditId = installmentApplication.getInstallmentApplicationId();
        com.ruoyi.installment_application_audit.domain.InstallmentApplicationAudit audit = installmentApplicationAuditService
                .selectInstallmentApplicationAuditById(auditId);
        if (audit == null) {
            return AjaxResult.error("未找到对应的审核记录");
        }
        // 2. 用审核表的tailAmount和tailPayTime
        java.math.BigDecimal tailAmount = audit.getTailAmount();
        java.util.Date tailPayTime = audit.getTailPayTime();
        // 3. 其它批量分期逻辑同前
        Integer repayDayOfMonth = installmentApplication.getRepayDayOfMonth();
        if (repayDayOfMonth == null) {
            repayDayOfMonth = 1;
        }
        java.util.Date now = new java.util.Date();
        Long periodCount = installmentApplication.getPeriodCount();
        int loopCount = periodCount != null ? periodCount.intValue() : 0;
        boolean hasTail = tailAmount != null && tailAmount.compareTo(java.math.BigDecimal.ZERO) > 0;
        if (hasTail) {
            loopCount += 1;
        }
        int total = 0;
        for (int i = 1; i <= loopCount; i++) {
            InstallmentApplication item = new InstallmentApplication();
            item.setPeriodCount((long) i);
            item.setActualPaymentAmount(installmentApplication.getActualPaymentAmount());
            item.setAccountType(installmentApplication.getAccountType());
            item.setLoanId(installmentApplication.getLoanId());
            item.setCreateDate(installmentApplication.getCreateDate());
            item.setUpdateDate(installmentApplication.getUpdateDate());
            item.setCreateBy(getUsername());
            item.setInstallmentApplicationId(auditId);
            // 账单金额
            if (hasTail && i == loopCount) {
                item.setBillAmount(tailAmount);
            } else {
                item.setBillAmount(installmentApplication.getBillAmount());
            }
            // 还款日
            if (hasTail && i == loopCount) {
                if (tailPayTime != null) {
                    item.setRepayDay(tailPayTime);
                } else {
                    java.util.Calendar cal = java.util.Calendar.getInstance();
                    cal.setTime(now);
                    cal.add(java.util.Calendar.MONTH, i - 1);
                    cal.set(java.util.Calendar.DAY_OF_MONTH, repayDayOfMonth);
                    item.setRepayDay(cal.getTime());
                }
            } else {
                java.util.Calendar cal = java.util.Calendar.getInstance();
                cal.setTime(now);
                cal.add(java.util.Calendar.MONTH, i - 1);
                cal.set(java.util.Calendar.DAY_OF_MONTH, repayDayOfMonth);
                item.setRepayDay(cal.getTime());
            }
            total += installmentApplicationService.insertInstallmentApplication(item);
        }
        return toAjax(total);
    }

    /**
     * 修改分期申请
     */
    @PreAuthorize("@ss.hasPermi('installment_application:installment_application:edit')")
    @Log(title = "分期申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstallmentApplication installmentApplication) {
        installmentApplication.setUpdateBy(getUsername());
        return toAjax(installmentApplicationService.updateInstallmentApplication(installmentApplication));
    }

    /**
     * 删除分期申请
     */
    @PreAuthorize("@ss.hasPermi('installment_application:installment_application:remove')")
    @Log(title = "分期申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(installmentApplicationService.deleteInstallmentApplicationByIds(ids));
    }
}
