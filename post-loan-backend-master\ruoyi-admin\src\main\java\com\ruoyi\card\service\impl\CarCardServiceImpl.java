package com.ruoyi.card.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.card.mapper.CarCardMapper;
import com.ruoyi.card.domain.CarCard;
import com.ruoyi.card.service.ICarCardService;

/**
 * 行驶证Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
@Service
public class CarCardServiceImpl implements ICarCardService 
{
    @Autowired
    private CarCardMapper carCardMapper;

    /**
     * 查询行驶证
     * 
     * @param id 行驶证主键
     * @return 行驶证
     */
    @Override
    public CarCard selectCarCardById(String id)
    {
        return carCardMapper.selectCarCardById(id);
    }

    /**
     * 查询行驶证列表
     * 
     * @param carCard 行驶证
     * @return 行驶证
     */
    @Override
    public List<CarCard> selectCarCardList(CarCard carCard)
    {
//        carCard.setUseNature("非营运1");
        return carCardMapper.selectCarCardList(carCard);
    }

    /**
     * 新增行驶证
     * 
     * @param carCard 行驶证
     * @return 结果
     */
    @Override
    public int insertCarCard(CarCard carCard)
    {
        return carCardMapper.insertCarCard(carCard);
    }

    /**
     * 修改行驶证
     * 
     * @param carCard 行驶证
     * @return 结果
     */
    @Override
    public int updateCarCard(CarCard carCard)
    {
        return carCardMapper.updateCarCard(carCard);
    }

    /**
     * 批量删除行驶证
     * 
     * @param ids 需要删除的行驶证主键
     * @return 结果
     */
    @Override
    public int deleteCarCardByIds(String[] ids)
    {
        return carCardMapper.deleteCarCardByIds(ids);
    }

    /**
     * 删除行驶证信息
     * 
     * @param id 行驶证主键
     * @return 结果
     */
    @Override
    public int deleteCarCardById(String id)
    {
        return carCardMapper.deleteCarCardById(id);
    }
}
