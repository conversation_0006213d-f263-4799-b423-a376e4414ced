<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.litigation_cost_submission.mapper.LitigationCostSubmissionLimitMapper">
    
    <resultMap type="LitigationCostSubmissionLimit" id="LitigationCostSubmissionLimitResult">
        <result property="id"    column="id"    />
        <result property="litigationCaseId"    column="litigation_case_id"    />
        <result property="costType"    column="cost_type"    />
        <result property="isSubmitted"    column="is_submitted"    />
        <result property="submissionTime"    column="submission_time"    />
        <result property="submittedBy"    column="submitted_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectLitigationCostSubmissionLimitVo">
        select id, litigation_case_id, cost_type, is_submitted, submission_time, submitted_by, create_time, update_time from litigation_cost_submission_limit
    </sql>

    <select id="selectLitigationCostSubmissionLimitList" parameterType="LitigationCostSubmissionLimit" resultMap="LitigationCostSubmissionLimitResult">
        <include refid="selectLitigationCostSubmissionLimitVo"/>
        <where>  
            <if test="litigationCaseId != null "> and litigation_case_id = #{litigationCaseId}</if>
            <if test="costType != null  and costType != ''"> and cost_type = #{costType}</if>
            <if test="isSubmitted != null  and isSubmitted != ''"> and is_submitted = #{isSubmitted}</if>
            <if test="submissionTime != null "> and submission_time = #{submissionTime}</if>
            <if test="submittedBy != null  and submittedBy != ''"> and submitted_by = #{submittedBy}</if>
        </where>
    </select>
    
    <select id="selectLitigationCostSubmissionLimitById" parameterType="Long" resultMap="LitigationCostSubmissionLimitResult">
        <include refid="selectLitigationCostSubmissionLimitVo"/>
        where id = #{id}
    </select>

    <insert id="insertLitigationCostSubmissionLimit" parameterType="LitigationCostSubmissionLimit" useGeneratedKeys="true" keyProperty="id">
        insert into litigation_cost_submission_limit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="litigationCaseId != null">litigation_case_id,</if>
            <if test="costType != null and costType != ''">cost_type,</if>
            <if test="isSubmitted != null and isSubmitted != ''">is_submitted,</if>
            <if test="submissionTime != null">submission_time,</if>
            <if test="submittedBy != null">submitted_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="litigationCaseId != null">#{litigationCaseId},</if>
            <if test="costType != null and costType != ''">#{costType},</if>
            <if test="isSubmitted != null and isSubmitted != ''">#{isSubmitted},</if>
            <if test="submissionTime != null">#{submissionTime},</if>
            <if test="submittedBy != null">#{submittedBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateLitigationCostSubmissionLimit" parameterType="LitigationCostSubmissionLimit">
        update litigation_cost_submission_limit
        <trim prefix="SET" suffixOverrides=",">
            <if test="litigationCaseId != null">litigation_case_id = #{litigationCaseId},</if>
            <if test="costType != null and costType != ''">cost_type = #{costType},</if>
            <if test="isSubmitted != null and isSubmitted != ''">is_submitted = #{isSubmitted},</if>
            <if test="submissionTime != null">submission_time = #{submissionTime},</if>
            <if test="submittedBy != null">submitted_by = #{submittedBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLitigationCostSubmissionLimitById" parameterType="Long">
        delete from litigation_cost_submission_limit where id = #{id}
    </delete>

    <delete id="deleteLitigationCostSubmissionLimitByIds" parameterType="String">
        delete from litigation_cost_submission_limit where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>