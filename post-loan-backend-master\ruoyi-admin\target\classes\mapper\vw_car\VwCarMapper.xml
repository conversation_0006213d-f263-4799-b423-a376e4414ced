<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vw_car.mapper.VwCarMapper">
    
    <resultMap type="VwCar" id="VwCarResult">
        <result property="applyNo"    column="apply_no"    />
        <result property="customerId"    column="customer_id"    />
        <result property="identityNo"    column="identity_no"    />
        <result property="shape"    column="shape"    />
        <result property="plateNo"    column="plate_no"    />
        <result property="carType"    column="car_type"    />
        <result property="carOwner"    column="car_owner"    />
        <result property="address"    column="address"    />
        <result property="useNature"    column="use_nature"    />
        <result property="engineNumber"    column="engine_number"    />
        <result property="registerDate"    column="register_date"    />
        <result property="issueDate"    column="issue_date"    />
        <result property="registerOrg"    column="register_org"    />
        <result property="plateProvince"    column="plate_province"    />
        <result property="plateCity"    column="plate_city"    />
        <result property="plateAddress"    column="plate_address"    />
        <result property="mileage"    column="mileage"    />
        <result property="color"    column="color"    />
        <result property="nature"    column="nature"    />
        <result property="brandType"    column="brand_type"    />
        <result property="energy"    column="energy"    />
        <result property="regIssueDate"    column="reg_issue_date"    />
        <result property="registrationDate"    column="registration_date"    />
        <result property="registrateFlag"    column="registrate_flag"    />
        <result property="registrateReason"    column="registrate_reason"    />
        <result property="brandId"    column="brand_id"    />
        <result property="brandName"    column="brand_name"    />
        <result property="seriesId"    column="series_id"    />
        <result property="seriesName"    column="series_name"    />
        <result property="modelId"    column="model_id"    />
        <result property="modelName"    column="model_name"    />
        <result property="cityId"    column="city_id"    />
        <result property="cityName"    column="city_name"    />
        <result property="mileAge"    column="mile_age"    />
        <result property="regDate"    column="reg_date"    />
        <result property="modelPrice"    column="model_price"    />
        <result property="dealerPrice"    column="dealer_price"    />
        <result property="highDealerPrice"    column="high_dealer_price"    />
        <result property="individualPrice"    column="individual_price"    />
        <result property="dealerBuyPrice"    column="dealer_buy_price"    />
        <result property="individualLowSoldPrice"    column="individual_low_sold_price"    />
        <result property="dealerLowBuyPrice"    column="dealer_low_buy_price"    />
        <result property="dealerHighSoldPrice"    column="dealer_high_sold_price"    />
        <result property="dealerLowSoldPrice"    column="dealer_low_sold_price"    />
        <result property="url"    column="url"    />
        <result property="manProvince"    column="man_province"    />
        <result property="manCity"    column="man_city"    />
        <result property="manBorough"    column="man_borough"    />
        <result property="manAddress"    column="man_address"    />
        <result property="manDetailAddress"    column="man_detail_address"    />
        <result property="carProvince"    column="car_province"    />
        <result property="carCity"    column="car_city"    />
        <result property="carBorough"    column="car_borough"    />
        <result property="carAddress"    column="car_address"    />
        <result property="carDetailAddress"    column="car_detail_address"    />
        <result property="carLandType"    column="car_land_type"    />
        <result property="carStatus"    column="car_status"    />
        <result property="gpsStatus"    column="gps_status"    />
        <result property="carColor"    column="car_color"    />
        <result property="modelYear"    column="model_year"    />
        <result property="modelLiter"    column="model_liter"    />
        <result property="modelGear"    column="model_gear"    />
        <result property="modelEmissionStandard"    column="model_emission_standard"    />
        <result property="minRegYear"    column="min_reg_year"    />
        <result property="maxRegYear"    column="max_reg_year"    />
        <result property="seriesGroupName"    column="series_group_name"    />
        <result property="reportUrl"    column="report_url"    />
        <result property="vehicleType"    column="vehicle_type"    />
        <result property="seriesType"    column="series_type"    />
        <result property="carState"    column="car_state"    />
        <result property="jzgOrderNo"    column="jzg_order_no"    />
        <result property="jzgAssessmentPriceSold"    column="jzg_assessment_price_sold"    />
        <result property="jzgAssessmentPriceBuy"    column="jzg_assessment_price_buy"    />
        <result property="jzgManufacturerPrice"    column="jzg_manufacturer_price"    />
        <result property="jzgAssessmentReportPdf"    column="jzg_assessment_report_pdf"    />
        <result property="jzgStatus"    column="jzg_status"    />
        <result property="jzgStatusDes"    column="jzg_status_des"    />
        <result property="departureDate"    column="departure_date"    />
        <result property="transferCount"    column="transfer_count"    />
        <result property="insuranceCondition"    column="insurance_condition"    />
        <result property="registrationNo"    column="registration_no"    />
        <result property="orderNo"    column="order_no"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="orderReportUrl"    column="order_report_url"    />
        <result property="orderReportWeb"    column="order_report_web"    />
        <result property="beforePlateNo"    column="before_plate_no"    />
        <result property="manufacturer"    column="manufacturer"    />
        <result property="seatNumber"    column="seat_number"    />
        <result property="wxBrandCode"    column="wx_brand_code"    />
        <result property="wxBrandName"    column="wx_brand_name"    />
        <result property="wxSeriesCode"    column="wx_series_code"    />
        <result property="wxSeriesName"    column="wx_series_name"    />
        <result property="wxModelCode"    column="wx_model_code"    />
        <result property="wxModelName"    column="wx_model_name"    />
        <result property="wxGuidancePrice"    column="wx_guidance_price"    />
        <result property="wxAssessmentPrice"    column="wx_assessment_price"    />
        <result property="wxReportOrderNo"    column="wx_report_order_no"    />
        <result property="wxReportUrl"    column="wx_report_url"    />
        <result property="isAccident"    column="is_accident"    />
        <result property="avpId"    column="avp_id"    />
    </resultMap>

    <sql id="selectVwCarVo">
        select apply_no, customer_id, identity_no, shape, plate_no, car_type, car_owner, address, use_nature, engine_number, register_date, issue_date, register_org, plate_province, plate_city, plate_address, mileage, color, nature, brand_type, energy, reg_issue_date, registration_date, registrate_flag, registrate_reason, brand_id, brand_name, series_id, series_name, model_id, model_name, city_id, city_name, mile_age, reg_date, model_price, dealer_price, high_dealer_price, individual_price, dealer_buy_price, individual_low_sold_price, dealer_low_buy_price, dealer_high_sold_price, dealer_low_sold_price, url, man_province, man_city, man_borough, man_address, man_detail_address, car_province, car_city, car_borough, car_address, car_detail_address, car_land_type, car_status, gps_status, car_color, model_year, model_liter, model_gear, model_emission_standard, min_reg_year, max_reg_year, series_group_name, report_url, vehicle_type, series_type, car_state, jzg_order_no, jzg_assessment_price_sold, jzg_assessment_price_buy, jzg_manufacturer_price, jzg_assessment_report_pdf, jzg_status, jzg_status_des, departure_date, transfer_count, insurance_condition, registration_no, order_no, order_status, order_report_url, order_report_web, before_plate_no, manufacturer, seat_number, wx_brand_code, wx_brand_name, wx_series_code, wx_series_name, wx_model_code, wx_model_name, wx_guidance_price, wx_assessment_price, wx_report_order_no, wx_report_url, is_accident, avp_id from vw_car
    </sql>

    <select id="selectVwCarList" parameterType="VwCar" resultMap="VwCarResult">
        <include refid="selectVwCarVo"/>
        <where>  
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="customerId != null  and customerId != ''"> and customer_id = #{customerId}</if>
            <if test="identityNo != null  and identityNo != ''"> and identity_no = #{identityNo}</if>
            <if test="shape != null  and shape != ''"> and shape = #{shape}</if>
            <if test="plateNo != null  and plateNo != ''"> and plate_no = #{plateNo}</if>
            <if test="carType != null  and carType != ''"> and car_type = #{carType}</if>
            <if test="carOwner != null  and carOwner != ''"> and car_owner = #{carOwner}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="useNature != null  and useNature != ''"> and use_nature = #{useNature}</if>
            <if test="engineNumber != null  and engineNumber != ''"> and engine_number = #{engineNumber}</if>
            <if test="registerDate != null  and registerDate != ''"> and register_date = #{registerDate}</if>
            <if test="issueDate != null  and issueDate != ''"> and issue_date = #{issueDate}</if>
            <if test="registerOrg != null  and registerOrg != ''"> and register_org = #{registerOrg}</if>
            <if test="plateProvince != null  and plateProvince != ''"> and plate_province = #{plateProvince}</if>
            <if test="plateCity != null  and plateCity != ''"> and plate_city = #{plateCity}</if>
            <if test="plateAddress != null  and plateAddress != ''"> and plate_address = #{plateAddress}</if>
            <if test="mileage != null "> and mileage = #{mileage}</if>
            <if test="color != null  and color != ''"> and color = #{color}</if>
            <if test="nature != null  and nature != ''"> and nature = #{nature}</if>
            <if test="brandType != null  and brandType != ''"> and brand_type = #{brandType}</if>
            <if test="energy != null  and energy != ''"> and energy = #{energy}</if>
            <if test="regIssueDate != null  and regIssueDate != ''"> and reg_issue_date = #{regIssueDate}</if>
            <if test="registrationDate != null  and registrationDate != ''"> and registration_date = #{registrationDate}</if>
            <if test="registrateFlag != null  and registrateFlag != ''"> and registrate_flag = #{registrateFlag}</if>
            <if test="registrateReason != null  and registrateReason != ''"> and registrate_reason = #{registrateReason}</if>
            <if test="brandId != null  and brandId != ''"> and brand_id = #{brandId}</if>
            <if test="brandName != null  and brandName != ''"> and brand_name like concat('%', #{brandName}, '%')</if>
            <if test="seriesId != null  and seriesId != ''"> and series_id = #{seriesId}</if>
            <if test="seriesName != null  and seriesName != ''"> and series_name like concat('%', #{seriesName}, '%')</if>
            <if test="modelId != null  and modelId != ''"> and model_id = #{modelId}</if>
            <if test="modelName != null  and modelName != ''"> and model_name like concat('%', #{modelName}, '%')</if>
            <if test="cityId != null  and cityId != ''"> and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''"> and city_name like concat('%', #{cityName}, '%')</if>
            <if test="mileAge != null "> and mile_age = #{mileAge}</if>
            <if test="regDate != null  and regDate != ''"> and reg_date = #{regDate}</if>
            <if test="modelPrice != null "> and model_price = #{modelPrice}</if>
            <if test="dealerPrice != null "> and dealer_price = #{dealerPrice}</if>
            <if test="highDealerPrice != null "> and high_dealer_price = #{highDealerPrice}</if>
            <if test="individualPrice != null "> and individual_price = #{individualPrice}</if>
            <if test="dealerBuyPrice != null "> and dealer_buy_price = #{dealerBuyPrice}</if>
            <if test="individualLowSoldPrice != null "> and individual_low_sold_price = #{individualLowSoldPrice}</if>
            <if test="dealerLowBuyPrice != null "> and dealer_low_buy_price = #{dealerLowBuyPrice}</if>
            <if test="dealerHighSoldPrice != null "> and dealer_high_sold_price = #{dealerHighSoldPrice}</if>
            <if test="dealerLowSoldPrice != null "> and dealer_low_sold_price = #{dealerLowSoldPrice}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="manProvince != null  and manProvince != ''"> and man_province = #{manProvince}</if>
            <if test="manCity != null  and manCity != ''"> and man_city = #{manCity}</if>
            <if test="manBorough != null  and manBorough != ''"> and man_borough = #{manBorough}</if>
            <if test="manAddress != null  and manAddress != ''"> and man_address = #{manAddress}</if>
            <if test="manDetailAddress != null  and manDetailAddress != ''"> and man_detail_address = #{manDetailAddress}</if>
            <if test="carProvince != null  and carProvince != ''"> and car_province = #{carProvince}</if>
            <if test="carCity != null  and carCity != ''"> and car_city = #{carCity}</if>
            <if test="carBorough != null  and carBorough != ''"> and car_borough = #{carBorough}</if>
            <if test="carAddress != null  and carAddress != ''"> and car_address = #{carAddress}</if>
            <if test="carDetailAddress != null  and carDetailAddress != ''"> and car_detail_address = #{carDetailAddress}</if>
            <if test="carLandType != null  and carLandType != ''"> and car_land_type = #{carLandType}</if>
            <if test="carStatus != null  and carStatus != ''"> and car_status = #{carStatus}</if>
            <if test="gpsStatus != null  and gpsStatus != ''"> and gps_status = #{gpsStatus}</if>
            <if test="carColor != null  and carColor != ''"> and car_color = #{carColor}</if>
            <if test="modelYear != null "> and model_year = #{modelYear}</if>
            <if test="modelLiter != null  and modelLiter != ''"> and model_liter = #{modelLiter}</if>
            <if test="modelGear != null  and modelGear != ''"> and model_gear = #{modelGear}</if>
            <if test="modelEmissionStandard != null  and modelEmissionStandard != ''"> and model_emission_standard = #{modelEmissionStandard}</if>
            <if test="minRegYear != null "> and min_reg_year = #{minRegYear}</if>
            <if test="maxRegYear != null "> and max_reg_year = #{maxRegYear}</if>
            <if test="seriesGroupName != null  and seriesGroupName != ''"> and series_group_name like concat('%', #{seriesGroupName}, '%')</if>
            <if test="reportUrl != null  and reportUrl != ''"> and report_url = #{reportUrl}</if>
            <if test="vehicleType != null  and vehicleType != ''"> and vehicle_type = #{vehicleType}</if>
            <if test="seriesType != null  and seriesType != ''"> and series_type = #{seriesType}</if>
            <if test="carState != null  and carState != ''"> and car_state = #{carState}</if>
            <if test="jzgOrderNo != null  and jzgOrderNo != ''"> and jzg_order_no = #{jzgOrderNo}</if>
            <if test="jzgAssessmentPriceSold != null "> and jzg_assessment_price_sold = #{jzgAssessmentPriceSold}</if>
            <if test="jzgAssessmentPriceBuy != null "> and jzg_assessment_price_buy = #{jzgAssessmentPriceBuy}</if>
            <if test="jzgManufacturerPrice != null "> and jzg_manufacturer_price = #{jzgManufacturerPrice}</if>
            <if test="jzgAssessmentReportPdf != null  and jzgAssessmentReportPdf != ''"> and jzg_assessment_report_pdf = #{jzgAssessmentReportPdf}</if>
            <if test="jzgStatus != null  and jzgStatus != ''"> and jzg_status = #{jzgStatus}</if>
            <if test="jzgStatusDes != null  and jzgStatusDes != ''"> and jzg_status_des = #{jzgStatusDes}</if>
            <if test="departureDate != null  and departureDate != ''"> and departure_date = #{departureDate}</if>
            <if test="transferCount != null "> and transfer_count = #{transferCount}</if>
            <if test="insuranceCondition != null  and insuranceCondition != ''"> and insurance_condition = #{insuranceCondition}</if>
            <if test="registrationNo != null  and registrationNo != ''"> and registration_no = #{registrationNo}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="orderStatus != null  and orderStatus != ''"> and order_status = #{orderStatus}</if>
            <if test="orderReportUrl != null  and orderReportUrl != ''"> and order_report_url = #{orderReportUrl}</if>
            <if test="orderReportWeb != null  and orderReportWeb != ''"> and order_report_web = #{orderReportWeb}</if>
            <if test="beforePlateNo != null  and beforePlateNo != ''"> and before_plate_no = #{beforePlateNo}</if>
            <if test="manufacturer != null  and manufacturer != ''"> and manufacturer = #{manufacturer}</if>
            <if test="seatNumber != null "> and seat_number = #{seatNumber}</if>
            <if test="wxBrandCode != null  and wxBrandCode != ''"> and wx_brand_code = #{wxBrandCode}</if>
            <if test="wxBrandName != null  and wxBrandName != ''"> and wx_brand_name like concat('%', #{wxBrandName}, '%')</if>
            <if test="wxSeriesCode != null  and wxSeriesCode != ''"> and wx_series_code = #{wxSeriesCode}</if>
            <if test="wxSeriesName != null  and wxSeriesName != ''"> and wx_series_name like concat('%', #{wxSeriesName}, '%')</if>
            <if test="wxModelCode != null  and wxModelCode != ''"> and wx_model_code = #{wxModelCode}</if>
            <if test="wxModelName != null  and wxModelName != ''"> and wx_model_name like concat('%', #{wxModelName}, '%')</if>
            <if test="wxGuidancePrice != null "> and wx_guidance_price = #{wxGuidancePrice}</if>
            <if test="wxAssessmentPrice != null "> and wx_assessment_price = #{wxAssessmentPrice}</if>
            <if test="wxReportOrderNo != null  and wxReportOrderNo != ''"> and wx_report_order_no = #{wxReportOrderNo}</if>
            <if test="wxReportUrl != null  and wxReportUrl != ''"> and wx_report_url = #{wxReportUrl}</if>
            <if test="isAccident != null  and isAccident != ''"> and is_accident = #{isAccident}</if>
            <if test="avpId != null  and avpId != ''"> and avp_id = #{avpId}</if>
        </where>
    </select>
    
    <select id="selectVwCarByApplyNo" parameterType="String" resultMap="VwCarResult">
        <include refid="selectVwCarVo"/>
        where plate_no = #{applyNo}
    </select>

    <insert id="insertVwCar" parameterType="VwCar">
        insert into vw_car
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyNo != null">apply_no,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="identityNo != null">identity_no,</if>
            <if test="shape != null">shape,</if>
            <if test="plateNo != null">plate_no,</if>
            <if test="carType != null">car_type,</if>
            <if test="carOwner != null">car_owner,</if>
            <if test="address != null">address,</if>
            <if test="useNature != null">use_nature,</if>
            <if test="engineNumber != null">engine_number,</if>
            <if test="registerDate != null">register_date,</if>
            <if test="issueDate != null">issue_date,</if>
            <if test="registerOrg != null">register_org,</if>
            <if test="plateProvince != null">plate_province,</if>
            <if test="plateCity != null">plate_city,</if>
            <if test="plateAddress != null">plate_address,</if>
            <if test="mileage != null">mileage,</if>
            <if test="color != null">color,</if>
            <if test="nature != null">nature,</if>
            <if test="brandType != null">brand_type,</if>
            <if test="energy != null">energy,</if>
            <if test="regIssueDate != null">reg_issue_date,</if>
            <if test="registrationDate != null">registration_date,</if>
            <if test="registrateFlag != null">registrate_flag,</if>
            <if test="registrateReason != null">registrate_reason,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="brandName != null">brand_name,</if>
            <if test="seriesId != null">series_id,</if>
            <if test="seriesName != null">series_name,</if>
            <if test="modelId != null">model_id,</if>
            <if test="modelName != null">model_name,</if>
            <if test="cityId != null">city_id,</if>
            <if test="cityName != null">city_name,</if>
            <if test="mileAge != null">mile_age,</if>
            <if test="regDate != null">reg_date,</if>
            <if test="modelPrice != null">model_price,</if>
            <if test="dealerPrice != null">dealer_price,</if>
            <if test="highDealerPrice != null">high_dealer_price,</if>
            <if test="individualPrice != null">individual_price,</if>
            <if test="dealerBuyPrice != null">dealer_buy_price,</if>
            <if test="individualLowSoldPrice != null">individual_low_sold_price,</if>
            <if test="dealerLowBuyPrice != null">dealer_low_buy_price,</if>
            <if test="dealerHighSoldPrice != null">dealer_high_sold_price,</if>
            <if test="dealerLowSoldPrice != null">dealer_low_sold_price,</if>
            <if test="url != null">url,</if>
            <if test="manProvince != null">man_province,</if>
            <if test="manCity != null">man_city,</if>
            <if test="manBorough != null">man_borough,</if>
            <if test="manAddress != null">man_address,</if>
            <if test="manDetailAddress != null">man_detail_address,</if>
            <if test="carProvince != null">car_province,</if>
            <if test="carCity != null">car_city,</if>
            <if test="carBorough != null">car_borough,</if>
            <if test="carAddress != null">car_address,</if>
            <if test="carDetailAddress != null">car_detail_address,</if>
            <if test="carLandType != null">car_land_type,</if>
            <if test="carStatus != null">car_status,</if>
            <if test="gpsStatus != null">gps_status,</if>
            <if test="carColor != null">car_color,</if>
            <if test="modelYear != null">model_year,</if>
            <if test="modelLiter != null">model_liter,</if>
            <if test="modelGear != null">model_gear,</if>
            <if test="modelEmissionStandard != null">model_emission_standard,</if>
            <if test="minRegYear != null">min_reg_year,</if>
            <if test="maxRegYear != null">max_reg_year,</if>
            <if test="seriesGroupName != null">series_group_name,</if>
            <if test="reportUrl != null">report_url,</if>
            <if test="vehicleType != null">vehicle_type,</if>
            <if test="seriesType != null">series_type,</if>
            <if test="carState != null">car_state,</if>
            <if test="jzgOrderNo != null">jzg_order_no,</if>
            <if test="jzgAssessmentPriceSold != null">jzg_assessment_price_sold,</if>
            <if test="jzgAssessmentPriceBuy != null">jzg_assessment_price_buy,</if>
            <if test="jzgManufacturerPrice != null">jzg_manufacturer_price,</if>
            <if test="jzgAssessmentReportPdf != null">jzg_assessment_report_pdf,</if>
            <if test="jzgStatus != null">jzg_status,</if>
            <if test="jzgStatusDes != null">jzg_status_des,</if>
            <if test="departureDate != null">departure_date,</if>
            <if test="transferCount != null">transfer_count,</if>
            <if test="insuranceCondition != null">insurance_condition,</if>
            <if test="registrationNo != null">registration_no,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="orderReportUrl != null">order_report_url,</if>
            <if test="orderReportWeb != null">order_report_web,</if>
            <if test="beforePlateNo != null">before_plate_no,</if>
            <if test="manufacturer != null">manufacturer,</if>
            <if test="seatNumber != null">seat_number,</if>
            <if test="wxBrandCode != null">wx_brand_code,</if>
            <if test="wxBrandName != null">wx_brand_name,</if>
            <if test="wxSeriesCode != null">wx_series_code,</if>
            <if test="wxSeriesName != null">wx_series_name,</if>
            <if test="wxModelCode != null">wx_model_code,</if>
            <if test="wxModelName != null">wx_model_name,</if>
            <if test="wxGuidancePrice != null">wx_guidance_price,</if>
            <if test="wxAssessmentPrice != null">wx_assessment_price,</if>
            <if test="wxReportOrderNo != null">wx_report_order_no,</if>
            <if test="wxReportUrl != null">wx_report_url,</if>
            <if test="isAccident != null">is_accident,</if>
            <if test="avpId != null">avp_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyNo != null">#{applyNo},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="identityNo != null">#{identityNo},</if>
            <if test="shape != null">#{shape},</if>
            <if test="plateNo != null">#{plateNo},</if>
            <if test="carType != null">#{carType},</if>
            <if test="carOwner != null">#{carOwner},</if>
            <if test="address != null">#{address},</if>
            <if test="useNature != null">#{useNature},</if>
            <if test="engineNumber != null">#{engineNumber},</if>
            <if test="registerDate != null">#{registerDate},</if>
            <if test="issueDate != null">#{issueDate},</if>
            <if test="registerOrg != null">#{registerOrg},</if>
            <if test="plateProvince != null">#{plateProvince},</if>
            <if test="plateCity != null">#{plateCity},</if>
            <if test="plateAddress != null">#{plateAddress},</if>
            <if test="mileage != null">#{mileage},</if>
            <if test="color != null">#{color},</if>
            <if test="nature != null">#{nature},</if>
            <if test="brandType != null">#{brandType},</if>
            <if test="energy != null">#{energy},</if>
            <if test="regIssueDate != null">#{regIssueDate},</if>
            <if test="registrationDate != null">#{registrationDate},</if>
            <if test="registrateFlag != null">#{registrateFlag},</if>
            <if test="registrateReason != null">#{registrateReason},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="brandName != null">#{brandName},</if>
            <if test="seriesId != null">#{seriesId},</if>
            <if test="seriesName != null">#{seriesName},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="modelName != null">#{modelName},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="cityName != null">#{cityName},</if>
            <if test="mileAge != null">#{mileAge},</if>
            <if test="regDate != null">#{regDate},</if>
            <if test="modelPrice != null">#{modelPrice},</if>
            <if test="dealerPrice != null">#{dealerPrice},</if>
            <if test="highDealerPrice != null">#{highDealerPrice},</if>
            <if test="individualPrice != null">#{individualPrice},</if>
            <if test="dealerBuyPrice != null">#{dealerBuyPrice},</if>
            <if test="individualLowSoldPrice != null">#{individualLowSoldPrice},</if>
            <if test="dealerLowBuyPrice != null">#{dealerLowBuyPrice},</if>
            <if test="dealerHighSoldPrice != null">#{dealerHighSoldPrice},</if>
            <if test="dealerLowSoldPrice != null">#{dealerLowSoldPrice},</if>
            <if test="url != null">#{url},</if>
            <if test="manProvince != null">#{manProvince},</if>
            <if test="manCity != null">#{manCity},</if>
            <if test="manBorough != null">#{manBorough},</if>
            <if test="manAddress != null">#{manAddress},</if>
            <if test="manDetailAddress != null">#{manDetailAddress},</if>
            <if test="carProvince != null">#{carProvince},</if>
            <if test="carCity != null">#{carCity},</if>
            <if test="carBorough != null">#{carBorough},</if>
            <if test="carAddress != null">#{carAddress},</if>
            <if test="carDetailAddress != null">#{carDetailAddress},</if>
            <if test="carLandType != null">#{carLandType},</if>
            <if test="carStatus != null">#{carStatus},</if>
            <if test="gpsStatus != null">#{gpsStatus},</if>
            <if test="carColor != null">#{carColor},</if>
            <if test="modelYear != null">#{modelYear},</if>
            <if test="modelLiter != null">#{modelLiter},</if>
            <if test="modelGear != null">#{modelGear},</if>
            <if test="modelEmissionStandard != null">#{modelEmissionStandard},</if>
            <if test="minRegYear != null">#{minRegYear},</if>
            <if test="maxRegYear != null">#{maxRegYear},</if>
            <if test="seriesGroupName != null">#{seriesGroupName},</if>
            <if test="reportUrl != null">#{reportUrl},</if>
            <if test="vehicleType != null">#{vehicleType},</if>
            <if test="seriesType != null">#{seriesType},</if>
            <if test="carState != null">#{carState},</if>
            <if test="jzgOrderNo != null">#{jzgOrderNo},</if>
            <if test="jzgAssessmentPriceSold != null">#{jzgAssessmentPriceSold},</if>
            <if test="jzgAssessmentPriceBuy != null">#{jzgAssessmentPriceBuy},</if>
            <if test="jzgManufacturerPrice != null">#{jzgManufacturerPrice},</if>
            <if test="jzgAssessmentReportPdf != null">#{jzgAssessmentReportPdf},</if>
            <if test="jzgStatus != null">#{jzgStatus},</if>
            <if test="jzgStatusDes != null">#{jzgStatusDes},</if>
            <if test="departureDate != null">#{departureDate},</if>
            <if test="transferCount != null">#{transferCount},</if>
            <if test="insuranceCondition != null">#{insuranceCondition},</if>
            <if test="registrationNo != null">#{registrationNo},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="orderReportUrl != null">#{orderReportUrl},</if>
            <if test="orderReportWeb != null">#{orderReportWeb},</if>
            <if test="beforePlateNo != null">#{beforePlateNo},</if>
            <if test="manufacturer != null">#{manufacturer},</if>
            <if test="seatNumber != null">#{seatNumber},</if>
            <if test="wxBrandCode != null">#{wxBrandCode},</if>
            <if test="wxBrandName != null">#{wxBrandName},</if>
            <if test="wxSeriesCode != null">#{wxSeriesCode},</if>
            <if test="wxSeriesName != null">#{wxSeriesName},</if>
            <if test="wxModelCode != null">#{wxModelCode},</if>
            <if test="wxModelName != null">#{wxModelName},</if>
            <if test="wxGuidancePrice != null">#{wxGuidancePrice},</if>
            <if test="wxAssessmentPrice != null">#{wxAssessmentPrice},</if>
            <if test="wxReportOrderNo != null">#{wxReportOrderNo},</if>
            <if test="wxReportUrl != null">#{wxReportUrl},</if>
            <if test="isAccident != null">#{isAccident},</if>
            <if test="avpId != null">#{avpId},</if>
         </trim>
    </insert>

    <update id="updateVwCar" parameterType="VwCar">
        update vw_car
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="identityNo != null">identity_no = #{identityNo},</if>
            <if test="shape != null">shape = #{shape},</if>
            <if test="plateNo != null">plate_no = #{plateNo},</if>
            <if test="carType != null">car_type = #{carType},</if>
            <if test="carOwner != null">car_owner = #{carOwner},</if>
            <if test="address != null">address = #{address},</if>
            <if test="useNature != null">use_nature = #{useNature},</if>
            <if test="engineNumber != null">engine_number = #{engineNumber},</if>
            <if test="registerDate != null">register_date = #{registerDate},</if>
            <if test="issueDate != null">issue_date = #{issueDate},</if>
            <if test="registerOrg != null">register_org = #{registerOrg},</if>
            <if test="plateProvince != null">plate_province = #{plateProvince},</if>
            <if test="plateCity != null">plate_city = #{plateCity},</if>
            <if test="plateAddress != null">plate_address = #{plateAddress},</if>
            <if test="mileage != null">mileage = #{mileage},</if>
            <if test="color != null">color = #{color},</if>
            <if test="nature != null">nature = #{nature},</if>
            <if test="brandType != null">brand_type = #{brandType},</if>
            <if test="energy != null">energy = #{energy},</if>
            <if test="regIssueDate != null">reg_issue_date = #{regIssueDate},</if>
            <if test="registrationDate != null">registration_date = #{registrationDate},</if>
            <if test="registrateFlag != null">registrate_flag = #{registrateFlag},</if>
            <if test="registrateReason != null">registrate_reason = #{registrateReason},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="brandName != null">brand_name = #{brandName},</if>
            <if test="seriesId != null">series_id = #{seriesId},</if>
            <if test="seriesName != null">series_name = #{seriesName},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="modelName != null">model_name = #{modelName},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="mileAge != null">mile_age = #{mileAge},</if>
            <if test="regDate != null">reg_date = #{regDate},</if>
            <if test="modelPrice != null">model_price = #{modelPrice},</if>
            <if test="dealerPrice != null">dealer_price = #{dealerPrice},</if>
            <if test="highDealerPrice != null">high_dealer_price = #{highDealerPrice},</if>
            <if test="individualPrice != null">individual_price = #{individualPrice},</if>
            <if test="dealerBuyPrice != null">dealer_buy_price = #{dealerBuyPrice},</if>
            <if test="individualLowSoldPrice != null">individual_low_sold_price = #{individualLowSoldPrice},</if>
            <if test="dealerLowBuyPrice != null">dealer_low_buy_price = #{dealerLowBuyPrice},</if>
            <if test="dealerHighSoldPrice != null">dealer_high_sold_price = #{dealerHighSoldPrice},</if>
            <if test="dealerLowSoldPrice != null">dealer_low_sold_price = #{dealerLowSoldPrice},</if>
            <if test="url != null">url = #{url},</if>
            <if test="manProvince != null">man_province = #{manProvince},</if>
            <if test="manCity != null">man_city = #{manCity},</if>
            <if test="manBorough != null">man_borough = #{manBorough},</if>
            <if test="manAddress != null">man_address = #{manAddress},</if>
            <if test="manDetailAddress != null">man_detail_address = #{manDetailAddress},</if>
            <if test="carProvince != null">car_province = #{carProvince},</if>
            <if test="carCity != null">car_city = #{carCity},</if>
            <if test="carBorough != null">car_borough = #{carBorough},</if>
            <if test="carAddress != null">car_address = #{carAddress},</if>
            <if test="carDetailAddress != null">car_detail_address = #{carDetailAddress},</if>
            <if test="carLandType != null">car_land_type = #{carLandType},</if>
            <if test="carStatus != null">car_status = #{carStatus},</if>
            <if test="gpsStatus != null">gps_status = #{gpsStatus},</if>
            <if test="carColor != null">car_color = #{carColor},</if>
            <if test="modelYear != null">model_year = #{modelYear},</if>
            <if test="modelLiter != null">model_liter = #{modelLiter},</if>
            <if test="modelGear != null">model_gear = #{modelGear},</if>
            <if test="modelEmissionStandard != null">model_emission_standard = #{modelEmissionStandard},</if>
            <if test="minRegYear != null">min_reg_year = #{minRegYear},</if>
            <if test="maxRegYear != null">max_reg_year = #{maxRegYear},</if>
            <if test="seriesGroupName != null">series_group_name = #{seriesGroupName},</if>
            <if test="reportUrl != null">report_url = #{reportUrl},</if>
            <if test="vehicleType != null">vehicle_type = #{vehicleType},</if>
            <if test="seriesType != null">series_type = #{seriesType},</if>
            <if test="carState != null">car_state = #{carState},</if>
            <if test="jzgOrderNo != null">jzg_order_no = #{jzgOrderNo},</if>
            <if test="jzgAssessmentPriceSold != null">jzg_assessment_price_sold = #{jzgAssessmentPriceSold},</if>
            <if test="jzgAssessmentPriceBuy != null">jzg_assessment_price_buy = #{jzgAssessmentPriceBuy},</if>
            <if test="jzgManufacturerPrice != null">jzg_manufacturer_price = #{jzgManufacturerPrice},</if>
            <if test="jzgAssessmentReportPdf != null">jzg_assessment_report_pdf = #{jzgAssessmentReportPdf},</if>
            <if test="jzgStatus != null">jzg_status = #{jzgStatus},</if>
            <if test="jzgStatusDes != null">jzg_status_des = #{jzgStatusDes},</if>
            <if test="departureDate != null">departure_date = #{departureDate},</if>
            <if test="transferCount != null">transfer_count = #{transferCount},</if>
            <if test="insuranceCondition != null">insurance_condition = #{insuranceCondition},</if>
            <if test="registrationNo != null">registration_no = #{registrationNo},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="orderReportUrl != null">order_report_url = #{orderReportUrl},</if>
            <if test="orderReportWeb != null">order_report_web = #{orderReportWeb},</if>
            <if test="beforePlateNo != null">before_plate_no = #{beforePlateNo},</if>
            <if test="manufacturer != null">manufacturer = #{manufacturer},</if>
            <if test="seatNumber != null">seat_number = #{seatNumber},</if>
            <if test="wxBrandCode != null">wx_brand_code = #{wxBrandCode},</if>
            <if test="wxBrandName != null">wx_brand_name = #{wxBrandName},</if>
            <if test="wxSeriesCode != null">wx_series_code = #{wxSeriesCode},</if>
            <if test="wxSeriesName != null">wx_series_name = #{wxSeriesName},</if>
            <if test="wxModelCode != null">wx_model_code = #{wxModelCode},</if>
            <if test="wxModelName != null">wx_model_name = #{wxModelName},</if>
            <if test="wxGuidancePrice != null">wx_guidance_price = #{wxGuidancePrice},</if>
            <if test="wxAssessmentPrice != null">wx_assessment_price = #{wxAssessmentPrice},</if>
            <if test="wxReportOrderNo != null">wx_report_order_no = #{wxReportOrderNo},</if>
            <if test="wxReportUrl != null">wx_report_url = #{wxReportUrl},</if>
            <if test="isAccident != null">is_accident = #{isAccident},</if>
            <if test="avpId != null">avp_id = #{avpId},</if>
        </trim>
        where apply_no = #{applyNo}
    </update>

    <delete id="deleteVwCarByApplyNo" parameterType="String">
        delete from vw_car where apply_no = #{applyNo}
    </delete>

    <delete id="deleteVwCarByApplyNos" parameterType="String">
        delete from vw_car where apply_no in 
        <foreach item="applyNo" collection="array" open="(" separator="," close=")">
            #{applyNo}
        </foreach>
    </delete>
</mapper>