package com.ruoyi.garage.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 车库对象 garage
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
public class Garage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Integer id;

    /** 车库名称 */
    @Excel(name = "车库名称")
    private String name;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contacts;

    /** 电话 */
    @Excel(name = "电话")
    private String mobile;

    /** 单位所在-省份 */
    @Excel(name = "单位所在-省份")
    private String province;

    /** 单位所在-城市 */
    @Excel(name = "单位所在-城市")
    private String city;

    /** 单位所在-县/区 */
    @Excel(name = "单位所在-县/区")
    private String borough;

    /** 单位所在-地址 */
    @Excel(name = "单位所在-地址")
    private String address;

    /** 1-启用，2-停用 */
    @Excel(name = "1-启用，2-停用")
    private String status;

    /** 纬度 */
    @Excel(name = "纬度")
    private String lat;

    /** 经度 */
    @Excel(name = "经度")
    private String lng;

    /** 入库数量 */
    @Excel(name = "入库数量")
    private Integer num1;

    /** 出库输入 */
    @Excel(name = "出库输入")
    private Integer num2;

    /** 在库数量 */
    @Excel(name = "在库数量")
    private Integer num3;

    private Date startDate;

    private Date endDate;

    private Date enableDate;

    /** $column.columnComment */
    private Date createDate;

    /** $column.columnComment */
    private Date updateDate;

    /** $column.columnComment */
    private String delFlag;

    public void setId(Integer id) 
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setContacts(String contacts) 
    {
        this.contacts = contacts;
    }

    public String getContacts() 
    {
        return contacts;
    }

    public void setMobile(String mobile) 
    {
        this.mobile = mobile;
    }

    public String getMobile() 
    {
        return mobile;
    }

    public void setProvince(String province) 
    {
        this.province = province;
    }

    public String getProvince() 
    {
        return province;
    }

    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }

    public void setBorough(String borough) 
    {
        this.borough = borough;
    }

    public String getBorough() 
    {
        return borough;
    }

    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setLat(String lat) 
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }

    public void setLng(String lng) 
    {
        this.lng = lng;
    }

    public String getLng() 
    {
        return lng;
    }

    public void setNum1(Integer num1) 
    {
        this.num1 = num1;
    }

    public Integer getNum1() 
    {
        return num1;
    }

    public void setNum2(Integer num2) 
    {
        this.num2 = num2;
    }

    public Integer getNum2() 
    {
        return num2;
    }

    public void setNum3(Integer num3) 
    {
        this.num3 = num3;
    }

    public Integer getNum3() 
    {
        return num3;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public Date getEnableDate() {
        return enableDate;
    }

    public void setEnableDate(Date enableDate) {
        this.enableDate = enableDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("contacts", getContacts())
            .append("mobile", getMobile())
            .append("province", getProvince())
            .append("city", getCity())
            .append("borough", getBorough())
            .append("address", getAddress())
            .append("status", getStatus())
            .append("lat", getLat())
            .append("lng", getLng())
            .append("num1", getNum1())
            .append("num2", getNum2())
            .append("num3", getNum3())
            .append("createBy", getCreateBy())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .append("delFlag", getDelFlag())
            .append("enableDate", getEnableDate())
            .toString();
    }
}
