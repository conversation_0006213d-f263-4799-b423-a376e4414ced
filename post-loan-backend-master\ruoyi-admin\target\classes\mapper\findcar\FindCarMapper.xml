<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.findcar.mapper.FindCarMapper">
    
    <resultMap type="FindCar" id="FindCarResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="guarantyId"    column="guaranty_id"    />
        <result property="carStatus"    column="car_status"    />
        <result property="store"    column="store"    />
        <result property="img"    column="img"    />
        <result property="collectionMethod"    column="collection_method"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectFindCarVo">
        select id, member_id, guaranty_id, car_status, store, img, collection_method, create_by, create_date, update_by, update_date, del_flag from find_car
    </sql>

    <select id="selectFindCarList" parameterType="FindCar" resultMap="FindCarResult">
        <include refid="selectFindCarVo"/>
        <where>  
            <if test="carStatus != null  and carStatus != ''"> and car_status = #{carStatus}</if>
            <if test="img != null  and img != ''"> and img = #{img}</if>
            <if test="collectionMethod != null  and collectionMethod != ''"> and collection_method = #{collectionMethod}</if>
        </where>
    </select>
    
    <select id="selectFindCarById" parameterType="String" resultMap="FindCarResult">
        <include refid="selectFindCarVo"/>
        where id = #{id}
    </select>

    <insert id="insertFindCar" parameterType="FindCar" useGeneratedKeys="true" keyProperty="id">
        insert into find_car
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="memberId != null">member_id,</if>
            <if test="guarantyId != null">guaranty_id,</if>
            <if test="carStatus != null">car_status,</if>
            <if test="store != null">store,</if>
            <if test="img != null">img,</if>
            <if test="collectionMethod != null">collection_method,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="memberId != null">#{memberId},</if>
            <if test="guarantyId != null">#{guarantyId},</if>
            <if test="carStatus != null">#{carStatus},</if>
            <if test="store != null">#{store},</if>
            <if test="img != null">#{img},</if>
            <if test="collectionMethod != null">#{collectionMethod},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateFindCar" parameterType="FindCar">
        update find_car
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="guarantyId != null">guaranty_id = #{guarantyId},</if>
            <if test="carStatus != null">car_status = #{carStatus},</if>
            <if test="store != null">store = #{store},</if>
            <if test="img != null">img = #{img},</if>
            <if test="collectionMethod != null">collection_method = #{collectionMethod},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFindCarById" parameterType="String">
        delete from find_car where id = #{id}
    </delete>

    <delete id="deleteFindCarByIds" parameterType="String">
        delete from find_car where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>