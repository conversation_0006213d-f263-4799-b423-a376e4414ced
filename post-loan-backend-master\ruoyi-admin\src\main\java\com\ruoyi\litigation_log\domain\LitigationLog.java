package com.ruoyi.litigation_log.domain;

import java.util.Date;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.deser.std.DateDeserializers;
import com.fasterxml.jackson.databind.ser.std.DateSerializer;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import java.io.IOException;

/**
 * 法诉日志对象 litigation_log
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public class LitigationLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**
     * 自定义日期反序列化器，支持ISO 8601格式
     */
    public static class CustomDateDeserializer extends JsonDeserializer<Date> {
        private static final String[] DATE_FORMATS = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
            "yyyy-MM-dd'T'HH:mm:ss'Z'",
            "yyyy-MM-dd'T'HH:mm:ss.SSSXXX",
            "yyyy-MM-dd'T'HH:mm:ssXXX",
            "yyyy-MM-dd"
        };

        @Override
        public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String dateStr = p.getText();
            if (dateStr == null || dateStr.trim().isEmpty()) {
                return null;
            }

            for (String format : DATE_FORMATS) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(format);
                    return sdf.parse(dateStr);
                } catch (ParseException e) {
                    // 继续尝试下一个格式
                }
            }
            
            // 如果所有格式都失败，抛出异常
            throw new IOException("无法解析日期字符串: " + dateStr);
        }
    }

    /** 主键 */
    private Long id;

    /** 贷款ID */
    @Excel(name = "贷款ID")
    private Long loanId;

    /** 文书名称 */
    @Excel(name = "文书名称")
    private String docName;

    /** 文书号 */
    @Excel(name = "文书号")
    private String docNumber;

    /** 文书照片上传地址 */
    @Excel(name = "文书照片上传地址")
    private String docUploadUrl;

    /** 预计开庭时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预计开庭时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date openDate;

    /** 法诉id */
    @Excel(name = "法诉id")
    private Long litigationId;

    /** 法务状态 */
    @Excel(name = "法务状态")
    private String status;

    /** 文书生效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = CustomDateDeserializer.class)
    @Excel(name = "文书生效时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date docEffectiveDate;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLoanId(Long loanId) 
    {
        this.loanId = loanId;
    }

    public Long getLoanId() 
    {
        return loanId;
    }

    public void setDocName(String docName) 
    {
        this.docName = docName;
    }

    public String getDocName() 
    {
        return docName;
    }

    public void setDocNumber(String docNumber) 
    {
        this.docNumber = docNumber;
    }

    public String getDocNumber() 
    {
        return docNumber;
    }

    public void setDocUploadUrl(String docUploadUrl) 
    {
        this.docUploadUrl = docUploadUrl;
    }

    public String getDocUploadUrl() 
    {
        return docUploadUrl;
    }

    public void setOpenDate(Date openDate) 
    {
        this.openDate = openDate;
    }

    public Date getOpenDate() 
    {
        return openDate;
    }

    public void setLitigationId(Long litigationId) 
    {
        this.litigationId = litigationId;
    }

    public Long getLitigationId() 
    {
        return litigationId;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setDocEffectiveDate(Date docEffectiveDate) 
    {
        this.docEffectiveDate = docEffectiveDate;
    }

    public Date getDocEffectiveDate() 
    {
        return docEffectiveDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("loanId", getLoanId())
            .append("docName", getDocName())
            .append("docNumber", getDocNumber())
            .append("docUploadUrl", getDocUploadUrl())
            .append("openDate", getOpenDate())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("litigationId", getLitigationId())
            .append("status", getStatus())
            .append("docEffectiveDate", getDocEffectiveDate())
            .toString();
    }
}
