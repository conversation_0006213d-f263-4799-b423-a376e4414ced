package com.ruoyi.reimburse.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.reimburse.domain.Reimburse;
import com.ruoyi.reimburse.service.IReimburseService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 报销记录Controller
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
@RestController
@RequestMapping("/reimburse/reimburse")
public class ReimburseController extends BaseController
{
    @Autowired
    private IReimburseService reimburseService;

    /**
     * 查询报销记录列表
     */
    @PreAuthorize("@ss.hasPermi('reimburse:reimburse:list')")
    @GetMapping("/list")
    public TableDataInfo list(Reimburse reimburse)
    {
        startPage();
        List<Reimburse> list = reimburseService.selectReimburseList(reimburse);
        return getDataTable(list);
    }

    /**
     * 导出报销记录列表
     */
    @PreAuthorize("@ss.hasPermi('reimburse:reimburse:export')")
    @Log(title = "报销记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Reimburse reimburse)
    {
        List<Reimburse> list = reimburseService.selectReimburseList(reimburse);
        ExcelUtil<Reimburse> util = new ExcelUtil<Reimburse>(Reimburse.class);
        util.exportExcel(response, list, "报销记录数据");
    }

    /**
     * 获取报销记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('reimburse:reimburse:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(reimburseService.selectReimburseById(id));
    }

    /**
     * 新增报销记录
     */
    @PreAuthorize("@ss.hasPermi('reimburse:reimburse:add')")
    @Log(title = "报销记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Reimburse reimburse)
    {
        return toAjax(reimburseService.insertReimburse(reimburse));
    }

    /**
     * 修改报销记录
     */
    @PreAuthorize("@ss.hasPermi('reimburse:reimburse:edit')")
    @Log(title = "报销记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Reimburse reimburse)
    {
        return toAjax(reimburseService.updateReimburse(reimburse));
    }

    /**
     * 删除报销记录
     */
    @PreAuthorize("@ss.hasPermi('reimburse:reimburse:remove')")
    @Log(title = "报销记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(reimburseService.deleteReimburseByIds(ids));
    }
}
