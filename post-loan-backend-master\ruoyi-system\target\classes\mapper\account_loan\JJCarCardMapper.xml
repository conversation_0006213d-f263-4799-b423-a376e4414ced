<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.JJCarCardMapper">
    
    <resultMap type="JJCarCard" id="CarCardResult">
        <result property="id"    column="id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="customerId"    column="customer_id"    />
        <result property="identityNo"    column="identity_no"    />
        <result property="shape"    column="shape"    />
        <result property="plateNo"    column="plate_no"    />
        <result property="carType"    column="car_type"    />
        <result property="carOwner"    column="car_owner"    />
        <result property="address"    column="address"    />
        <result property="useNature"    column="use_nature"    />
        <result property="engineNumber"    column="engine_number"    />
        <result property="registerDate"    column="register_date"    />
        <result property="issueDate"    column="issue_date"    />
        <result property="registerOrg"    column="register_org"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectCarCardVo">
        select * from car_card
    </sql>

    <select id="selectCarCardList" parameterType="JJCarCard" resultMap="CarCardResult">
        <include refid="selectCarCardVo"/>
        <where>
            DATE(create_date) = CURDATE() or DATE(update_date) = CURDATE()
        </where>
    </select>
    
    <select id="selectCarCardById" parameterType="String" resultMap="CarCardResult">
        <include refid="selectCarCardVo"/>
        where id = #{id}
    </select>

    <insert id="insertCarCard" parameterType="JJCarCard">
        insert into car_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applyNo != null">apply_no,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="identityNo != null">identity_no,</if>
            <if test="shape != null">shape,</if>
            <if test="plateNo != null">plate_no,</if>
            <if test="carType != null">car_type,</if>
            <if test="carOwner != null">car_owner,</if>
            <if test="address != null">address,</if>
            <if test="useNature != null">use_nature,</if>
            <if test="engineNumber != null">engine_number,</if>
            <if test="registerDate != null">register_date,</if>
            <if test="issueDate != null">issue_date,</if>
            <if test="registerOrg != null">register_org,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="identityNo != null">#{identityNo},</if>
            <if test="shape != null">#{shape},</if>
            <if test="plateNo != null">#{plateNo},</if>
            <if test="carType != null">#{carType},</if>
            <if test="carOwner != null">#{carOwner},</if>
            <if test="address != null">#{address},</if>
            <if test="useNature != null">#{useNature},</if>
            <if test="engineNumber != null">#{engineNumber},</if>
            <if test="registerDate != null">#{registerDate},</if>
            <if test="issueDate != null">#{issueDate},</if>
            <if test="registerOrg != null">#{registerOrg},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateCarCard" parameterType="JJCarCard">
        update car_card
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null">apply_no = #{applyNo},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="identityNo != null">identity_no = #{identityNo},</if>
            <if test="shape != null">shape = #{shape},</if>
            <if test="plateNo != null">plate_no = #{plateNo},</if>
            <if test="carType != null">car_type = #{carType},</if>
            <if test="carOwner != null">car_owner = #{carOwner},</if>
            <if test="address != null">address = #{address},</if>
            <if test="useNature != null">use_nature = #{useNature},</if>
            <if test="engineNumber != null">engine_number = #{engineNumber},</if>
            <if test="registerDate != null">register_date = #{registerDate},</if>
            <if test="issueDate != null">issue_date = #{issueDate},</if>
            <if test="registerOrg != null">register_org = #{registerOrg},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCarCardById" parameterType="String">
        delete from car_card where id = #{id}
    </delete>

    <delete id="deleteCarCardByIds" parameterType="String">
        delete from car_card where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>