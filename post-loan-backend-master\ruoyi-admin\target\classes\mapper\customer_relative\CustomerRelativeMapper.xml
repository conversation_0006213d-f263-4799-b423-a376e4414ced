<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.customer_relative.mapper.CustomerRelativeMapper">
    
    <resultMap type="CustomerRelative" id="CustomerRelativeResult">
        <result property="id"    column="id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="customerId"    column="customer_id"    />
        <result property="relationship"    column="relationship"    />
        <result property="customerName"    column="customer_name"    />
        <result property="mobileNo"    column="mobile_no"    />
        <result property="certType"    column="cert_type"    />
        <result property="certId"    column="cert_id"    />
        <result property="workCorp"    column="work_corp"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="borough"    column="borough"    />
        <result property="address"    column="address"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="remarks"    column="remarks"    />
    </resultMap>

    <sql id="selectCustomerRelativeVo">
        select id, apply_no, customer_id, relationship, customer_name, mobile_no, cert_type, cert_id, work_corp, province, city, borough, address, create_by, create_date, update_by, update_date, remarks from customer_relative
    </sql>

    <select id="selectCustomerRelativeList" parameterType="CustomerRelative" resultMap="CustomerRelativeResult">
        <include refid="selectCustomerRelativeVo"/>
        <where>  
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="customerId != null  and customerId != ''"> and customer_id = #{customerId}</if>
            <if test="relationship != null  and relationship != ''"> and relationship = #{relationship}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="mobileNo != null  and mobileNo != ''"> and mobile_no = #{mobileNo}</if>
            <if test="certType != null  and certType != ''"> and cert_type = #{certType}</if>
            <if test="certId != null  and certId != ''"> and cert_id = #{certId}</if>
            <if test="workCorp != null  and workCorp != ''"> and work_corp = #{workCorp}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="borough != null  and borough != ''"> and borough = #{borough}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
        </where>
    </select>
    
    <select id="selectCustomerRelativeById" parameterType="String" resultMap="CustomerRelativeResult">
        <include refid="selectCustomerRelativeVo"/>
        where id = #{id}
    </select>

    <insert id="insertCustomerRelative" parameterType="CustomerRelative">
        insert into customer_relative
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applyNo != null and applyNo != ''">apply_no,</if>
            <if test="customerId != null and customerId != ''">customer_id,</if>
            <if test="relationship != null">relationship,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="mobileNo != null">mobile_no,</if>
            <if test="certType != null">cert_type,</if>
            <if test="certId != null">cert_id,</if>
            <if test="workCorp != null">work_corp,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="borough != null">borough,</if>
            <if test="address != null">address,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="remarks != null">remarks,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applyNo != null and applyNo != ''">#{applyNo},</if>
            <if test="customerId != null and customerId != ''">#{customerId},</if>
            <if test="relationship != null">#{relationship},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="mobileNo != null">#{mobileNo},</if>
            <if test="certType != null">#{certType},</if>
            <if test="certId != null">#{certId},</if>
            <if test="workCorp != null">#{workCorp},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="borough != null">#{borough},</if>
            <if test="address != null">#{address},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remarks != null">#{remarks},</if>
         </trim>
    </insert>

    <update id="updateCustomerRelative" parameterType="CustomerRelative">
        update customer_relative
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no = #{applyNo},</if>
            <if test="customerId != null and customerId != ''">customer_id = #{customerId},</if>
            <if test="relationship != null">relationship = #{relationship},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="mobileNo != null">mobile_no = #{mobileNo},</if>
            <if test="certType != null">cert_type = #{certType},</if>
            <if test="certId != null">cert_id = #{certId},</if>
            <if test="workCorp != null">work_corp = #{workCorp},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="borough != null">borough = #{borough},</if>
            <if test="address != null">address = #{address},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerRelativeById" parameterType="String">
        delete from customer_relative where id = #{id}
    </delete>

    <delete id="deleteCustomerRelativeByIds" parameterType="String">
        delete from customer_relative where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>