package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.LhTrialRepay;

/**
 * 蓝海还款试算Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
public interface LhTrialRepayMapper 
{
    /**
     * 查询蓝海还款试算
     * 
     * @param id 蓝海还款试算主键
     * @return 蓝海还款试算
     */
    public LhTrialRepay selectLhTrialRepayById(String id);

    /**
     * 查询蓝海还款试算列表
     * 
     * @param lhTrialRepay 蓝海还款试算
     * @return 蓝海还款试算集合
     */
    public List<LhTrialRepay> selectLhTrialRepayList(LhTrialRepay lhTrialRepay);

    public LhTrialRepay selectLhTrialRepayLists(LhTrialRepay lhTrialRepay);

    /**
     * 新增蓝海还款试算
     * 
     * @param lhTrialRepay 蓝海还款试算
     * @return 结果
     */
    public int insertLhTrialRepay(LhTrialRepay lhTrialRepay);

    /**
     * 修改蓝海还款试算
     * 
     * @param lhTrialRepay 蓝海还款试算
     * @return 结果
     */
    public int updateLhTrialRepay(LhTrialRepay lhTrialRepay);

    /**
     * 删除蓝海还款试算
     * 
     * @param id 蓝海还款试算主键
     * @return 结果
     */
    public int deleteLhTrialRepayById(String id);

    /**
     * 批量删除蓝海还款试算
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLhTrialRepayByIds(String[] ids);
}
