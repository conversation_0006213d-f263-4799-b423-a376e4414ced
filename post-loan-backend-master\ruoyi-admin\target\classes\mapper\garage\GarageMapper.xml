<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.garage.mapper.GarageMapper">
    
    <resultMap type="Garage" id="GarageResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="contacts"    column="contacts"    />
        <result property="mobile"    column="mobile"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="borough"    column="borough"    />
        <result property="address"    column="address"    />
        <result property="status"    column="status"    />
        <result property="lat"    column="lat"    />
        <result property="lng"    column="lng"    />
        <result property="num1"    column="num1"    />
        <result property="num2"    column="num2"    />
        <result property="num3"    column="num3"    />
        <result property="enableDate"    column="enable_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectGarageVo">
        select id, name, contacts, mobile, province, city, borough, address, status, lat, lng, num1, num2, num3, enable_date,create_by, create_date, update_by, update_date, del_flag from garage
    </sql>

    <select id="selectGarageList" parameterType="Garage" resultMap="GarageResult">
        <include refid="selectGarageVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="contacts != null  and contacts != ''"> and contacts = #{contacts}</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="borough != null  and borough != ''"> and borough = #{borough}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
            <if test="lng != null  and lng != ''"> and lng = #{lng}</if>
            <if test="num1 != null "> and num1 = #{num1}</if>
            <if test="num2 != null "> and num2 = #{num2}</if>
            <if test="num3 != null "> and num3 = #{num3}</if>
            <if test="startDate != null and endDate != null">
                AND enable_date BETWEEN #{startDate} AND #{endDate}
            </if>
        </where>
    </select>
    
    <select id="selectGarageById" parameterType="Integer" resultMap="GarageResult">
        <include refid="selectGarageVo"/>
        where id = #{id}
    </select>

    <insert id="insertGarage" parameterType="Garage" useGeneratedKeys="true" keyProperty="id">
        insert into garage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="contacts != null">contacts,</if>
            <if test="mobile != null">mobile,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="borough != null">borough,</if>
            <if test="address != null">address,</if>
            <if test="status != null">status,</if>
            <if test="lat != null">lat,</if>
            <if test="lng != null">lng,</if>
            <if test="num1 != null">num1,</if>
            <if test="num2 != null">num2,</if>
            <if test="num3 != null">num3,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="contacts != null">#{contacts},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="borough != null">#{borough},</if>
            <if test="address != null">#{address},</if>
            <if test="status != null">#{status},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lng != null">#{lng},</if>
            <if test="num1 != null">#{num1},</if>
            <if test="num2 != null">#{num2},</if>
            <if test="num3 != null">#{num3},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateGarage" parameterType="Garage">
        update garage
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="contacts != null">contacts = #{contacts},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="borough != null">borough = #{borough},</if>
            <if test="address != null">address = #{address},</if>
            <if test="status != null">status = #{status},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="num1 != null">num1 = #{num1},</if>
            <if test="num2 != null">num2 = #{num2},</if>
            <if test="num3 != null">num3 = #{num3},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGarageById" parameterType="Integer">
        delete from garage where id = #{id}
    </delete>

    <delete id="deleteGarageByIds" parameterType="String">
        delete from garage where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>