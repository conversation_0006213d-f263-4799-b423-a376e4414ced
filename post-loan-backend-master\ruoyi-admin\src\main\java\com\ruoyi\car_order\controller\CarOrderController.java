package com.ruoyi.car_order.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.zrx.image.interfaces.service.QueryService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.car_order.domain.CarOrder;
import com.ruoyi.car_order.service.ICarOrderService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

import static com.ruoyi.common.utils.DateUtils.getNowDate;

/**
 * 找车订单Controller
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/car_order/car_order")
public class CarOrderController extends BaseController
{
    @Autowired
    private ICarOrderService carOrderService;

    /**
     * 查询找车订单列表
     */
    @PreAuthorize("@ss.hasPermi('car_order:car_order:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarOrder carOrder)
    {
        startPage();
        List<CarOrder> list = carOrderService.selectCarOrderList(carOrder);
        return getDataTable(list);
    }

    /**
     * 导出找车订单列表
     */
    @PreAuthorize("@ss.hasPermi('car_order:car_order:export')")
    @Log(title = "找车订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarOrder carOrder)
    {
        List<CarOrder> list = carOrderService.selectCarOrderList(carOrder);
        ExcelUtil<CarOrder> util = new ExcelUtil<CarOrder>(CarOrder.class);
        util.exportExcel(response, list, "找车订单数据");
    }

    /**
     * 获取找车订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('car_order:car_order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(carOrderService.selectCarOrderById(id));
    }

    /**
     * 新增找车订单
     */
    @PreAuthorize("@ss.hasPermi('car_order:car_order:add')")
    @Log(title = "找车订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CarOrder carOrder)
    {
        carOrder.setKeyStatus(0L);
        carOrder.setStatus(0);
        carOrder.setCreateDate(getNowDate());
        carOrder.setAllocationTime(getNowDate());
        carOrder.setCreateBy(getUsername());
        AjaxResult result = toAjax(carOrderService.insertCarOrder(carOrder));
        return result;
    }

    /**
     * 修改找车订单
     */
    @PreAuthorize("@ss.hasPermi('car_order:car_order:edit')")
    @Log(title = "找车订单", businessType = BusinessType.UPDATE)
    @PutMapping
//    @Anonymous
    public AjaxResult edit(@RequestBody CarOrder carOrder)
    {

        return toAjax(carOrderService.updateCarOrder(carOrder));
    }

    /**
     * 删除找车订单
     */
    @PreAuthorize("@ss.hasPermi('car_order:car_order:remove')")
    @Log(title = "找车订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(carOrderService.deleteCarOrderByIds(ids));
    }
}
