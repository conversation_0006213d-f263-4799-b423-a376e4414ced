<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.assign_table.mapper.AssignTableMapper">
    
    <resultMap type="AssignTable" id="AssignTableResult">
        <result property="id"    column="id"    />
        <result property="assignUser"    column="assign_user"    />
        <result property="assignTime"    column="assign_time"    />
        <result property="status"    column="status"    />
        <result property="revokeTime"    column="revoke_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="loanId"    column="loan_id"    />
    </resultMap>

    <sql id="selectAssignTableVo">
        select id, assign_user, assign_time, status, revoke_time, create_time, update_time, loan_id from assign_table
    </sql>

    <select id="selectAssignTableList" parameterType="AssignTable" resultMap="AssignTableResult">
        <include refid="selectAssignTableVo"/>
        <where>  
            <if test="assignUser != null  and assignUser != ''"> and assign_user = #{assignUser}</if>
            <if test="assignTime != null "> and assign_time = #{assignTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="revokeTime != null "> and revoke_time = #{revokeTime}</if>
            <if test="loanId != null "> and loan_id = #{loanId}</if>
        </where>
    </select>
    
    <select id="selectAssignTableById" parameterType="Long" resultMap="AssignTableResult">
        <include refid="selectAssignTableVo"/>
        where id = #{id}
    </select>

    <insert id="insertAssignTable" parameterType="AssignTable" useGeneratedKeys="true" keyProperty="id">
        insert into assign_table
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assignUser != null and assignUser != ''">assign_user,</if>
            <if test="assignTime != null">assign_time,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="revokeTime != null">revoke_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="loanId != null">loan_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assignUser != null and assignUser != ''">#{assignUser},</if>
            <if test="assignTime != null">#{assignTime},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="revokeTime != null">#{revokeTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="loanId != null">#{loanId},</if>
         </trim>
    </insert>

    <update id="updateAssignTable" parameterType="AssignTable">
        update assign_table
        <trim prefix="SET" suffixOverrides=",">
            <if test="assignUser != null and assignUser != ''">assign_user = #{assignUser},</if>
            <if test="assignTime != null">assign_time = #{assignTime},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="revokeTime != null">revoke_time = #{revokeTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="loanId != null">loan_id = #{loanId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAssignTableById" parameterType="Long">
        delete from assign_table where id = #{id}
    </delete>

    <delete id="deleteAssignTableByIds" parameterType="String">
        delete from assign_table where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>