package com.ruoyi.installment_application.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.installment_application.mapper.InstallmentApplicationMapper;
import com.ruoyi.installment_application.domain.InstallmentApplication;
import com.ruoyi.installment_application.service.IInstallmentApplicationService;

/**
 * 分期申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class InstallmentApplicationServiceImpl implements IInstallmentApplicationService 
{
    @Autowired
    private InstallmentApplicationMapper installmentApplicationMapper;

    /**
     * 查询分期申请
     * 
     * @param id 分期申请主键
     * @return 分期申请
     */
    @Override
    public InstallmentApplication selectInstallmentApplicationById(Long id)
    {
        return installmentApplicationMapper.selectInstallmentApplicationById(id);
    }

    /**
     * 查询分期申请列表
     * 
     * @param installmentApplication 分期申请
     * @return 分期申请
     */
    @Override
    public List<InstallmentApplication> selectInstallmentApplicationList(InstallmentApplication installmentApplication)
    {
        return installmentApplicationMapper.selectInstallmentApplicationList(installmentApplication);
    }

    /**
     * 新增分期申请
     * 
     * @param installmentApplication 分期申请
     * @return 结果
     */
    @Override
    public int insertInstallmentApplication(InstallmentApplication installmentApplication)
    {
        return installmentApplicationMapper.insertInstallmentApplication(installmentApplication);
    }

    /**
     * 修改分期申请
     * 
     * @param installmentApplication 分期申请
     * @return 结果
     */
    @Override
    public int updateInstallmentApplication(InstallmentApplication installmentApplication)
    {
        return installmentApplicationMapper.updateInstallmentApplication(installmentApplication);
    }

    /**
     * 批量删除分期申请
     * 
     * @param ids 需要删除的分期申请主键
     * @return 结果
     */
    @Override
    public int deleteInstallmentApplicationByIds(Long[] ids)
    {
        return installmentApplicationMapper.deleteInstallmentApplicationByIds(ids);
    }

    /**
     * 删除分期申请信息
     * 
     * @param id 分期申请主键
     * @return 结果
     */
    @Override
    public int deleteInstallmentApplicationById(Long id)
    {
        return installmentApplicationMapper.deleteInstallmentApplicationById(id);
    }
}
