package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.ZgcTrialRepay;
import com.ruoyi.system.service.IZgcTrialRepayService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 中关村还款试算Controller
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@RestController
@RequestMapping("/system/zgc")
public class ZgcTrialRepayController extends BaseController
{
    @Autowired
    private IZgcTrialRepayService zgcTrialRepayService;

    /**
     * 查询中关村还款试算列表
     */
//    @PreAuthorize("@ss.hasPermi('system:repay:list')")
    @GetMapping("/list")
    public TableDataInfo list(ZgcTrialRepay zgcTrialRepay)
    {
        startPage();
        List<ZgcTrialRepay> list = zgcTrialRepayService.selectZgcTrialRepayList(zgcTrialRepay);
        return getDataTable(list);
    }

    /**
     * 导出中关村还款试算列表
     */
    @PreAuthorize("@ss.hasPermi('system:repay:export')")
    @Log(title = "中关村还款试算", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZgcTrialRepay zgcTrialRepay)
    {
        List<ZgcTrialRepay> list = zgcTrialRepayService.selectZgcTrialRepayList(zgcTrialRepay);
        ExcelUtil<ZgcTrialRepay> util = new ExcelUtil<ZgcTrialRepay>(ZgcTrialRepay.class);
        util.exportExcel(response, list, "中关村还款试算数据");
    }

    /**
     * 获取中关村还款试算详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:repay:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(zgcTrialRepayService.selectZgcTrialRepayById(id));
    }

    /**
     * 新增中关村还款试算
     */
    @PreAuthorize("@ss.hasPermi('system:repay:add')")
    @Log(title = "中关村还款试算", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZgcTrialRepay zgcTrialRepay)
    {
        return toAjax(zgcTrialRepayService.insertZgcTrialRepay(zgcTrialRepay));
    }

    /**
     * 修改中关村还款试算
     */
    @PreAuthorize("@ss.hasPermi('system:repay:edit')")
    @Log(title = "中关村还款试算", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZgcTrialRepay zgcTrialRepay)
    {
        return toAjax(zgcTrialRepayService.updateZgcTrialRepay(zgcTrialRepay));
    }

    /**
     * 删除中关村还款试算
     */
    @PreAuthorize("@ss.hasPermi('system:repay:remove')")
    @Log(title = "中关村还款试算", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(zgcTrialRepayService.deleteZgcTrialRepayByIds(ids));
    }
}
