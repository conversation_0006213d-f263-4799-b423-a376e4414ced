<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vw_customer_info.mapper.VwCustomerInfoMapper">

    <resultMap type="VwCustomerInfo" id="VwCustomerInfoResult">
        <result property="id"    column="id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="certId"    column="cert_id"    />
        <result property="address"    column="address"    />
        <result property="docAddress"    column="doc_address"    />
        <result property="docDetailAddress"    column="doc_detail_address"    />
        <result property="docProvince"    column="doc_province"    />
        <result property="docCity"    column="doc_city"    />
        <result property="docBorough"    column="doc_borough"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="borough"    column="borough"    />
        <result property="liveAddress"    column="live_address"    />
        <result property="detailAddress"    column="detail_address"    />
        <result property="censusProvince"    column="census_province"    />
        <result property="censusCity"    column="census_city"    />
        <result property="censusArea"    column="census_area"    />
        <result property="censusAddress"    column="census_address"    />
    </resultMap>

    <sql id="selectVwCustomerInfoVo">
        select id, customer_name, cert_id, address, doc_address, doc_detail_address, doc_province, doc_city, doc_borough, mobile_phone, province, city, borough, live_address, detail_address, census_province, census_city, census_area, census_address from vw_customer_info
    </sql>

    <select id="selectVwCustomerInfoList" parameterType="VwCustomerInfo" resultMap="VwCustomerInfoResult">
        <include refid="selectVwCustomerInfoVo"/>
        <where>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="certId != null  and certId != ''"> and cert_id = #{certId}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="docAddress != null  and docAddress != ''"> and doc_address = #{docAddress}</if>
            <if test="docDetailAddress != null  and docDetailAddress != ''"> and doc_detail_address = #{docDetailAddress}</if>
            <if test="docProvince != null  and docProvince != ''"> and doc_province = #{docProvince}</if>
            <if test="docCity != null  and docCity != ''"> and doc_city = #{docCity}</if>
            <if test="docBorough != null  and docBorough != ''"> and doc_borough = #{docBorough}</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone = #{mobilePhone}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="borough != null  and borough != ''"> and borough = #{borough}</if>
            <if test="liveAddress != null  and liveAddress != ''"> and live_address = #{liveAddress}</if>
            <if test="detailAddress != null  and detailAddress != ''"> and detail_address = #{detailAddress}</if>
            <if test="censusProvince != null  and censusProvince != ''"> and census_province = #{censusProvince}</if>
            <if test="censusCity != null  and censusCity != ''"> and census_city = #{censusCity}</if>
            <if test="censusArea != null  and censusArea != ''"> and census_area = #{censusArea}</if>
            <if test="censusAddress != null  and censusAddress != ''"> and census_address = #{censusAddress}</if>
        </where>
    </select>

    <select id="selectVwCustomerInfoById" parameterType="String" resultMap="VwCustomerInfoResult">
        <include refid="selectVwCustomerInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertVwCustomerInfo" parameterType="VwCustomerInfo">
        insert into vw_customer_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="certId != null">cert_id,</if>
            <if test="address != null">address,</if>
            <if test="docAddress != null">doc_address,</if>
            <if test="docDetailAddress != null">doc_detail_address,</if>
            <if test="docProvince != null">doc_province,</if>
            <if test="docCity != null">doc_city,</if>
            <if test="docBorough != null">doc_borough,</if>
            <if test="mobilePhone != null">mobile_phone,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="borough != null">borough,</if>
            <if test="liveAddress != null">live_address,</if>
            <if test="detailAddress != null">detail_address,</if>
            <if test="censusProvince != null">census_province,</if>
            <if test="censusCity != null">census_city,</if>
            <if test="censusArea != null">census_area,</if>
            <if test="censusAddress != null">census_address,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="certId != null">#{certId},</if>
            <if test="address != null">#{address},</if>
            <if test="docAddress != null">#{docAddress},</if>
            <if test="docDetailAddress != null">#{docDetailAddress},</if>
            <if test="docProvince != null">#{docProvince},</if>
            <if test="docCity != null">#{docCity},</if>
            <if test="docBorough != null">#{docBorough},</if>
            <if test="mobilePhone != null">#{mobilePhone},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="borough != null">#{borough},</if>
            <if test="liveAddress != null">#{liveAddress},</if>
            <if test="detailAddress != null">#{detailAddress},</if>
            <if test="censusProvince != null">#{censusProvince},</if>
            <if test="censusCity != null">#{censusCity},</if>
            <if test="censusArea != null">#{censusArea},</if>
            <if test="censusAddress != null">#{censusAddress},</if>
        </trim>
    </insert>

    <update id="updateVwCustomerInfo" parameterType="VwCustomerInfo">
        update vw_customer_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="certId != null">cert_id = #{certId},</if>
            <if test="address != null">address = #{address},</if>
            <if test="docAddress != null">doc_address = #{docAddress},</if>
            <if test="docDetailAddress != null">doc_detail_address = #{docDetailAddress},</if>
            <if test="docProvince != null">doc_province = #{docProvince},</if>
            <if test="docCity != null">doc_city = #{docCity},</if>
            <if test="docBorough != null">doc_borough = #{docBorough},</if>
            <if test="mobilePhone != null">mobile_phone = #{mobilePhone},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="borough != null">borough = #{borough},</if>
            <if test="liveAddress != null">live_address = #{liveAddress},</if>
            <if test="detailAddress != null">detail_address = #{detailAddress},</if>
            <if test="censusProvince != null">census_province = #{censusProvince},</if>
            <if test="censusCity != null">census_city = #{censusCity},</if>
            <if test="censusArea != null">census_area = #{censusArea},</if>
            <if test="censusAddress != null">census_address = #{censusAddress},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVwCustomerInfoById" parameterType="String">
        delete from vw_customer_info where id = #{id}
    </delete>

    <delete id="deleteVwCustomerInfoByIds" parameterType="String">
        delete from vw_customer_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>