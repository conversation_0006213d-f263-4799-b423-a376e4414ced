<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zgc_repay_plan.mapper.ZgcRepayPlanMapper">
    
    <resultMap type="ZgcRepayPlan" id="ZgcRepayPlanResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="period"    column="period"    />
        <result property="repayDate"    column="repay_date"    />
        <result property="actualRepayDate"    column="actual_repay_date"    />
        <result property="repayAmount"    column="repay_amount"    />
        <result property="capital"    column="capital"    />
        <result property="actualCapital"    column="actual_capital"    />
        <result property="interest"    column="interest"    />
        <result property="actualInterest"    column="actual_interest"    />
        <result property="defInterest"    column="def_interest"    />
        <result property="actualDefInterest"    column="actual_def_interest"    />
        <result property="balance"    column="balance"    />
        <result property="payType"    column="pay_type"    />
        <result property="status"    column="status"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
    </resultMap>

    <sql id="selectZgcRepayPlanVo">
        select id, apply_id, period, repay_date, actual_repay_date, repay_amount, capital, actual_capital, interest, actual_interest, def_interest, actual_def_interest, balance, pay_type, status, create_date, update_date from zgc_repay_plan
    </sql>

    <select id="selectZgcRepayPlanList" parameterType="ZgcRepayPlan" resultMap="ZgcRepayPlanResult">
        <include refid="selectZgcRepayPlanVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="period != null  and period != ''"> and period = #{period}</if>
            <if test="repayDate != null  and repayDate != ''"> and repay_date = #{repayDate}</if>
            <if test="actualRepayDate != null  and actualRepayDate != ''"> and actual_repay_date = #{actualRepayDate}</if>
            <if test="repayAmount != null  and repayAmount != ''"> and repay_amount = #{repayAmount}</if>
            <if test="capital != null  and capital != ''"> and capital = #{capital}</if>
            <if test="actualCapital != null  and actualCapital != ''"> and actual_capital = #{actualCapital}</if>
            <if test="interest != null  and interest != ''"> and interest = #{interest}</if>
            <if test="actualInterest != null  and actualInterest != ''"> and actual_interest = #{actualInterest}</if>
            <if test="defInterest != null  and defInterest != ''"> and def_interest = #{defInterest}</if>
            <if test="actualDefInterest != null  and actualDefInterest != ''"> and actual_def_interest = #{actualDefInterest}</if>
            <if test="balance != null  and balance != ''"> and balance = #{balance}</if>
            <if test="payType != null  and payType != ''"> and pay_type = #{payType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
    </select>
    
    <select id="selectZgcRepayPlanById" parameterType="String" resultMap="ZgcRepayPlanResult">
        <include refid="selectZgcRepayPlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertZgcRepayPlan" parameterType="ZgcRepayPlan">
        insert into zgc_repay_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applyId != null">apply_id,</if>
            <if test="period != null">period,</if>
            <if test="repayDate != null">repay_date,</if>
            <if test="actualRepayDate != null">actual_repay_date,</if>
            <if test="repayAmount != null">repay_amount,</if>
            <if test="capital != null">capital,</if>
            <if test="actualCapital != null">actual_capital,</if>
            <if test="interest != null">interest,</if>
            <if test="actualInterest != null">actual_interest,</if>
            <if test="defInterest != null">def_interest,</if>
            <if test="actualDefInterest != null">actual_def_interest,</if>
            <if test="balance != null">balance,</if>
            <if test="payType != null">pay_type,</if>
            <if test="status != null">status,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateDate != null">update_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applyId != null">#{applyId},</if>
            <if test="period != null">#{period},</if>
            <if test="repayDate != null">#{repayDate},</if>
            <if test="actualRepayDate != null">#{actualRepayDate},</if>
            <if test="repayAmount != null">#{repayAmount},</if>
            <if test="capital != null">#{capital},</if>
            <if test="actualCapital != null">#{actualCapital},</if>
            <if test="interest != null">#{interest},</if>
            <if test="actualInterest != null">#{actualInterest},</if>
            <if test="defInterest != null">#{defInterest},</if>
            <if test="actualDefInterest != null">#{actualDefInterest},</if>
            <if test="balance != null">#{balance},</if>
            <if test="payType != null">#{payType},</if>
            <if test="status != null">#{status},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateDate != null">#{updateDate},</if>
         </trim>
    </insert>

    <update id="updateZgcRepayPlan" parameterType="ZgcRepayPlan">
        update zgc_repay_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyId != null">apply_id = #{applyId},</if>
            <if test="period != null">period = #{period},</if>
            <if test="repayDate != null">repay_date = #{repayDate},</if>
            <if test="actualRepayDate != null">actual_repay_date = #{actualRepayDate},</if>
            <if test="repayAmount != null">repay_amount = #{repayAmount},</if>
            <if test="capital != null">capital = #{capital},</if>
            <if test="actualCapital != null">actual_capital = #{actualCapital},</if>
            <if test="interest != null">interest = #{interest},</if>
            <if test="actualInterest != null">actual_interest = #{actualInterest},</if>
            <if test="defInterest != null">def_interest = #{defInterest},</if>
            <if test="actualDefInterest != null">actual_def_interest = #{actualDefInterest},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZgcRepayPlanById" parameterType="String">
        delete from zgc_repay_plan where id = #{id}
    </delete>

    <delete id="deleteZgcRepayPlanByIds" parameterType="String">
        delete from zgc_repay_plan where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>