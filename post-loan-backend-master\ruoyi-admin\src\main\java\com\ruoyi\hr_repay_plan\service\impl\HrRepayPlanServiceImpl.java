package com.ruoyi.hr_repay_plan.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.hr_repay_plan.mapper.HrRepayPlanMapper;
import com.ruoyi.hr_repay_plan.domain.HrRepayPlan;
import com.ruoyi.hr_repay_plan.service.IHrRepayPlanService;

/**
 * 华瑞还款计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class HrRepayPlanServiceImpl implements IHrRepayPlanService 
{
    @Autowired
    private HrRepayPlanMapper hrRepayPlanMapper;

    /**
     * 查询华瑞还款计划
     * 
     * @param id 华瑞还款计划主键
     * @return 华瑞还款计划
     */
    @Override
    public HrRepayPlan selectHrRepayPlanById(String id)
    {
        return hrRepayPlanMapper.selectHrRepayPlanById(id);
    }

    /**
     * 查询华瑞还款计划列表
     * 
     * @param hrRepayPlan 华瑞还款计划
     * @return 华瑞还款计划
     */
    @Override
    public List<HrRepayPlan> selectHrRepayPlanList(HrRepayPlan hrRepayPlan)
    {
        return hrRepayPlanMapper.selectHrRepayPlanList(hrRepayPlan);
    }

    /**
     * 新增华瑞还款计划
     * 
     * @param hrRepayPlan 华瑞还款计划
     * @return 结果
     */
    @Override
    public int insertHrRepayPlan(HrRepayPlan hrRepayPlan)
    {
        return hrRepayPlanMapper.insertHrRepayPlan(hrRepayPlan);
    }

    /**
     * 修改华瑞还款计划
     * 
     * @param hrRepayPlan 华瑞还款计划
     * @return 结果
     */
    @Override
    public int updateHrRepayPlan(HrRepayPlan hrRepayPlan)
    {
        return hrRepayPlanMapper.updateHrRepayPlan(hrRepayPlan);
    }

    /**
     * 批量删除华瑞还款计划
     * 
     * @param ids 需要删除的华瑞还款计划主键
     * @return 结果
     */
    @Override
    public int deleteHrRepayPlanByIds(String[] ids)
    {
        return hrRepayPlanMapper.deleteHrRepayPlanByIds(ids);
    }

    /**
     * 删除华瑞还款计划信息
     * 
     * @param id 华瑞还款计划主键
     * @return 结果
     */
    @Override
    public int deleteHrRepayPlanById(String id)
    {
        return hrRepayPlanMapper.deleteHrRepayPlanById(id);
    }
}
