package com.ruoyi.loan_settle.mapper;

import java.util.List;
import com.ruoyi.loan_settle.domain.LoanSettle;

/**
 * 流程结清Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface LoanSettleMapper 
{
    /**
     * 查询流程结清
     * 
     * @param id 流程结清主键
     * @return 流程结清
     */
    public LoanSettle selectLoanSettleById(String id);

    /**
     * 查询流程结清列表
     * 
     * @param loanSettle 流程结清
     * @return 流程结清集合
     */
    public List<LoanSettle> selectLoanSettleList(LoanSettle loanSettle);

    public LoanSettle selectLoanSettleListDetail(LoanSettle loanSettle);

    /**
     * 新增流程结清
     * 
     * @param loanSettle 流程结清
     * @return 结果
     */
    public int insertLoanSettle(LoanSettle loanSettle);

    /**
     * 修改流程结清
     * 
     * @param loanSettle 流程结清
     * @return 结果
     */
    public int updateLoanSettle(LoanSettle loanSettle);

    /**
     * 删除流程结清
     * 
     * @param id 流程结清主键
     * @return 结果
     */
    public int deleteLoanSettleById(String id);

    /**
     * 批量删除流程结清
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLoanSettleByIds(String[] ids);
}
