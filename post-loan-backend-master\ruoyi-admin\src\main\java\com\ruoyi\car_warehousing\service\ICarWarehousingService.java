package com.ruoyi.car_warehousing.service;

import java.util.List;
import com.ruoyi.car_warehousing.domain.CarWarehousing;

/**
 * 车辆出入库Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface ICarWarehousingService {
    /**
     * 查询车辆出入库
     * 
     * @param id 车辆出入库主键
     * @return 车辆出入库
     */
    public CarWarehousing selectCarWarehousingById(String id);

    /**
     * 查询车辆出入库列表
     * 
     * @param carWarehousing 车辆出入库
     * @return 车辆出入库集合
     */
    public List<CarWarehousing> selectCarWarehousingList(CarWarehousing carWarehousing);

    /**
     * 新增车辆出入库
     * 
     * @param carWarehousing 车辆出入库
     * @return 结果
     */
    public int insertCarWarehousing(CarWarehousing carWarehousing);

    /**
     * 修改车辆出入库
     * 
     * @param carWarehousing 车辆出入库
     * @return 结果
     */
    public int updateCarWarehousing(CarWarehousing carWarehousing);

    /**
     * 批量删除车辆出入库
     * 
     * @param ids 需要删除的车辆出入库主键集合
     * @return 结果
     */
    public int deleteCarWarehousingByIds(String[] ids);

    /**
     * 删除车辆出入库信息
     * 
     * @param id 车辆出入库主键
     * @return 结果
     */
    public int deleteCarWarehousingById(String id);
}
