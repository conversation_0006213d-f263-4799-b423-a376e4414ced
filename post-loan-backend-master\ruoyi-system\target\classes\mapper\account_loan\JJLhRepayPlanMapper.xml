<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.JJLhRepayPlanMapper">
    
    <resultMap type="JJLhRepayPlan" id="LhRepayPlanResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="loanNo"    column="loan_no"    />
        <result property="period"    column="period"    />
        <result property="repayDate"    column="repay_date"    />
        <result property="actualRepayDate"    column="actual_repay_date"    />
        <result property="repayAmount"    column="repay_amount"    />
        <result property="lhRepayAmount"    column="lh_repay_amount"    />
        <result property="guaranteeFee"    column="guarantee_fee"    />
        <result property="lhEarnings"    column="lh_earnings"    />
        <result property="gmEarnings"    column="gm_earnings"    />
        <result property="capital"    column="capital"    />
        <result property="actualCapital"    column="actual_capital"    />
        <result property="interest"    column="interest"    />
        <result property="actualInterest"    column="actual_interest"    />
        <result property="defInterest"    column="def_interest"    />
        <result property="compoundInterest"    column="compound_interest"    />
        <result property="balance"    column="balance"    />
        <result property="overdueDays"    column="overdue_days"    />
        <result property="status"    column="status"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="flag"    column="flag"    />
    </resultMap>

    <sql id="selectLhRepayPlanVo">
        select * from lh_repay_plan
    </sql>

    <select id="selectLhRepayPlanList" parameterType="JJLhRepayPlan" resultMap="LhRepayPlanResult">
        <include refid="selectLhRepayPlanVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="loanNo != null  and loanNo != ''"> and loan_no = #{loanNo}</if>
            <if test="period != null  and period != ''"> and period = #{period}</if>
            <if test="repayDate != null  and repayDate != ''"> and repay_date = #{repayDate}</if>
            <if test="actualRepayDate != null  and actualRepayDate != ''"> and actual_repay_date = #{actualRepayDate}</if>
            <if test="repayAmount != null  and repayAmount != ''"> and repay_amount = #{repayAmount}</if>
            <if test="lhRepayAmount != null  and lhRepayAmount != ''"> and lh_repay_amount = #{lhRepayAmount}</if>
            <if test="guaranteeFee != null  and guaranteeFee != ''"> and guarantee_fee = #{guaranteeFee}</if>
            <if test="lhEarnings != null  and lhEarnings != ''"> and lh_earnings = #{lhEarnings}</if>
            <if test="gmEarnings != null  and gmEarnings != ''"> and gm_earnings = #{gmEarnings}</if>
            <if test="capital != null  and capital != ''"> and capital = #{capital}</if>
            <if test="actualCapital != null  and actualCapital != ''"> and actual_capital = #{actualCapital}</if>
            <if test="interest != null  and interest != ''"> and interest = #{interest}</if>
            <if test="actualInterest != null  and actualInterest != ''"> and actual_interest = #{actualInterest}</if>
            <if test="defInterest != null  and defInterest != ''"> and def_interest = #{defInterest}</if>
            <if test="compoundInterest != null  and compoundInterest != ''"> and compound_interest = #{compoundInterest}</if>
            <if test="balance != null  and balance != ''"> and balance = #{balance}</if>
            <if test="overdueDays != null  and overdueDays != ''"> and overdue_days = #{overdueDays}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="flag != null  and flag != ''"> and flag = #{flag}</if>
        </where>
    </select>

    <select id="selectLhRepayPlanListGroup" parameterType="JJLhRepayPlan" resultMap="LhRepayPlanResult">
        <include refid="selectLhRepayPlanVo"/>
        <where>
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="loanNo != null  and loanNo != ''"> and loan_no = #{loanNo}</if>
            <if test="period != null  and period != ''"> and period = #{period}</if>
            <if test="repayDate != null  and repayDate != ''"> and repay_date = #{repayDate}</if>
            <if test="actualRepayDate != null  and actualRepayDate != ''"> and actual_repay_date = #{actualRepayDate}</if>
            <if test="repayAmount != null  and repayAmount != ''"> and repay_amount = #{repayAmount}</if>
            <if test="lhRepayAmount != null  and lhRepayAmount != ''"> and lh_repay_amount = #{lhRepayAmount}</if>
            <if test="guaranteeFee != null  and guaranteeFee != ''"> and guarantee_fee = #{guaranteeFee}</if>
            <if test="lhEarnings != null  and lhEarnings != ''"> and lh_earnings = #{lhEarnings}</if>
            <if test="gmEarnings != null  and gmEarnings != ''"> and gm_earnings = #{gmEarnings}</if>
            <if test="capital != null  and capital != ''"> and capital = #{capital}</if>
            <if test="actualCapital != null  and actualCapital != ''"> and actual_capital = #{actualCapital}</if>
            <if test="interest != null  and interest != ''"> and interest = #{interest}</if>
            <if test="actualInterest != null  and actualInterest != ''"> and actual_interest = #{actualInterest}</if>
            <if test="defInterest != null  and defInterest != ''"> and def_interest = #{defInterest}</if>
            <if test="compoundInterest != null  and compoundInterest != ''"> and compound_interest = #{compoundInterest}</if>
            <if test="balance != null  and balance != ''"> and balance = #{balance}</if>
            <if test="overdueDays != null  and overdueDays != ''"> and overdue_days = #{overdueDays}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="flag != null  and flag != ''"> and flag = #{flag}</if>
        </where>
        group by apply_id
    </select>

    <select id="selectLhRepayPlanListNow" parameterType="String" resultMap="LhRepayPlanResult">
        select * from lh_repay_plan where apply_id = #{applyId} and  YEAR(repay_date) = YEAR(CURDATE())  and MONTH(repay_date) = MONTH(CURDATE()) limit 1

    </select>

</mapper>