package com.ruoyi.car_team.service;

import java.util.List;
import com.ruoyi.car_team.domain.GarageTeam;

/**
 * 团队管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface IGarageTeamService 
{
    /**
     * 查询团队管理
     * 
     * @param id 团队管理主键
     * @return 团队管理
     */
    public GarageTeam selectGarageTeamById(Long id);

    /**
     * 查询团队管理列表
     * 
     * @param garageTeam 团队管理
     * @return 团队管理集合
     */
    public List<GarageTeam> selectGarageTeamList(GarageTeam garageTeam);

    /**
     * 新增团队管理
     * 
     * @param garageTeam 团队管理
     * @return 结果
     */
    public int insertGarageTeam(GarageTeam garageTeam);

    /**
     * 修改团队管理
     * 
     * @param garageTeam 团队管理
     * @return 结果
     */
    public int updateGarageTeam(GarageTeam garageTeam);

    /**
     * 批量删除团队管理
     * 
     * @param ids 需要删除的团队管理主键集合
     * @return 结果
     */
    public int deleteGarageTeamByIds(Long[] ids);

    /**
     * 删除团队管理信息
     * 
     * @param id 团队管理主键
     * @return 结果
     */
    public int deleteGarageTeamById(Long id);
}
