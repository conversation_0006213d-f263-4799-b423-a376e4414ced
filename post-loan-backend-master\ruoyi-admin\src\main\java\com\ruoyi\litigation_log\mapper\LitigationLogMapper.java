package com.ruoyi.litigation_log.mapper;

import java.util.List;
import com.ruoyi.litigation_log.domain.LitigationLog;

/**
 * 法诉日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface LitigationLogMapper 
{
    /**
     * 查询法诉日志
     * 
     * @param id 法诉日志主键
     * @return 法诉日志
     */
    public LitigationLog selectLitigationLogById(Long id);

    /**
     * 查询法诉日志列表
     * 
     * @param litigationLog 法诉日志
     * @return 法诉日志集合
     */
    public List<LitigationLog> selectLitigationLogList(LitigationLog litigationLog);

    /**
     * 新增法诉日志
     * 
     * @param litigationLog 法诉日志
     * @return 结果
     */
    public int insertLitigationLog(LitigationLog litigationLog);

    /**
     * 修改法诉日志
     * 
     * @param litigationLog 法诉日志
     * @return 结果
     */
    public int updateLitigationLog(LitigationLog litigationLog);

    /**
     * 删除法诉日志
     * 
     * @param id 法诉日志主键
     * @return 结果
     */
    public int deleteLitigationLogById(Long id);

    /**
     * 批量删除法诉日志
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLitigationLogByIds(Long[] ids);

    /**
     * 查询某法诉案件的最新一条催记记录
     * @param litigationId 法诉案件ID
     * @return 最新的法诉日志
     */
    public LitigationLog selectLatestLitigationLogByLitigationId(Long litigationId);
}
