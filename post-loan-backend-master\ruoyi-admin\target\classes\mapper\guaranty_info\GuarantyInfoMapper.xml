<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.guaranty_info.mapper.GuarantyInfoMapper">
    
    <resultMap type="GuarantyInfo" id="GuarantyInfoResult">
        <result property="id"    column="id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="guarantyWay"    column="guaranty_way"    />
        <result property="guarantyDate"    column="guaranty_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="expressNumber"    column="express_number"    />
        <result property="expressDate"    column="express_date"    />
        <result property="expressCompany"    column="express_company"    />
        <result property="result"    column="result"    />
        <result property="releaseAgentId"    column="release_agent_id"    />
    </resultMap>

    <sql id="selectGuarantyInfoVo">
        select id, apply_no, guaranty_way, guaranty_date, create_by, create_date, update_by, update_date, del_flag, express_number, express_date, express_company, result, release_agent_id from guaranty_info
    </sql>

    <select id="selectGuarantyInfoList" parameterType="GuarantyInfo" resultMap="GuarantyInfoResult">
        <include refid="selectGuarantyInfoVo"/>
        <where>  
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="guarantyWay != null  and guarantyWay != ''"> and guaranty_way = #{guarantyWay}</if>
            <if test="guarantyDate != null "> and guaranty_date = #{guarantyDate}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="expressNumber != null  and expressNumber != ''"> and express_number = #{expressNumber}</if>
            <if test="expressDate != null  and expressDate != ''"> and express_date = #{expressDate}</if>
            <if test="expressCompany != null  and expressCompany != ''"> and express_company = #{expressCompany}</if>
            <if test="result != null  and result != ''"> and result = #{result}</if>
            <if test="releaseAgentId != null  and releaseAgentId != ''"> and release_agent_id = #{releaseAgentId}</if>
        </where>
    </select>
    
    <select id="selectGuarantyInfoById" parameterType="String" resultMap="GuarantyInfoResult">
       select * from guaranty_info  where apply_no = #{id} and del_flag='0'
    </select>

    <insert id="insertGuarantyInfo" parameterType="GuarantyInfo">
        insert into guaranty_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applyNo != null and applyNo != ''">apply_no,</if>
            <if test="guarantyWay != null">guaranty_way,</if>
            <if test="guarantyDate != null">guaranty_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="expressNumber != null">express_number,</if>
            <if test="expressDate != null">express_date,</if>
            <if test="expressCompany != null">express_company,</if>
            <if test="result != null">result,</if>
            <if test="releaseAgentId != null">release_agent_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applyNo != null and applyNo != ''">#{applyNo},</if>
            <if test="guarantyWay != null">#{guarantyWay},</if>
            <if test="guarantyDate != null">#{guarantyDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="expressNumber != null">#{expressNumber},</if>
            <if test="expressDate != null">#{expressDate},</if>
            <if test="expressCompany != null">#{expressCompany},</if>
            <if test="result != null">#{result},</if>
            <if test="releaseAgentId != null">#{releaseAgentId},</if>
         </trim>
    </insert>

    <update id="updateGuarantyInfo" parameterType="GuarantyInfo">
        update guaranty_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no = #{applyNo},</if>
            <if test="guarantyWay != null">guaranty_way = #{guarantyWay},</if>
            <if test="guarantyDate != null">guaranty_date = #{guarantyDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="expressNumber != null">express_number = #{expressNumber},</if>
            <if test="expressDate != null">express_date = #{expressDate},</if>
            <if test="expressCompany != null">express_company = #{expressCompany},</if>
            <if test="result != null">result = #{result},</if>
            <if test="releaseAgentId != null">release_agent_id = #{releaseAgentId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGuarantyInfoById" parameterType="String">
        delete from guaranty_info where id = #{id}
    </delete>

    <delete id="deleteGuarantyInfoByIds" parameterType="String">
        delete from guaranty_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>