package com.ruoyi.loan_compensation.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ruoyi.account_loan.service.IAccountLoanService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.loan_compensation.mapper.LoanCompensationMapper;
import com.ruoyi.loan_compensation.domain.LoanCompensation;
import com.ruoyi.loan_compensation.service.ILoanCompensationService;

/**
 * 代偿Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
public class LoanCompensationServiceImpl implements ILoanCompensationService {
    private static final Logger log = LoggerFactory.getLogger(LoanCompensationServiceImpl.class);

    @Autowired
    private LoanCompensationMapper loanCompensationMapper;

    @Autowired
    private IAccountLoanService accountLoanService;

    /**
     * 查询代偿
     * 
     * @param id 代偿主键
     * @return 代偿
     */
    @Override
    public LoanCompensation selectLoanCompensationById(String id) {
        return loanCompensationMapper.selectLoanCompensationById(id);
    }

    @Override
    public LoanCompensation selectLoanCompensationByIds(String id) {
        return loanCompensationMapper.selectLoanCompensationByIds(id);
    }

    /**
     * 查询代偿列表
     * 
     * @param loanCompensation 代偿
     * @return 代偿
     */
    @Override
    public List<LoanCompensation> selectLoanCompensationList(LoanCompensation loanCompensation) {
        return loanCompensationMapper.selectLoanCompensationList(loanCompensation);
    }

    /**
     * 新增代偿
     * 
     * @param loanCompensation 代偿
     * @return 结果
     */
    @Override
    public int insertLoanCompensation(LoanCompensation loanCompensation) {
        return loanCompensationMapper.insertLoanCompensation(loanCompensation);
    }

    /**
     * 修改代偿
     * 
     * @param loanCompensation 代偿
     * @return 结果
     */
    @Override
    public int updateLoanCompensation(LoanCompensation loanCompensation) {
        return loanCompensationMapper.updateLoanCompensation(loanCompensation);
    }

    /**
     * 批量删除代偿
     * 
     * @param ids 需要删除的代偿主键
     * @return 结果
     */
    @Override
    public int deleteLoanCompensationByIds(String[] ids) {
        return loanCompensationMapper.deleteLoanCompensationByIds(ids);
    }

    /**
     * 删除代偿信息
     * 
     * @param id 代偿主键
     * @return 结果
     */
    @Override
    public int deleteLoanCompensationById(String id) {
        return loanCompensationMapper.deleteLoanCompensationById(id);
    }

    /**
     * 获取贷款的贷后还款状态
     * 
     * @param loanId 贷款ID
     * @return 贷后还款状态
     */
    @Override
    public Integer getAccountLoanRepaymentStatus(Long loanId) {
        return accountLoanService.getRepaymentStatusByLoanId(loanId);
    }

    /**
     * 更新贷款的贷后还款状态
     * 
     * @param loanId          贷款ID
     * @param repaymentStatus 贷后还款状态
     * @return 结果
     */
    @Override
    public int updateAccountLoanRepaymentStatus(Long loanId, Integer repaymentStatus) {
        return accountLoanService.updateRepaymentStatus(loanId, repaymentStatus);
    }

    /**
     * 发起代偿申请（包含状态验证）
     * 
     * @param loanCompensation 代偿信息
     * @return 结果
     */
    @Override
    public AjaxResult initiateCompensation(LoanCompensation loanCompensation) {
        // 参数校验
        if (loanCompensation == null) {
            return AjaxResult.error("代偿信息不能为空");
        }

        if (loanCompensation.getLoanId() == null) {
            return AjaxResult.error("贷款ID不能为空");
        }

        // 先获取当前状态进行日志记录
        Integer currentStatus = getAccountLoanRepaymentStatus(loanCompensation.getLoanId());
        log.info("发起代偿前，贷款ID: {}, 当前贷后还款状态: {}", loanCompensation.getLoanId(), currentStatus);

        // 直接检查状态，只有6-逾期未还款或7-逾期还款中，才能更新为8-代偿未还款
        if (currentStatus == null) {
            log.error("无法获取贷款状态，贷款ID: {}", loanCompensation.getLoanId());
            return AjaxResult.error("无法获取贷款状态");
        }

        if (currentStatus != 6 && currentStatus != 7) {
            log.warn("未满足代偿条件，贷款ID: {}, 当前状态: {}, 需要状态: 6或7",
                    loanCompensation.getLoanId(), currentStatus);
            return AjaxResult.error("未满足代偿条件，当前状态不是逾期未还款或逾期还款中");
        }

        // 直接更新状态为8-代偿未还款
        int updateResult = updateAccountLoanRepaymentStatus(loanCompensation.getLoanId(), 8);
        if (updateResult <= 0) {
            log.error("更新贷后还款状态失败，贷款ID: {}", loanCompensation.getLoanId());
            return AjaxResult.error("更新贷后还款状态失败");
        }

        // 再次获取状态，确认更新成功
        Integer newStatus = getAccountLoanRepaymentStatus(loanCompensation.getLoanId());
        log.info("状态更新结果，贷款ID: {}, 原状态: {}, 新状态: {}",
                loanCompensation.getLoanId(), currentStatus, newStatus);

        // 设置默认值
        loanCompensation.setExamineStatus(0); // 默认为跟催员发起状态
        loanCompensation.setCreateDate(DateUtils.getNowDate());

        // 插入代偿记录
        int result = loanCompensationMapper.insertLoanCompensation(loanCompensation);
        if (result <= 0) {
            // 插入失败，回滚状态
            log.error("插入代偿记录失败，尝试回滚状态，贷款ID: {}", loanCompensation.getLoanId());
            // 回滚到原来的状态
            int rollbackResult = accountLoanService.updateRepaymentStatus(loanCompensation.getLoanId(), currentStatus);
            log.info("状态回滚结果: {}, 贷款ID: {}, 回滚到状态: {}",
                    rollbackResult > 0 ? "成功" : "失败", loanCompensation.getLoanId(), currentStatus);
            return AjaxResult.error("发起代偿失败");
        }

        log.info("发起代偿成功，贷款ID: {}, 代偿ID: {}", loanCompensation.getLoanId(), loanCompensation.getId());
        return AjaxResult.success("发起代偿成功", loanCompensation);
    }
}
