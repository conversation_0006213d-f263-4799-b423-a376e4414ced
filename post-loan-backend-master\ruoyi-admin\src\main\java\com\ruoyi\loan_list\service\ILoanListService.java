package com.ruoyi.loan_list.service;

import java.util.List;
import com.ruoyi.loan_list.domain.LoanList;

/**
 * 贷后逾期记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface ILoanListService {
    /**
     * 查询贷后逾期记录
     * 
     * @param id 贷后逾期记录主键
     * @return 贷后逾期记录
     */
    public LoanList selectLoanListById(String id);

    /**
     * 查询贷后逾期记录列表
     * 
     * @param loanList 贷后逾期记录
     * @return 贷后逾期记录集合
     */
    public List<LoanList> selectLoanListList(LoanList loanList);

    /**
     * 新增贷后逾期记录
     * 
     * @param loanList 贷后逾期记录
     * @return 结果
     */
    public int insertLoanList(LoanList loanList);

    /**
     * 修改贷后逾期记录
     * 
     * @param loanList 贷后逾期记录
     * @return 结果
     */
    public int updateLoanList(LoanList loanList);

    /**
     * 批量删除贷后逾期记录
     * 
     * @param ids 需要删除的贷后逾期记录主键集合
     * @return 结果
     */
    public int deleteLoanListByIds(String[] ids);

    /**
     * 删除贷后逾期记录信息
     * 
     * @param id 贷后逾期记录主键
     * @return 结果
     */
    public int deleteLoanListById(String id);

    /**
     * 批量更新催收人
     */
    int batchUpdateUrgeUser(java.util.List<String> ids, String urgeUser);
    int batchAssignPetitionUser(java.util.List<String> ids, String petitionUser);
    int revokePetitionUser(String id);
}
