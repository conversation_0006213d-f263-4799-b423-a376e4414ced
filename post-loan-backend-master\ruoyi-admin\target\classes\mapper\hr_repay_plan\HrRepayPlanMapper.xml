<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.hr_repay_plan.mapper.HrRepayPlanMapper">
    
    <resultMap type="HrRepayPlan" id="HrRepayPlanResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="period"    column="period"    />
        <result property="repayDate"    column="repay_date"    />
        <result property="actualRepayDate"    column="actual_repay_date"    />
        <result property="repayAmount"    column="repay_amount"    />
        <result property="hrRepayAmount"    column="hr_repay_amount"    />
        <result property="capital"    column="capital"    />
        <result property="actualCapital"    column="actual_capital"    />
        <result property="interest"    column="interest"    />
        <result property="actualInterest"    column="actual_interest"    />
        <result property="defInterest"    column="def_interest"    />
        <result property="actualDefInterest"    column="actual_def_interest"    />
        <result property="guaranteeFee"    column="guarantee_fee"    />
        <result property="balance"    column="balance"    />
        <result property="overdueDays"    column="overdue_days"    />
        <result property="status"    column="status"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
    </resultMap>

    <sql id="selectHrRepayPlanVo">
        select id, apply_id, period, repay_date, actual_repay_date, repay_amount, hr_repay_amount, capital, actual_capital, interest, actual_interest, def_interest, actual_def_interest, guarantee_fee, balance, overdue_days, status, create_date, update_by, update_date from hr_repay_plan
    </sql>

    <select id="selectHrRepayPlanList" parameterType="HrRepayPlan" resultMap="HrRepayPlanResult">
        <include refid="selectHrRepayPlanVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="period != null  and period != ''"> and period = #{period}</if>
            <if test="repayDate != null  and repayDate != ''"> and repay_date = #{repayDate}</if>
            <if test="actualRepayDate != null  and actualRepayDate != ''"> and actual_repay_date = #{actualRepayDate}</if>
            <if test="repayAmount != null  and repayAmount != ''"> and repay_amount = #{repayAmount}</if>
            <if test="hrRepayAmount != null  and hrRepayAmount != ''"> and hr_repay_amount = #{hrRepayAmount}</if>
            <if test="capital != null  and capital != ''"> and capital = #{capital}</if>
            <if test="actualCapital != null  and actualCapital != ''"> and actual_capital = #{actualCapital}</if>
            <if test="interest != null  and interest != ''"> and interest = #{interest}</if>
            <if test="actualInterest != null  and actualInterest != ''"> and actual_interest = #{actualInterest}</if>
            <if test="defInterest != null  and defInterest != ''"> and def_interest = #{defInterest}</if>
            <if test="actualDefInterest != null  and actualDefInterest != ''"> and actual_def_interest = #{actualDefInterest}</if>
            <if test="guaranteeFee != null  and guaranteeFee != ''"> and guarantee_fee = #{guaranteeFee}</if>
            <if test="balance != null  and balance != ''"> and balance = #{balance}</if>
            <if test="overdueDays != null  and overdueDays != ''"> and overdue_days = #{overdueDays}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
    </select>
    
    <select id="selectHrRepayPlanById" parameterType="String" resultMap="HrRepayPlanResult">
        <include refid="selectHrRepayPlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertHrRepayPlan" parameterType="HrRepayPlan">
        insert into hr_repay_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applyId != null">apply_id,</if>
            <if test="period != null">period,</if>
            <if test="repayDate != null">repay_date,</if>
            <if test="actualRepayDate != null">actual_repay_date,</if>
            <if test="repayAmount != null">repay_amount,</if>
            <if test="hrRepayAmount != null">hr_repay_amount,</if>
            <if test="capital != null">capital,</if>
            <if test="actualCapital != null">actual_capital,</if>
            <if test="interest != null">interest,</if>
            <if test="actualInterest != null">actual_interest,</if>
            <if test="defInterest != null">def_interest,</if>
            <if test="actualDefInterest != null">actual_def_interest,</if>
            <if test="guaranteeFee != null">guarantee_fee,</if>
            <if test="balance != null">balance,</if>
            <if test="overdueDays != null">overdue_days,</if>
            <if test="status != null">status,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applyId != null">#{applyId},</if>
            <if test="period != null">#{period},</if>
            <if test="repayDate != null">#{repayDate},</if>
            <if test="actualRepayDate != null">#{actualRepayDate},</if>
            <if test="repayAmount != null">#{repayAmount},</if>
            <if test="hrRepayAmount != null">#{hrRepayAmount},</if>
            <if test="capital != null">#{capital},</if>
            <if test="actualCapital != null">#{actualCapital},</if>
            <if test="interest != null">#{interest},</if>
            <if test="actualInterest != null">#{actualInterest},</if>
            <if test="defInterest != null">#{defInterest},</if>
            <if test="actualDefInterest != null">#{actualDefInterest},</if>
            <if test="guaranteeFee != null">#{guaranteeFee},</if>
            <if test="balance != null">#{balance},</if>
            <if test="overdueDays != null">#{overdueDays},</if>
            <if test="status != null">#{status},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
         </trim>
    </insert>

    <update id="updateHrRepayPlan" parameterType="HrRepayPlan">
        update hr_repay_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyId != null">apply_id = #{applyId},</if>
            <if test="period != null">period = #{period},</if>
            <if test="repayDate != null">repay_date = #{repayDate},</if>
            <if test="actualRepayDate != null">actual_repay_date = #{actualRepayDate},</if>
            <if test="repayAmount != null">repay_amount = #{repayAmount},</if>
            <if test="hrRepayAmount != null">hr_repay_amount = #{hrRepayAmount},</if>
            <if test="capital != null">capital = #{capital},</if>
            <if test="actualCapital != null">actual_capital = #{actualCapital},</if>
            <if test="interest != null">interest = #{interest},</if>
            <if test="actualInterest != null">actual_interest = #{actualInterest},</if>
            <if test="defInterest != null">def_interest = #{defInterest},</if>
            <if test="actualDefInterest != null">actual_def_interest = #{actualDefInterest},</if>
            <if test="guaranteeFee != null">guarantee_fee = #{guaranteeFee},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="overdueDays != null">overdue_days = #{overdueDays},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHrRepayPlanById" parameterType="String">
        delete from hr_repay_plan where id = #{id}
    </delete>

    <delete id="deleteHrRepayPlanByIds" parameterType="String">
        delete from hr_repay_plan where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>