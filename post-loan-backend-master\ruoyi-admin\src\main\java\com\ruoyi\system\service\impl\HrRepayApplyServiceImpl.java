package com.ruoyi.system.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.HrRepayApplyMapper;
import com.ruoyi.system.domain.HrRepayApply;
import com.ruoyi.system.service.IHrRepayApplyService;

/**
 * 华瑞提前还款Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class HrRepayApplyServiceImpl implements IHrRepayApplyService 
{
    @Autowired
    private HrRepayApplyMapper hrRepayApplyMapper;

    /**
     * 查询华瑞提前还款
     * 
     * @param id 华瑞提前还款主键
     * @return 华瑞提前还款
     */
    @Override
    public HrRepayApply selectHrRepayApplyById(String id)
    {
        return hrRepayApplyMapper.selectHrRepayApplyById(id);
    }

    /**
     * 查询华瑞提前还款列表
     * 
     * @param hrRepayApply 华瑞提前还款
     * @return 华瑞提前还款
     */
    @Override
    public List<HrRepayApply> selectHrRepayApplyList(HrRepayApply hrRepayApply)
    {
        return hrRepayApplyMapper.selectHrRepayApplyList(hrRepayApply);
    }

    @Override
    public HrRepayApply selectHrRepayApplyLists(HrRepayApply hrRepayApply)
    {
        return hrRepayApplyMapper.selectHrRepayApplyLists(hrRepayApply);
    }

    /**
     * 新增华瑞提前还款
     * 
     * @param hrRepayApply 华瑞提前还款
     * @return 结果
     */
    @Override
    public int insertHrRepayApply(HrRepayApply hrRepayApply)
    {
        return hrRepayApplyMapper.insertHrRepayApply(hrRepayApply);
    }

    /**
     * 修改华瑞提前还款
     * 
     * @param hrRepayApply 华瑞提前还款
     * @return 结果
     */
    @Override
    public int updateHrRepayApply(HrRepayApply hrRepayApply)
    {
        return hrRepayApplyMapper.updateHrRepayApply(hrRepayApply);
    }

    /**
     * 批量删除华瑞提前还款
     * 
     * @param ids 需要删除的华瑞提前还款主键
     * @return 结果
     */
    @Override
    public int deleteHrRepayApplyByIds(String[] ids)
    {
        return hrRepayApplyMapper.deleteHrRepayApplyByIds(ids);
    }

    /**
     * 删除华瑞提前还款信息
     * 
     * @param id 华瑞提前还款主键
     * @return 结果
     */
    @Override
    public int deleteHrRepayApplyById(String id)
    {
        return hrRepayApplyMapper.deleteHrRepayApplyById(id);
    }
}
