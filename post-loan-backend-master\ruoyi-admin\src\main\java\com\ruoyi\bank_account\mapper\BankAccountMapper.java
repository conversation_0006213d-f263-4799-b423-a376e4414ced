package com.ruoyi.bank_account.mapper;

import java.util.List;
import com.ruoyi.bank_account.domain.BankAccount;

/**
 * 银行账户Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface BankAccountMapper 
{
    /**
     * 查询银行账户
     * 
     * @param id 银行账户主键
     * @return 银行账户
     */
    public BankAccount selectBankAccountById(String id);

    /**
     * 查询银行账户列表
     * 
     * @param bankAccount 银行账户
     * @return 银行账户集合
     */
    public List<BankAccount> selectBankAccountList(BankAccount bankAccount);

    /**
     * 新增银行账户
     * 
     * @param bankAccount 银行账户
     * @return 结果
     */
    public int insertBankAccount(BankAccount bankAccount);

    /**
     * 修改银行账户
     * 
     * @param bankAccount 银行账户
     * @return 结果
     */
    public int updateBankAccount(BankAccount bankAccount);

    /**
     * 删除银行账户
     * 
     * @param id 银行账户主键
     * @return 结果
     */
    public int deleteBankAccountById(String id);

    /**
     * 批量删除银行账户
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBankAccountByIds(String[] ids);
}
