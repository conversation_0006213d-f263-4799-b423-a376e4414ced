package com.ruoyi.system.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ZgcTrialRepayMapper;
import com.ruoyi.system.domain.ZgcTrialRepay;
import com.ruoyi.system.service.IZgcTrialRepayService;

/**
 * 中关村还款试算Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class ZgcTrialRepayServiceImpl implements IZgcTrialRepayService 
{
    @Autowired
    private ZgcTrialRepayMapper zgcTrialRepayMapper;

    /**
     * 查询中关村还款试算
     * 
     * @param id 中关村还款试算主键
     * @return 中关村还款试算
     */
    @Override
    public ZgcTrialRepay selectZgcTrialRepayById(String id)
    {
        return zgcTrialRepayMapper.selectZgcTrialRepayById(id);
    }

    /**
     * 查询中关村还款试算列表
     * 
     * @param zgcTrialRepay 中关村还款试算
     * @return 中关村还款试算
     */
    @Override
    public List<ZgcTrialRepay> selectZgcTrialRepayList(ZgcTrialRepay zgcTrialRepay)
    {
        return zgcTrialRepayMapper.selectZgcTrialRepayList(zgcTrialRepay);
    }

    @Override
    public ZgcTrialRepay selectZgcTrialRepayLists(ZgcTrialRepay zgcTrialRepay)
    {
        return zgcTrialRepayMapper.selectZgcTrialRepayLists(zgcTrialRepay);
    }

    /**
     * 新增中关村还款试算
     * 
     * @param zgcTrialRepay 中关村还款试算
     * @return 结果
     */
    @Override
    public int insertZgcTrialRepay(ZgcTrialRepay zgcTrialRepay)
    {
        return zgcTrialRepayMapper.insertZgcTrialRepay(zgcTrialRepay);
    }

    /**
     * 修改中关村还款试算
     * 
     * @param zgcTrialRepay 中关村还款试算
     * @return 结果
     */
    @Override
    public int updateZgcTrialRepay(ZgcTrialRepay zgcTrialRepay)
    {
        return zgcTrialRepayMapper.updateZgcTrialRepay(zgcTrialRepay);
    }

    /**
     * 批量删除中关村还款试算
     * 
     * @param ids 需要删除的中关村还款试算主键
     * @return 结果
     */
    @Override
    public int deleteZgcTrialRepayByIds(String[] ids)
    {
        return zgcTrialRepayMapper.deleteZgcTrialRepayByIds(ids);
    }

    /**
     * 删除中关村还款试算信息
     * 
     * @param id 中关村还款试算主键
     * @return 结果
     */
    @Override
    public int deleteZgcTrialRepayById(String id)
    {
        return zgcTrialRepayMapper.deleteZgcTrialRepayById(id);
    }
}
