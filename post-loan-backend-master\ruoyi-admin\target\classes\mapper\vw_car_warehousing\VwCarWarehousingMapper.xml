<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vw_car_warehousing.mapper.VwCarWarehousingMapper">
    
    <resultMap type="VwCarWarehousing" id="VwCarWarehousingResult">
        <result property="id"    column="id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="teamId"    column="team_id"    />
        <result property="garageId"    column="garage_id"    />
        <result property="libraryStatus"    column="library_status"    />
        <result property="inboundTime"    column="inbound_time"    />
        <result property="outboundTime"    column="outbound_time"    />
        <result property="locatingCommission"    column="locating_commission"    />
        <result property="keyStatus"    column="key_status"    />
        <result property="keyTime"    column="key_time"    />
        <result property="collectionMethod"    column="collection_method"    />
        <result property="status"    column="status"    />
        <result property="allocationTime"    column="allocation_time"    />
        <result property="customerName"    column="customer_name"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="nickName"    column="nick_name"    />
        <result property="plateNo"    column="plate_no"    />
        <result property="jgName"    column="jg_name"    />
        <result property="jgStatus"    column="jg_status"    />
        <result property="carDetailAddress"    column="car_detail_address"    />
        <result property="carStatus"    column="car_status"    />
        <result property="gpsStatus"    column="gps_status"    />
        <result property="teamName"    column="team_name"    />
        <result property="garageName"    column="garage_name"    />
        <result property="parkingFee"    column="parking_fee"    />
        <result property="sellingFares"    column="selling_fares"    />
        <result property="outbounStatus"    column="outboun_status"    />
        <result property="accidentFlag"    column="accident_flag"    />
    </resultMap>

    <sql id="selectVwCarWarehousingVo">
        select * from vw_car_warehousing
    </sql>
    

    <select id="selectVwCarWarehousingList" parameterType="VwCarWarehousing" resultMap="VwCarWarehousingResult">
        <include refid="selectVwCarWarehousingVo"/>
        <where>  
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="garageId != null "> and garage_id = #{garageId}</if>
            <if test="libraryStatus != null "> and library_status = #{libraryStatus}</if>
            <if test="inboundTime != null "> and inbound_time = #{inboundTime}</if>
            <if test="outboundTime != null "> and outbound_time = #{outboundTime}</if>
            <if test="locatingCommission != null "> and locating_commission = #{locatingCommission}</if>
            <if test="keyStatus != null "> and key_status = #{keyStatus}</if>
            <if test="keyTime != null "> and key_time = #{keyTime}</if>
            <if test="collectionMethod != null "> and collection_method = #{collectionMethod}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="allocationTime != null "> and allocation_time = #{allocationTime}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone = #{mobilePhone}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="plateNo != null  and plateNo != ''"> and plate_no = #{plateNo}</if>
            <if test="jgName != null  and jgName != ''"> and jg_name like concat('%', #{jgName}, '%')</if>
            <if test="jgStatus != null  and jgStatus != ''"> and jg_status = #{jgStatus}</if>
            <if test="carDetailAddress != null  and carDetailAddress != ''"> and car_detail_address = #{carDetailAddress}</if>
            <if test="carStatus != null  and carStatus != ''"> and car_status = #{carStatus}</if>
            <if test="gpsStatus != null  and gpsStatus != ''"> and gps_status = #{gpsStatus}</if>
            <if test="teamName != null  and teamName != ''"> and team_name like concat('%', #{teamName}, '%')</if>
            <if test="garageName != null  and garageName != ''"> and garage_name like concat('%', #{garageName}, '%')</if>
            <if test="parkingFee != null "> and parking_fee = #{parkingFee}</if>
            <if test="sellingFares != null "> and selling_fares = #{sellingFares}</if>
            <if test="outbounStatus != null "> and outboun_status = #{outbounStatus}</if>
            <if test="accidentFlag != null "> and accident_flag = #{accidentFlag}</if>
        </where>
    </select>
    
    <select id="selectVwCarWarehousingById" parameterType="String" resultMap="VwCarWarehousingResult">
        <include refid="selectVwCarWarehousingVo"/>
        where id = #{id}
    </select>

    <insert id="insertVwCarWarehousing" parameterType="VwCarWarehousing">
        insert into vw_car_warehousing
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="applyNo != null">apply_no,</if>
            <if test="teamId != null">team_id,</if>
            <if test="garageId != null">garage_id,</if>
            <if test="libraryStatus != null">library_status,</if>
            <if test="inboundTime != null">inbound_time,</if>
            <if test="outboundTime != null">outbound_time,</if>
            <if test="locatingCommission != null">locating_commission,</if>
            <if test="keyStatus != null">key_status,</if>
            <if test="keyTime != null">key_time,</if>
            <if test="collectionMethod != null">collection_method,</if>
            <if test="status != null">status,</if>
            <if test="allocationTime != null">allocation_time,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="mobilePhone != null">mobile_phone,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="plateNo != null">plate_no,</if>
            <if test="jgName != null">jg_name,</if>
            <if test="jgStatus != null">jg_status,</if>
            <if test="carDetailAddress != null">car_detail_address,</if>
            <if test="carStatus != null">car_status,</if>
            <if test="gpsStatus != null">gps_status,</if>
            <if test="teamName != null">team_name,</if>
            <if test="garageName != null">garage_name,</if>
            <if test="parkingFee != null">parking_fee,</if>
            <if test="sellingFares != null">selling_fares,</if>
            <if test="outbounStatus != null">outboun_status,</if>
            <if test="accidentFlag != null">accident_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="garageId != null">#{garageId},</if>
            <if test="libraryStatus != null">#{libraryStatus},</if>
            <if test="inboundTime != null">#{inboundTime},</if>
            <if test="outboundTime != null">#{outboundTime},</if>
            <if test="locatingCommission != null">#{locatingCommission},</if>
            <if test="keyStatus != null">#{keyStatus},</if>
            <if test="keyTime != null">#{keyTime},</if>
            <if test="collectionMethod != null">#{collectionMethod},</if>
            <if test="status != null">#{status},</if>
            <if test="allocationTime != null">#{allocationTime},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="mobilePhone != null">#{mobilePhone},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="plateNo != null">#{plateNo},</if>
            <if test="jgName != null">#{jgName},</if>
            <if test="jgStatus != null">#{jgStatus},</if>
            <if test="carDetailAddress != null">#{carDetailAddress},</if>
            <if test="carStatus != null">#{carStatus},</if>
            <if test="gpsStatus != null">#{gpsStatus},</if>
            <if test="teamName != null">#{teamName},</if>
            <if test="garageName != null">#{garageName},</if>
            <if test="parkingFee != null">#{parkingFee},</if>
            <if test="sellingFares != null">#{sellingFares},</if>
            <if test="outbounStatus != null">#{outbounStatus},</if>
            <if test="accidentFlag != null">#{accidentFlag},</if>
         </trim>
    </insert>

    <update id="updateVwCarWarehousing" parameterType="VwCarWarehousing">
        update vw_car_warehousing
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="applyNo != null">apply_no = #{applyNo},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="garageId != null">garage_id = #{garageId},</if>
            <if test="libraryStatus != null">library_status = #{libraryStatus},</if>
            <if test="inboundTime != null">inbound_time = #{inboundTime},</if>
            <if test="outboundTime != null">outbound_time = #{outboundTime},</if>
            <if test="locatingCommission != null">locating_commission = #{locatingCommission},</if>
            <if test="keyStatus != null">key_status = #{keyStatus},</if>
            <if test="keyTime != null">key_time = #{keyTime},</if>
            <if test="collectionMethod != null">collection_method = #{collectionMethod},</if>
            <if test="status != null">status = #{status},</if>
            <if test="allocationTime != null">allocation_time = #{allocationTime},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="mobilePhone != null">mobile_phone = #{mobilePhone},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="plateNo != null">plate_no = #{plateNo},</if>
            <if test="jgName != null">jg_name = #{jgName},</if>
            <if test="jgStatus != null">jg_status = #{jgStatus},</if>
            <if test="carDetailAddress != null">car_detail_address = #{carDetailAddress},</if>
            <if test="carStatus != null">car_status = #{carStatus},</if>
            <if test="gpsStatus != null">gps_status = #{gpsStatus},</if>
            <if test="teamName != null">team_name = #{teamName},</if>
            <if test="garageName != null">garage_name = #{garageName},</if>
            <if test="parkingFee != null">parking_fee = #{parkingFee},</if>
            <if test="sellingFares != null">selling_fares = #{sellingFares},</if>
            <if test="outbounStatus != null">outboun_status = #{outbounStatus},</if>
            <if test="accidentFlag != null">accident_flag = #{accidentFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVwCarWarehousingById" parameterType="String">
        delete from vw_car_warehousing where id = #{id}
    </delete>

    <delete id="deleteVwCarWarehousingByIds" parameterType="String">
        delete from vw_car_warehousing where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>