package com.ruoyi.ind_car_info.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.ind_car_info.mapper.IndCarInfoMapper;
import com.ruoyi.ind_car_info.domain.IndCarInfo;
import com.ruoyi.ind_car_info.service.IIndCarInfoService;

/**
 * 车辆信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class IndCarInfoServiceImpl implements IIndCarInfoService 
{
    @Autowired
    private IndCarInfoMapper indCarInfoMapper;

    /**
     * 查询车辆信息
     * 
     * @param applyNo 车辆信息主键
     * @return 车辆信息
     */
    @Override
    public IndCarInfo selectIndCarInfoById(String applyNo)
    {
        return indCarInfoMapper.selectIndCarInfoById(applyNo);
    }

    /**
     * 查询车辆信息列表
     * 
     * @param indCarInfo 车辆信息
     * @return 车辆信息
     */
    @Override
    public List<IndCarInfo> selectIndCarInfoList(IndCarInfo indCarInfo)
    {
        return indCarInfoMapper.selectIndCarInfoList(indCarInfo);
    }

    /**
     * 新增车辆信息
     * 
     * @param indCarInfo 车辆信息
     * @return 结果
     */
    @Override
    public int insertIndCarInfo(IndCarInfo indCarInfo)
    {
        return indCarInfoMapper.insertIndCarInfo(indCarInfo);
    }

    /**
     * 修改车辆信息
     * 
     * @param indCarInfo 车辆信息
     * @return 结果
     */
    @Override
    public int updateIndCarInfo(IndCarInfo indCarInfo)
    {
        return indCarInfoMapper.updateIndCarInfo(indCarInfo);
    }

    /**
     * 批量删除车辆信息
     * 
     * @param ids 需要删除的车辆信息主键
     * @return 结果
     */
    @Override
    public int deleteIndCarInfoByIds(String[] ids)
    {
        return indCarInfoMapper.deleteIndCarInfoByIds(ids);
    }

    /**
     * 删除车辆信息信息
     * 
     * @param id 车辆信息主键
     * @return 结果
     */
    @Override
    public int deleteIndCarInfoById(String id)
    {
        return indCarInfoMapper.deleteIndCarInfoById(id);
    }
}
