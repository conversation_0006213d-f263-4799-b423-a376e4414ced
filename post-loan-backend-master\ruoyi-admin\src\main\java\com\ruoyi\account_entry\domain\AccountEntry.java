package com.ruoyi.account_entry.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 入账登记对象 account_entry
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public class AccountEntry extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 流程id */
    @Excel(name = "流程id")
    private Long loanId;

    /** 代偿id */
    @Excel(name = "代偿id")
    private Long loanCompensationId;

    /** 入账金额类型 */
    @Excel(name = "入账金额类型")
    private String amountType;

    /** 入账时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入账时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date entryTime;

    /** 实入金额 */
    @Excel(name = "实入金额")
    private BigDecimal actualAmount;

    /** 转入账号 */
    @Excel(name = "转入账号")
    private String transferAccount;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 应付金额 */
    @Excel(name = "应付金额")
    private BigDecimal amountDue;

    /** 差价 */
    @Excel(name = "差价")
    private java.math.BigDecimal difference;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setLoanId(Long loanId) {
        this.loanId = loanId;
    }

    public Long getLoanId() {
        return loanId;
    }

    public void setLoanCompensationId(Long loanCompensationId) {
        this.loanCompensationId = loanCompensationId;
    }

    public Long getLoanCompensationId() {
        return loanCompensationId;
    }

    public void setAmountType(String amountType) {
        this.amountType = amountType;
    }

    public String getAmountType() {
        return amountType;
    }

    public void setEntryTime(Date entryTime) {
        this.entryTime = entryTime;
    }

    public Date getEntryTime() {
        return entryTime;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setTransferAccount(String transferAccount) {
        this.transferAccount = transferAccount;
    }

    public String getTransferAccount() {
        return transferAccount;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setAmountDue(BigDecimal amountDue) {
        this.amountDue = amountDue;
    }

    public BigDecimal getAmountDue() {
        return amountDue;
    }

    public void setDifference(java.math.BigDecimal difference) {
        this.difference = difference;
    }

    public java.math.BigDecimal getDifference() {
        return difference;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("loanId", getLoanId())
                .append("loanCompensationId", getLoanCompensationId())
                .append("amountType", getAmountType())
                .append("entryTime", getEntryTime())
                .append("actualAmount", getActualAmount())
                .append("transferAccount", getTransferAccount())
                .append("createDate", getCreateDate())
                .append("updateDate", getUpdateDate())
                .append("createBy", getCreateBy())
                .append("updateBy", getUpdateBy())
                .append("amountDue", getAmountDue())
                .append("difference", getDifference())
                .toString();
    }
}
