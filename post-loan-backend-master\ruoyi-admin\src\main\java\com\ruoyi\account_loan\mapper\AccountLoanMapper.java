package com.ruoyi.account_loan.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.account_loan.domain.AccountLoan;
import com.ruoyi.vw_account_loan.domain.VwAccountLoan;

/**
 * 贷款-贷款信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface AccountLoanMapper {

    /**
     * 查询进件系统借据列表
     *
     * @param accountLoan VIEW
     * @return VIEW集合
     */
    public List<AccountLoan> selectJJAccountLoanList(AccountLoan accountLoan);

    /**
     * 查询贷款-贷款信息
     * 
     * @param id 贷款-贷款信息主键
     * @return 贷款-贷款信息
     */
    public AccountLoan selectAccountLoanById(String id);

    /**
     * 查询贷款-贷款信息列表
     * 
     * @param accountLoan 贷款-贷款信息
     * @return 贷款-贷款信息集合
     */
    public List<AccountLoan> selectAccountLoanList(AccountLoan accountLoan);

    /**
     * 新增贷款-贷款信息
     * 
     * @param accountLoan 贷款-贷款信息
     * @return 结果
     */
    public int insertAccountLoan(AccountLoan accountLoan);

    /**
     * 修改贷款-贷款信息
     * 
     * @param accountLoan 贷款-贷款信息
     * @return 结果
     */
    public int updateAccountLoan(AccountLoan accountLoan);

    /**
     * 删除贷款-贷款信息
     * 
     * @param id 贷款-贷款信息主键
     * @return 结果
     */
    public int deleteAccountLoanById(String id);

    /**
     * 批量删除贷款-贷款信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAccountLoanByIds(String[] ids);

    /**
     * 根据贷款ID查询贷后还款状态
     * 
     * @param loanId 贷款ID
     * @return 贷后还款状态
     */
    public Integer getRepaymentStatusByLoanId(Long loanId);

    /**
     * 更新贷款的贷后还款状态
     * 
     * @param loanId          贷款ID
     * @param repaymentStatus 贷后还款状态
     * @return 结果
     */
    public int updateRepaymentStatus(@Param("loanId") Long loanId, @Param("repaymentStatus") Integer repaymentStatus);

    /**
     * 根据贷款ID获取申请ID
     * 
     * @param loanId 贷款ID
     * @return 申请ID
     */
    public String getLoanApplyIdByLoanId(Long loanId);
}
