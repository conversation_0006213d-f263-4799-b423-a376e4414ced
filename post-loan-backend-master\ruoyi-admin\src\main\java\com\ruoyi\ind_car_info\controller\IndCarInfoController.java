package com.ruoyi.ind_car_info.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.ind_car_info.domain.IndCarInfo;
import com.ruoyi.ind_car_info.service.IIndCarInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 车辆信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/ind_car_info/ind_car_info")
public class IndCarInfoController extends BaseController
{
    @Autowired
    private IIndCarInfoService indCarInfoService;

    /**
     * 查询车辆信息列表
     */
    @PreAuthorize("@ss.hasPermi('ind_car_info:ind_car_info:list')")
    @GetMapping("/list")
    public TableDataInfo list(IndCarInfo indCarInfo)
    {
        startPage();
        List<IndCarInfo> list = indCarInfoService.selectIndCarInfoList(indCarInfo);
        return getDataTable(list);
    }

    /**
     * 导出车辆信息列表
     */
    @PreAuthorize("@ss.hasPermi('ind_car_info:ind_car_info:export')")
    @Log(title = "车辆信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IndCarInfo indCarInfo)
    {
        List<IndCarInfo> list = indCarInfoService.selectIndCarInfoList(indCarInfo);
        ExcelUtil<IndCarInfo> util = new ExcelUtil<IndCarInfo>(IndCarInfo.class);
        util.exportExcel(response, list, "车辆信息数据");
    }

    /**
     * 获取车辆信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('ind_car_info:ind_car_info:query')")
    @GetMapping(value = "/{id}")
    @Anonymous
    public AjaxResult getInfo(@PathVariable("id") String applyNo)
    {
        return success(indCarInfoService.selectIndCarInfoById(applyNo));
    }

    /**
     * 新增车辆信息
     */
    @PreAuthorize("@ss.hasPermi('ind_car_info:ind_car_info:add')")
    @Log(title = "车辆信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IndCarInfo indCarInfo)
    {
        return toAjax(indCarInfoService.insertIndCarInfo(indCarInfo));
    }

    /**
     * 修改车辆信息
     */
    @PreAuthorize("@ss.hasPermi('ind_car_info:ind_car_info:edit')")
    @Log(title = "车辆信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IndCarInfo indCarInfo)
    {
        return toAjax(indCarInfoService.updateIndCarInfo(indCarInfo));
    }

    /**
     * 删除车辆信息
     */
    @PreAuthorize("@ss.hasPermi('ind_car_info:ind_car_info:remove')")
    @Log(title = "车辆信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(indCarInfoService.deleteIndCarInfoByIds(ids));
    }
}
