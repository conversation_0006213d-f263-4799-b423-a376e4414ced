package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 苏银提前还款对象 sy_prepayment_apply
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
public class SyPrepaymentApply extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyId;

    /** 苏银申请编号 */
    @Excel(name = "苏银申请编号")
    private String syApplyId;

    /** 提前结清类型:1-客户提前结清;2-渠道商回购;3-高危回购;4-取消/退车;5-全额代偿 */
    @Excel(name = "提前结清类型:1-客户提前结清;2-渠道商回购;3-高危回购;4-取消/退车;5-全额代偿")
    private String prepaymentType;

    /** 类型:1-试算 2-申请 */
    @Excel(name = "类型:1-试算 2-申请")
    private String type;

    /** 约定还款日 */
    @Excel(name = "约定还款日")
    private String repayDate;

    /** 本金欠款 */
    @Excel(name = "本金欠款")
    private String capitalArrears;

    /** 利息欠款 */
    @Excel(name = "利息欠款")
    private String interestArrears;

    /** 费用欠款(应收罚息) */
    @Excel(name = "费用欠款(应收罚息)")
    private String costArrears;

    /** 剩余本金(剩余未还的本金) */
    @Excel(name = "剩余本金(剩余未还的本金)")
    private String lastCapital;

    /** 违约金 */
    @Excel(name = "违约金")
    private String liquidateDamages;

    /** 当月利息 */
    @Excel(name = "当月利息")
    private String currentInterest;

    /** 保证金冲抵 */
    @Excel(name = "保证金冲抵")
    private String bondAmt;

    /** 留购价 */
    @Excel(name = "留购价")
    private String leaseAmt;

    /** 应退还服务费 */
    @Excel(name = "应退还服务费")
    private String refundAmt;

    /** 应还款金额 */
    @Excel(name = "应还款金额")
    private String repaymentAmount;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setApplyId(String applyId) 
    {
        this.applyId = applyId;
    }

    public String getApplyId() 
    {
        return applyId;
    }

    public void setSyApplyId(String syApplyId) 
    {
        this.syApplyId = syApplyId;
    }

    public String getSyApplyId() 
    {
        return syApplyId;
    }

    public void setPrepaymentType(String prepaymentType) 
    {
        this.prepaymentType = prepaymentType;
    }

    public String getPrepaymentType() 
    {
        return prepaymentType;
    }

    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    public void setRepayDate(String repayDate) 
    {
        this.repayDate = repayDate;
    }

    public String getRepayDate() 
    {
        return repayDate;
    }

    public void setCapitalArrears(String capitalArrears) 
    {
        this.capitalArrears = capitalArrears;
    }

    public String getCapitalArrears() 
    {
        return capitalArrears;
    }

    public void setInterestArrears(String interestArrears) 
    {
        this.interestArrears = interestArrears;
    }

    public String getInterestArrears() 
    {
        return interestArrears;
    }

    public void setCostArrears(String costArrears) 
    {
        this.costArrears = costArrears;
    }

    public String getCostArrears() 
    {
        return costArrears;
    }

    public void setLastCapital(String lastCapital) 
    {
        this.lastCapital = lastCapital;
    }

    public String getLastCapital() 
    {
        return lastCapital;
    }

    public void setLiquidateDamages(String liquidateDamages) 
    {
        this.liquidateDamages = liquidateDamages;
    }

    public String getLiquidateDamages() 
    {
        return liquidateDamages;
    }

    public void setCurrentInterest(String currentInterest) 
    {
        this.currentInterest = currentInterest;
    }

    public String getCurrentInterest() 
    {
        return currentInterest;
    }

    public void setBondAmt(String bondAmt) 
    {
        this.bondAmt = bondAmt;
    }

    public String getBondAmt() 
    {
        return bondAmt;
    }

    public void setLeaseAmt(String leaseAmt) 
    {
        this.leaseAmt = leaseAmt;
    }

    public String getLeaseAmt() 
    {
        return leaseAmt;
    }

    public void setRefundAmt(String refundAmt) 
    {
        this.refundAmt = refundAmt;
    }

    public String getRefundAmt() 
    {
        return refundAmt;
    }

    public void setRepaymentAmount(String repaymentAmount) 
    {
        this.repaymentAmount = repaymentAmount;
    }

    public String getRepaymentAmount() 
    {
        return repaymentAmount;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("applyId", getApplyId())
            .append("syApplyId", getSyApplyId())
            .append("prepaymentType", getPrepaymentType())
            .append("type", getType())
            .append("repayDate", getRepayDate())
            .append("capitalArrears", getCapitalArrears())
            .append("interestArrears", getInterestArrears())
            .append("costArrears", getCostArrears())
            .append("lastCapital", getLastCapital())
            .append("liquidateDamages", getLiquidateDamages())
            .append("currentInterest", getCurrentInterest())
            .append("bondAmt", getBondAmt())
            .append("leaseAmt", getLeaseAmt())
            .append("refundAmt", getRefundAmt())
            .append("repaymentAmount", getRepaymentAmount())
            .append("createDate", getCreateDate())
            .toString();
    }
}
