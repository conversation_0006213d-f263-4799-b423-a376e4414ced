package com.ruoyi.car_team.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 团队管理对象 garage_team
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
public class GarageTeam extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 团队名称 */
    @Excel(name = "团队名称")
    private String teamName;

    /** 账号 */
    @Excel(name = "账号")
    private String account;

    /** 状态：0-禁用，1-启用 */
    @Excel(name = "状态：0-禁用，1-启用")
    private Integer status;

    /** 类型: 0-个人，1-团队 */
    @Excel(name = "类型: 0-个人，1-团队")
    private Integer teamType;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 电话 */
    @Excel(name = "电话")
    private String phone;

    /** 佣金结算账号 */
    @Excel(name = "佣金结算账号")
    private String settlementAccount;

    /** 启用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "启用时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date enabledDate;

    /** 创建时间 */
    private Date createdDate;

    /** 更新时间 */
    private Date updatedDate;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setTeamName(String teamName) 
    {
        this.teamName = teamName;
    }

    public String getTeamName() 
    {
        return teamName;
    }

    public void setAccount(String account) 
    {
        this.account = account;
    }

    public String getAccount() 
    {
        return account;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setTeamType(Integer teamType) 
    {
        this.teamType = teamType;
    }

    public Integer getTeamType() 
    {
        return teamType;
    }

    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }

    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }

    public void setSettlementAccount(String settlementAccount) 
    {
        this.settlementAccount = settlementAccount;
    }

    public String getSettlementAccount() 
    {
        return settlementAccount;
    }

    public void setEnabledDate(Date enabledDate) 
    {
        this.enabledDate = enabledDate;
    }

    public Date getEnabledDate() 
    {
        return enabledDate;
    }

    public void setCreatedDate(Date createdDate) 
    {
        this.createdDate = createdDate;
    }

    public Date getCreatedDate() 
    {
        return createdDate;
    }

    public void setUpdatedDate(Date updatedDate) 
    {
        this.updatedDate = updatedDate;
    }

    public Date getUpdatedDate() 
    {
        return updatedDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teamName", getTeamName())
            .append("account", getAccount())
            .append("status", getStatus())
            .append("teamType", getTeamType())
            .append("contactPerson", getContactPerson())
            .append("phone", getPhone())
            .append("settlementAccount", getSettlementAccount())
            .append("enabledDate", getEnabledDate())
            .append("createdDate", getCreatedDate())
            .append("updatedDate", getUpdatedDate())
            .toString();
    }
}
