package com.ruoyi.car_warehousing.controller;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.car_warehousing.domain.CarWarehousing;
import com.ruoyi.car_warehousing.service.ICarWarehousingService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

import java.math.BigDecimal;

/**
 * 车辆出入库Controller
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/car_warehousing/car_warehousing")
public class CarWarehousingController extends BaseController {
    @Autowired
    private ICarWarehousingService carWarehousingService;

    /**
     * 查询车辆出入库列表
     */
    @PreAuthorize("@ss.hasPermi('car_warehousing:car_warehousing:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarWarehousing carWarehousing) {
        startPage();
        List<CarWarehousing> list = carWarehousingService.selectCarWarehousingList(carWarehousing);
        return getDataTable(list);
    }

    /**
     * 导出车辆出入库列表
     */
    @PreAuthorize("@ss.hasPermi('car_warehousing:car_warehousing:export')")
    @Log(title = "车辆出入库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarWarehousing carWarehousing) {
        List<CarWarehousing> list = carWarehousingService.selectCarWarehousingList(carWarehousing);
        ExcelUtil<CarWarehousing> util = new ExcelUtil<CarWarehousing>(CarWarehousing.class);
        util.exportExcel(response, list, "车辆出入库数据");
    }

    /**
     * 获取车辆出入库详细信息
     */
    @PreAuthorize("@ss.hasPermi('car_warehousing:car_warehousing:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(carWarehousingService.selectCarWarehousingById(id));
    }

    /**
     * 新增车辆出入库
     */
    @PreAuthorize("@ss.hasPermi('car_warehousing:car_warehousing:add')")
    @Log(title = "车辆出入库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CarWarehousing carWarehousing) {
        // 验证出库类型和卖车价格
        if (carWarehousing.getOutbounStatus() != null && carWarehousing.getOutbounStatus() == 2) {
            // 协商卖车时必须输入卖车价格
            if (carWarehousing.getSellingFares() == null
                    || carWarehousing.getSellingFares().compareTo(BigDecimal.ZERO) <= 0) {
                return error("协商卖车时必须输入卖车价格");
            }
        }
        return toAjax(carWarehousingService.insertCarWarehousing(carWarehousing));
    }

    /**
     * 修改车辆出入库
     */
    // @PreAuthorize("@ss.hasPermi('car_warehousing:car_warehousing:edit')")
    @Log(title = "车辆出入库", businessType = BusinessType.UPDATE)
    @PutMapping
    @Anonymous
    public AjaxResult edit(@RequestBody CarWarehousing carWarehousing) {
        System.out.println(carWarehousing);
        // 验证出库类型和卖车价格
        if (carWarehousing.getOutbounStatus() != null && carWarehousing.getOutbounStatus() == 2) {
            // 协商卖车时必须输入卖车价格
            if (carWarehousing.getSellingFares() == null
                    || carWarehousing.getSellingFares().compareTo(BigDecimal.ZERO) <= 0) {
                return error("协商卖车时必须输入卖车价格");
            }
        }

        if (carWarehousing.getLibraryStatus() != null) {
            if (carWarehousing.getLibraryStatus() == 1) {
                carWarehousing.setInboundTime(new Date());
            } else if (carWarehousing.getLibraryStatus() == 2) {
                carWarehousing.setOutboundTime(new Date());
            }
        }
        return toAjax(carWarehousingService.updateCarWarehousing(carWarehousing));
    }

    /**
     * 删除车辆出入库
     */
    @PreAuthorize("@ss.hasPermi('car_warehousing:car_warehousing:remove')")
    @Log(title = "车辆出入库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(carWarehousingService.deleteCarWarehousingByIds(ids));
    }

    /**
     * 获取出库类型选项
     */
    @GetMapping("/outboundTypes")
    public AjaxResult getOutboundTypes() {
        return success(new Object[][] {
                { 1, "归还车主" },
                { 2, "协商卖车" },
                { 3, "法院扣车" }
        });
    }

    /**
     * 获取事故标记选项
     */
    @GetMapping("/accidentFlags")
    public AjaxResult getAccidentFlags() {
        return success(new Object[][] {
                { 1, "无事故" },
                { 2, "有事故" }
        });
    }
}
