11:39:41.549 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
11:39:44.875 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'u.openid_service' in 'field list'
### The error may exist in file [C:\code\project\java_project\post-loan-backend-master\ruoyi-system\target\classes\mapper\system\SysUserMapper.xml]
### The error may involve com.ruoyi.system.mapper.SysUserMapper.selectUserByUserName-Inline
### The error occurred while setting parameters
### SQL: select u.user_id,u.openid,u.openid_service,u.daiqian_id,u.org_id,u.office_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,         d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status,         r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status         from sys_user u       left join sys_dept d on u.dept_id = d.dept_id       left join sys_user_role ur on u.user_id = ur.user_id       left join sys_role r on r.role_id = ur.role_id         where u.user_name = ? and u.del_flag = '0'
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'u.openid_service' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'u.openid_service' in 'field list']
12:30:59.770 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码已失效]
12:31:03.029 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'u.openid_service' in 'field list'
### The error may exist in file [C:\code\project\java_project\post-loan-backend-master\ruoyi-system\target\classes\mapper\system\SysUserMapper.xml]
### The error may involve com.ruoyi.system.mapper.SysUserMapper.selectUserByUserName-Inline
### The error occurred while setting parameters
### SQL: select u.user_id,u.openid,u.openid_service,u.daiqian_id,u.org_id,u.office_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,         d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status,         r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status         from sys_user u       left join sys_dept d on u.dept_id = d.dept_id       left join sys_user_role ur on u.user_id = ur.user_id       left join sys_role r on r.role_id = ur.role_id         where u.user_name = ? and u.del_flag = '0'
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'u.openid_service' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'u.openid_service' in 'field list']
12:34:44.998 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码错误]
12:34:49.488 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
15:49:19.419 [schedule-pool-1] INFO  sys-user - [run,55] - [*************]内网IP[yidianadmin][Success][登录成功]
17:27:47.388 [schedule-pool-1] INFO  sys-user - [run,55] - [*************]内网IP[yidianadmin][Success][登录成功]
