package com.ruoyi.car_team.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.car_team.domain.GarageTeam;
import com.ruoyi.car_team.service.IGarageTeamService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 团队管理Controller
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/car_team/car_team")
public class GarageTeamController extends BaseController {
    @Autowired
    private IGarageTeamService garageTeamService;

    /**
     * 查询团队管理列表
     */
    // @PreAuthorize("@ss.hasPermi('car_team:car_team:list')")
    @GetMapping("/list")
    @Anonymous
    public TableDataInfo list(GarageTeam garageTeam) {
        startPage();

        List<GarageTeam> list = garageTeamService.selectGarageTeamList(garageTeam);
        return getDataTable(list);
    }

    /**
     * 导出团队管理列表
     */
    @PreAuthorize("@ss.hasPermi('car_team:car_team:export')")
    @Log(title = "团队管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GarageTeam garageTeam) {
        List<GarageTeam> list = garageTeamService.selectGarageTeamList(garageTeam);
        ExcelUtil<GarageTeam> util = new ExcelUtil<GarageTeam>(GarageTeam.class);
        util.exportExcel(response, list, "团队管理数据");
    }

    /**
     * 获取团队管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('car_team:car_team:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(garageTeamService.selectGarageTeamById(id));
    }

    /**
     * 新增团队管理
     */
    @PreAuthorize("@ss.hasPermi('car_team:car_team:add')")
    @Log(title = "团队管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GarageTeam garageTeam) {
        return toAjax(garageTeamService.insertGarageTeam(garageTeam));
    }

    /**
     * 修改团队管理
     */
    @PreAuthorize("@ss.hasPermi('car_team:car_team:edit')")
    @Log(title = "团队管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GarageTeam garageTeam) {
        return toAjax(garageTeamService.updateGarageTeam(garageTeam));
    }

    /**
     * 删除团队管理
     */
    @PreAuthorize("@ss.hasPermi('car_team:car_team:remove')")
    @Log(title = "团队管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(garageTeamService.deleteGarageTeamByIds(ids));
    }
}
