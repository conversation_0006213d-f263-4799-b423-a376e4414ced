package com.ruoyi.customer_relative.mapper;

import java.util.List;
import com.ruoyi.customer_relative.domain.CustomerRelative;

/**
 * 客户联系人信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface CustomerRelativeMapper 
{
    /**
     * 查询客户联系人信息
     * 
     * @param id 客户联系人信息主键
     * @return 客户联系人信息
     */
    public CustomerRelative selectCustomerRelativeById(String id);

    /**
     * 查询客户联系人信息列表
     * 
     * @param customerRelative 客户联系人信息
     * @return 客户联系人信息集合
     */
    public List<CustomerRelative> selectCustomerRelativeList(CustomerRelative customerRelative);

    /**
     * 新增客户联系人信息
     * 
     * @param customerRelative 客户联系人信息
     * @return 结果
     */
    public int insertCustomerRelative(CustomerRelative customerRelative);

    /**
     * 修改客户联系人信息
     * 
     * @param customerRelative 客户联系人信息
     * @return 结果
     */
    public int updateCustomerRelative(CustomerRelative customerRelative);

    /**
     * 删除客户联系人信息
     * 
     * @param id 客户联系人信息主键
     * @return 结果
     */
    public int deleteCustomerRelativeById(String id);

    /**
     * 批量删除客户联系人信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerRelativeByIds(String[] ids);
}
