package com.ruoyi.litigation_cost.mapper;

import java.util.List;
import com.ruoyi.litigation_cost.domain.LitigationCost;

/**
 * 法诉费用明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface LitigationCostMapper 
{
    /**
     * 查询法诉费用明细
     * 
     * @param id 法诉费用明细主键
     * @return 法诉费用明细
     */
    public LitigationCost selectLitigationCostById(Long id);

    /**
     * 查询法诉费用明细列表
     * 
     * @param litigationCost 法诉费用明细
     * @return 法诉费用明细集合
     */
    public List<LitigationCost> selectLitigationCostList(LitigationCost litigationCost);

    /**
     * 新增法诉费用明细
     * 
     * @param litigationCost 法诉费用明细
     * @return 结果
     */
    public int insertLitigationCost(LitigationCost litigationCost);

    /**
     * 修改法诉费用明细
     * 
     * @param litigationCost 法诉费用明细
     * @return 结果
     */
    public int updateLitigationCost(LitigationCost litigationCost);

    /**
     * 删除法诉费用明细
     * 
     * @param id 法诉费用明细主键
     * @return 结果
     */
    public int deleteLitigationCostById(Long id);

    /**
     * 批量删除法诉费用明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLitigationCostByIds(Long[] ids);
}
