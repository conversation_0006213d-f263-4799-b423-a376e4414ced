package com.ruoyi.litigation_cost_approval.domain.dto;

/**
 * 单个审批请求DTO
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class SingleApprovalRequest {
    
    /** 记录ID */
    private Long id;
    
    /** 审批状态(1-通过 2-拒绝) */
    private String status;
    
    /** 拒绝原因 */
    private String rejectReason;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    @Override
    public String toString() {
        return "SingleApprovalRequest{" +
                "id=" + id +
                ", status='" + status + '\'' +
                ", rejectReason='" + rejectReason + '\'' +
                '}';
    }
}
