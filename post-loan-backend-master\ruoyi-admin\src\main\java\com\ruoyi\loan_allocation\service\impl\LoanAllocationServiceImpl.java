package com.ruoyi.loan_allocation.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.loan_allocation.mapper.LoanAllocationMapper;
import com.ruoyi.loan_allocation.domain.LoanAllocation;
import com.ruoyi.loan_allocation.service.ILoanAllocationService;

/**
 * 流程跟催员分配Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-21
 */
@Service
public class LoanAllocationServiceImpl implements ILoanAllocationService 
{
    @Autowired
    private LoanAllocationMapper loanAllocationMapper;

    /**
     * 查询流程跟催员分配
     * 
     * @param id 流程跟催员分配主键
     * @return 流程跟催员分配
     */
    @Override
    public LoanAllocation selectLoanAllocationById(String id)
    {
        return loanAllocationMapper.selectLoanAllocationById(id);
    }

    /**
     * 查询流程跟催员分配列表
     * 
     * @param loanAllocation 流程跟催员分配
     * @return 流程跟催员分配
     */
    @Override
    public List<LoanAllocation> selectLoanAllocationList(LoanAllocation loanAllocation)
    {
        return loanAllocationMapper.selectLoanAllocationList(loanAllocation);
    }

    /**
     * 新增流程跟催员分配
     * 
     * @param loanAllocation 流程跟催员分配
     * @return 结果
     */
    @Override
    public int insertLoanAllocation(LoanAllocation loanAllocation)
    {
        loanAllocation.setCreateTime(DateUtils.getNowDate());
        return loanAllocationMapper.insertLoanAllocation(loanAllocation);
    }

    /**
     * 修改流程跟催员分配
     * 
     * @param loanAllocation 流程跟催员分配
     * @return 结果
     */
    @Override
    public int updateLoanAllocation(LoanAllocation loanAllocation)
    {
        loanAllocation.setUpdateTime(DateUtils.getNowDate());
        return loanAllocationMapper.updateLoanAllocation(loanAllocation);
    }

    /**
     * 批量删除流程跟催员分配
     * 
     * @param ids 需要删除的流程跟催员分配主键
     * @return 结果
     */
    @Override
    public int deleteLoanAllocationByIds(String[] ids)
    {
        return loanAllocationMapper.deleteLoanAllocationByIds(ids);
    }

    /**
     * 删除流程跟催员分配信息
     * 
     * @param id 流程跟催员分配主键
     * @return 结果
     */
    @Override
    public int deleteLoanAllocationById(String id)
    {
        return loanAllocationMapper.deleteLoanAllocationById(id);
    }
}
