package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.HrRepayApply;

/**
 * 华瑞提前还款Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
public interface HrRepayApplyMapper 
{
    /**
     * 查询华瑞提前还款
     * 
     * @param id 华瑞提前还款主键
     * @return 华瑞提前还款
     */
    public HrRepayApply selectHrRepayApplyById(String id);

    /**
     * 查询华瑞提前还款列表
     * 
     * @param hrRepayApply 华瑞提前还款
     * @return 华瑞提前还款集合
     */
    public List<HrRepayApply> selectHrRepayApplyList(HrRepayApply hrRepayApply);


    public HrRepayApply selectHrRepayApplyLists(HrRepayApply hrRepayApply);

    /**
     * 新增华瑞提前还款
     * 
     * @param hrRepayApply 华瑞提前还款
     * @return 结果
     */
    public int insertHrRepayApply(HrRepayApply hrRepayApply);

    /**
     * 修改华瑞提前还款
     * 
     * @param hrRepayApply 华瑞提前还款
     * @return 结果
     */
    public int updateHrRepayApply(HrRepayApply hrRepayApply);

    /**
     * 删除华瑞提前还款
     * 
     * @param id 华瑞提前还款主键
     * @return 结果
     */
    public int deleteHrRepayApplyById(String id);

    /**
     * 批量删除华瑞提前还款
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHrRepayApplyByIds(String[] ids);
}
