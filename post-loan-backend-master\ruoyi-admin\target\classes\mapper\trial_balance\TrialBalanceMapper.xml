<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.trial_balance.mapper.TrialBalanceMapper">

    <resultMap type="TrialBalance" id="TrialBalanceResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="loanId"    column="loan_id"    />
        <result property="settleId"    column="settle_id"    />
        <result property="compensationId"    column="compensation_id"    />
        <result property="overdueDays"    column="overdue_days"    />
        <result property="loanAmount"    column="loan_amount"    />
        <result property="status"    column="status"    />
        <result property="principal"    column="principal"    />
        <result property="interest"    column="interest"    />
        <result property="defaultInterest"    column="default_interest"    />
        <result property="compoundInterest"    column="compound_interest"    />
        <result property="guaranteeFee"    column="guarantee_fee"    />
        <result property="bTotalMoney"    column="B_total_money"    />
        <result property="dTotalMoney"    column="D_total_money"    />
        <result property="liquidatedDamages"    column="liquidated_damages"    />
        <result property="otherDebt"    column="other_debt"    />
        <result property="oneCommutation"    column="one_commutation"    />
        <result property="totalMoney"    column="total_money"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
    </resultMap>

    <sql id="selectTrialBalanceVo">
        select * from trial_balance
    </sql>

    <select id="selectTrialBalanceList" parameterType="TrialBalance" resultMap="TrialBalanceResult">
        <include refid="selectTrialBalanceVo"/>
        <where>
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="loanId != null "> and loan_id = #{loanId}</if>
             <if test="settleId != null "> and settle_id = #{settleId}</if>
              <if test="compensationId != null "> and compensation_id = #{compensationId}</if>
            <if test="overdueDays != null "> and overdue_days = #{overdueDays}</if>
            <if test="loanAmount != null "> and loan_amount = #{loanAmount}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="principal != null "> and principal = #{principal}</if>
            <if test="interest != null "> and interest = #{interest}</if>
            <if test="defaultInterest != null "> and default_interest = #{defaultInterest}</if>
            <if test="compoundInterest != null "> and compound_interest = #{compoundInterest}</if>
            <if test="guaranteeFee != null "> and guarantee_fee = #{guaranteeFee}</if>
            <if test="bTotalMoney != null "> and B_total_money = #{bTotalMoney}</if>
            <if test="dTotalMoney != null "> and D_total_money = #{dTotalMoney}</if>
            <if test="liquidatedDamages != null "> and liquidated_damages = #{liquidatedDamages}</if>
            <if test="otherDebt != null "> and other_debt = #{otherDebt}</if>
            <if test="oneCommutation != null "> and one_commutation = #{oneCommutation}</if>
            <if test="totalMoney != null "> and total_money = #{totalMoney}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
    </select>

    <select id="selectTrialBalanceById" parameterType="String" resultMap="TrialBalanceResult">
        <include refid="selectTrialBalanceVo"/>
        where id = #{id}
    </select>
    <select id="selectTrialBalanceByIdjq" parameterType="String" resultMap="TrialBalanceResult">
        <include refid="selectTrialBalanceVo"/>
        where settle_id = #{id}
    </select>

    <select id="selectTrialBalanceByIddc" parameterType="String" resultMap="TrialBalanceResult">
        <include refid="selectTrialBalanceVo"/>
        where compensation_id = #{id}
    </select>

    <select id="selectTrialBalanceByIds" parameterType="map" resultMap="TrialBalanceResult">
        <include refid="selectTrialBalanceVo"/>
        where settle_id = #{settleId} and status = #{status}
    </select>

    <insert id="insertTrialBalance" parameterType="TrialBalance" useGeneratedKeys="true" keyProperty="id">
        insert into trial_balance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyId != null">apply_id,</if>
            <if test="loanId != null">loan_id,</if>
            <if test="settleId != null">settle_id,</if>
            <if test="overdueDays != null">overdue_days,</if>
            <if test="loanAmount != null">loan_amount,</if>
            <if test="status != null">status,</if>
            <if test="compensationId != null">compensation_id,</if>
            <if test="principal != null">principal,</if>
            <if test="interest != null">interest,</if>
            <if test="defaultInterest != null">default_interest,</if>
            <if test="compoundInterest != null">compound_interest,</if>
            <if test="guaranteeFee != null">guarantee_fee,</if>
            <if test="bTotalMoney != null">B_total_money,</if>
            <if test="dTotalMoney != null">D_total_money,</if>
            <if test="liquidatedDamages != null">liquidated_damages,</if>
            <if test="otherDebt != null">other_debt,</if>
            <if test="oneCommutation != null">one_commutation,</if>
            <if test="totalMoney != null">total_money,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyId != null">#{applyId},</if>
            <if test="loanId != null">#{loanId},</if>
            <if test="settleId != null">#{settleId},</if>
            <if test="overdueDays != null">#{overdueDays},</if>
            <if test="loanAmount != null">#{loanAmount},</if>
            <if test="status != null">#{status},</if>
            <if test="compensationId != null">#{compensationId},</if>
            <if test="principal != null">#{principal},</if>
            <if test="interest != null">#{interest},</if>
            <if test="defaultInterest != null">#{defaultInterest},</if>
            <if test="compoundInterest != null">#{compoundInterest},</if>
            <if test="guaranteeFee != null">#{guaranteeFee},</if>
            <if test="bTotalMoney != null">#{bTotalMoney},</if>
            <if test="dTotalMoney != null">#{dTotalMoney},</if>
            <if test="liquidatedDamages != null">#{liquidatedDamages},</if>
            <if test="otherDebt != null">#{otherDebt},</if>
            <if test="oneCommutation != null">#{oneCommutation},</if>
            <if test="totalMoney != null">#{totalMoney},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
        </trim>
    </insert>

    <update id="updateTrialBalance" parameterType="TrialBalance">
        update trial_balance
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyId != null">apply_id = #{applyId},</if>
            <if test="loanId != null">loan_id = #{loanId},</if>
            <if test="settleId != null">settle_id = #{settleId},</if>
            <if test="compensationId != null">compensation_id = #{compensationId},</if>
            <if test="overdueDays != null">overdue_days = #{overdueDays},</if>
            <if test="loanAmount != null">loan_amount = #{loanAmount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="principal != null">principal = #{principal},</if>
            <if test="interest != null">interest = #{interest},</if>
            <if test="defaultInterest != null">default_interest = #{defaultInterest},</if>
            <if test="compoundInterest != null">compound_interest = #{compoundInterest},</if>
            <if test="guaranteeFee != null">guarantee_fee = #{guaranteeFee},</if>
            <if test="bTotalMoney != null">B_total_money = #{bTotalMoney},</if>
            <if test="dTotalMoney != null">D_total_money = #{dTotalMoney},</if>
            <if test="liquidatedDamages != null">liquidated_damages = #{liquidatedDamages},</if>
            <if test="otherDebt != null">other_debt = #{otherDebt},</if>
            <if test="oneCommutation != null">one_commutation = #{oneCommutation},</if>
            <if test="totalMoney != null">total_money = #{totalMoney},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTrialBalanceById" parameterType="String">
        delete from trial_balance where id = #{id}
    </delete>

    <delete id="deleteTrialBalanceByIds" parameterType="String">
        delete from trial_balance where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>