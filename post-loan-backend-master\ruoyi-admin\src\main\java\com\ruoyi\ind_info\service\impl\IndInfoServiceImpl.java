package com.ruoyi.ind_info.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.ind_info.mapper.IndInfoMapper;
import com.ruoyi.ind_info.domain.IndInfo;
import com.ruoyi.ind_info.service.IIndInfoService;

/**
 * 个人客户信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class IndInfoServiceImpl implements IIndInfoService 
{
    @Autowired
    private IndInfoMapper indInfoMapper;

    /**
     * 查询个人客户信息
     * 
     * @param id 个人客户信息主键
     * @return 个人客户信息
     */
    @Override
    public IndInfo selectIndInfoById(String id)
    {
        return indInfoMapper.selectIndInfoById(id);
    }

    /**
     * 查询个人客户信息列表
     * 
     * @param indInfo 个人客户信息
     * @return 个人客户信息
     */
    @Override
    public List<IndInfo> selectIndInfoList(IndInfo indInfo)
    {
        return indInfoMapper.selectIndInfoList(indInfo);
    }

    /**
     * 新增个人客户信息
     * 
     * @param indInfo 个人客户信息
     * @return 结果
     */
    @Override
    public int insertIndInfo(IndInfo indInfo)
    {
        return indInfoMapper.insertIndInfo(indInfo);
    }

    /**
     * 修改个人客户信息
     * 
     * @param indInfo 个人客户信息
     * @return 结果
     */
    @Override
    public int updateIndInfo(IndInfo indInfo)
    {
        return indInfoMapper.updateIndInfo(indInfo);
    }

    /**
     * 批量删除个人客户信息
     * 
     * @param ids 需要删除的个人客户信息主键
     * @return 结果
     */
    @Override
    public int deleteIndInfoByIds(String[] ids)
    {
        return indInfoMapper.deleteIndInfoByIds(ids);
    }

    /**
     * 删除个人客户信息信息
     * 
     * @param id 个人客户信息主键
     * @return 结果
     */
    @Override
    public int deleteIndInfoById(String id)
    {
        return indInfoMapper.deleteIndInfoById(id);
    }
}
