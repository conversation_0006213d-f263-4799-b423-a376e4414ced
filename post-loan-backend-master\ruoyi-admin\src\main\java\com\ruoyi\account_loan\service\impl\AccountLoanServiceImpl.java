package com.ruoyi.account_loan.service.impl;

import java.util.List;
import java.util.Arrays;
import java.util.Date;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.vw_account_loan.domain.VwAccountLoan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ruoyi.account_loan.mapper.AccountLoanMapper;
import com.ruoyi.account_loan.domain.AccountLoan;
import com.ruoyi.account_loan.service.IAccountLoanService;

/**
 * 贷款-贷款信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
public class AccountLoanServiceImpl implements IAccountLoanService {
    private static final Logger log = LoggerFactory.getLogger(AccountLoanServiceImpl.class);

    @Autowired
    private AccountLoanMapper accountLoanMapper;

    /**
     * 查询贷款-贷款信息列表
     *
     * @param accountLoan 贷款-贷款信息
     * @return 贷款-贷款信息
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<AccountLoan> selectJJAccountLoanList(AccountLoan accountLoan) {
        return accountLoanMapper.selectJJAccountLoanList(accountLoan);
    }

    /**
     * 查询贷款-贷款信息
     * 
     * @param id 贷款-贷款信息主键
     * @return 贷款-贷款信息
     */
    @Override
    public AccountLoan selectAccountLoanById(String id) {
        return accountLoanMapper.selectAccountLoanById(id);
    }

    /**
     * 查询贷款-贷款信息列表
     * 
     * @param accountLoan 贷款-贷款信息
     * @return 贷款-贷款信息
     */
    @Override
    public List<AccountLoan> selectAccountLoanList(AccountLoan accountLoan) {
        return accountLoanMapper.selectAccountLoanList(accountLoan);
    }

    /**
     * 新增贷款-贷款信息
     * 
     * @param accountLoan 贷款-贷款信息
     * @return 结果
     */
    @Override
    public int insertAccountLoan(AccountLoan accountLoan) {
        return accountLoanMapper.insertAccountLoan(accountLoan);
    }

    /**
     * 修改贷款-贷款信息
     * 
     * @param accountLoan 贷款-贷款信息
     * @return 结果
     */
    @Override
    public int updateAccountLoan(AccountLoan accountLoan) {
        return accountLoanMapper.updateAccountLoan(accountLoan);
    }

    /**
     * 批量删除贷款-贷款信息
     * 
     * @param ids 需要删除的贷款-贷款信息主键
     * @return 结果
     */
    @Override
    public int deleteAccountLoanByIds(String[] ids) {
        return accountLoanMapper.deleteAccountLoanByIds(ids);
    }

    /**
     * 删除贷款-贷款信息信息
     * 
     * @param id 贷款-贷款信息主键
     * @return 结果
     */
    @Override
    public int deleteAccountLoanById(String id) {
        return accountLoanMapper.deleteAccountLoanById(id);
    }

    /**
     * 根据贷款ID查询贷后还款状态
     * 
     * @param loanId 贷款ID
     * @return 贷后还款状态
     */
    @Override
    public Integer getRepaymentStatusByLoanId(Long loanId) {
        if (loanId == null) {
            log.error("贷款ID不能为空");
            return null;
        }

        try {
            return accountLoanMapper.getRepaymentStatusByLoanId(loanId);
        } catch (Exception e) {
            log.error("查询贷后还款状态失败，贷款ID: " + loanId, e);
            return null;
        }
    }

    /**
     * 更新贷款的贷后还款状态
     * 
     * @param loanId          贷款ID
     * @param repaymentStatus 贷后还款状态
     * @return 结果
     */
    @Override
    public int updateRepaymentStatus(Long loanId, Integer repaymentStatus) {
        if (loanId == null || repaymentStatus == null) {
            log.error("贷款ID和贷后还款状态不能为空, loanId={}, repaymentStatus={}", loanId, repaymentStatus);
            return 0;
        }

        try {
            log.info("开始更新贷后还款状态, 贷款ID: {}, 目标状态: {}", loanId, repaymentStatus);

            // 将Integer类型的状态转换为String类型
            String repaymentStatusStr = String.valueOf(repaymentStatus);

            // 创建AccountLoan对象用于更新
            AccountLoan accountLoan = new AccountLoan();
            accountLoan.setRepaymentStatus(repaymentStatusStr);

            // 通过贷款ID查询account_loan记录
            AccountLoan loan = new AccountLoan();
            loan.setApplyId(getLoanApplyIdByLoanId(loanId));
            List<AccountLoan> loans = selectAccountLoanList(loan);

            if (loans == null || loans.isEmpty()) {
                log.error("未找到对应的贷款记录，贷款ID: {}", loanId);
                return 0;
            }

            // 获取第一条记录的ID
            accountLoan.setId(loans.get(0).getId());
            accountLoan.setUpdateDate(new Date());

            // 更新记录
            int result = updateAccountLoan(accountLoan);

            log.info("更新贷后还款状态结果: {}, 贷款ID: {}, 目标状态: {}",
                    result > 0 ? "成功" : "失败", loanId, repaymentStatus);
            return result;
        } catch (Exception e) {
            log.error("更新贷后还款状态异常，贷款ID: " + loanId + ", 状态: " + repaymentStatus, e);
            return 0;
        }
    }

    /**
     * 根据贷款ID获取申请ID
     * 
     * @param loanId 贷款ID
     * @return 申请ID
     */
    private String getLoanApplyIdByLoanId(Long loanId) {
        try {
            return accountLoanMapper.getLoanApplyIdByLoanId(loanId);
        } catch (Exception e) {
            log.error("获取申请ID失败，贷款ID: " + loanId, e);
            return null;
        }
    }

    /**
     * 检查并更新贷后还款状态
     * 
     * @param loanId          贷款ID
     * @param targetStatus    目标状态
     * @param allowedStatuses 允许的当前状态数组
     * @return 操作结果：0-失败，1-成功，-1-状态不匹配
     */
    @Override
    public int checkAndUpdateRepaymentStatus(Long loanId, Integer targetStatus, Integer[] allowedStatuses) {
        // 参数校验
        if (loanId == null || targetStatus == null || allowedStatuses == null || allowedStatuses.length == 0) {
            log.error("参数不完整，无法执行状态检查和更新, loanId={}, targetStatus={}, allowedStatuses={}",
                    loanId, targetStatus, allowedStatuses != null ? Arrays.toString(allowedStatuses) : "null");
            return 0;
        }

        // 获取当前状态
        Integer currentStatus = getRepaymentStatusByLoanId(loanId);
        log.info("获取当前贷后还款状态, 贷款ID: {}, 当前状态: {}", loanId, currentStatus);

        if (currentStatus == null) {
            log.error("无法获取贷款的当前状态，贷款ID: {}", loanId);
            return 0;
        }

        // 检查状态是否允许更新
        boolean isStatusAllowed = false;
        for (Integer status : allowedStatuses) {
            if (status.equals(currentStatus)) {
                isStatusAllowed = true;
                break;
            }
        }

        if (!isStatusAllowed) {
            log.info("当前状态不允许更新，贷款ID: {}, 当前状态: {}, 允许状态: {}, 目标状态: {}",
                    loanId, currentStatus, Arrays.toString(allowedStatuses), targetStatus);
            return -1;
        }

        // 执行更新
        int result = updateRepaymentStatus(loanId, targetStatus);
        if (result > 0) {
            log.info("贷款状态更新成功，贷款ID: {}, 从状态: {} 更新为: {}",
                    loanId, currentStatus, targetStatus);
            return 1;
        } else {
            log.error("贷款状态更新失败，贷款ID: {}, 当前状态: {}, 目标状态: {}",
                    loanId, currentStatus, targetStatus);
            return 0;
        }
    }
}
