package com.ruoyi.loan_list.mapper;

import java.util.List;
import com.ruoyi.loan_list.domain.LoanList;

/**
 * 贷后逾期记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface LoanListMapper {
    /**
     * 查询贷后逾期记录
     * 
     * @param id 贷后逾期记录主键
     * @return 贷后逾期记录
     */
    public LoanList selectLoanListById(String id);

    /**
     * 查询贷后逾期记录列表
     * 
     * @param loanList 贷后逾期记录
     * @return 贷后逾期记录集合
     */
    public List<LoanList> selectLoanListList(LoanList loanList);

    /**
     * 新增贷后逾期记录
     * 
     * @param loanList 贷后逾期记录
     * @return 结果
     */
    public int insertLoanList(LoanList loanList);

    /**
     * 修改贷后逾期记录
     * 
     * @param loanList 贷后逾期记录
     * @return 结果
     */
    public int updateLoanList(LoanList loanList);

    /**
     * 删除贷后逾期记录
     * 
     * @param id 贷后逾期记录主键
     * @return 结果
     */
    public int deleteLoanListById(String id);

    /**
     * 批量删除贷后逾期记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLoanListByIds(String[] ids);

    int batchUpdateUrgeUser(@org.apache.ibatis.annotations.Param("ids") java.util.List<String> ids,
            @org.apache.ibatis.annotations.Param("urgeUser") String urgeUser);

    int batchAssignPetitionUser(@org.apache.ibatis.annotations.Param("ids") java.util.List<String> ids,
                               @org.apache.ibatis.annotations.Param("petitionUser") String petitionUser,
                               @org.apache.ibatis.annotations.Param("petitionTime") java.util.Date petitionTime,
                               @org.apache.ibatis.annotations.Param("petitionAssignmentType") Integer petitionAssignmentType);

    int revokePetitionUser(@org.apache.ibatis.annotations.Param("id") String id,
                          @org.apache.ibatis.annotations.Param("petitionUser") String petitionUser,
                          @org.apache.ibatis.annotations.Param("petitionTime") java.util.Date petitionTime,
                          @org.apache.ibatis.annotations.Param("petitionAssignmentType") Integer petitionAssignmentType);

    /**
     * 批量查询指定id的petitionAssignmentType
     */
    List<LoanList> selectPetitionAssignmentTypesByIds(@org.apache.ibatis.annotations.Param("ids") List<String> ids);
}
