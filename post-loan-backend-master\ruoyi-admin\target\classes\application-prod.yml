# 生产环境配置文件
# 使用方式: java -jar app.jar --spring.profiles.active=prod

# 第三方接口安全配置（生产环境）
third-party:
  # AES加密配置
  aes:
    # 从环境变量获取32位AES密钥
    secret-key: "${THIRD_PARTY_AES_KEY}"
    # 从环境变量获取16位初始向量
    iv: "${THIRD_PARTY_AES_IV}"
  
  # 签名验证配置
  sign:
    # 从环境变量获取签名密钥
    key: "${THIRD_PARTY_SIGN_KEY}"
  
  # 合作方配置
  partners:
    # 允许的合作方列表（从环境变量获取）
    allowed: "${THIRD_PARTY_ALLOWED_PARTNERS:daihou001,daihou002}"
    # 时间戳有效期（分钟）
    timestamp-validity: "${THIRD_PARTY_TIMESTAMP_VALIDITY:5}"
    # 启用IP白名单
    enable-ip-whitelist: "${THIRD_PARTY_ENABLE_IP_WHITELIST:true}"
    # IP白名单（从环境变量获取）
    ip-whitelist: "${THIRD_PARTY_IP_WHITELIST:}"

# 日志配置（生产环境）
logging:
  level:
    com.loan: INFO
    com.loan.controller.ThirdPartyController: INFO
    com.loan.util: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/third-party-api.log
    max-size: 100MB
    max-history: 30

# 安全配置增强
server:
  # 隐藏服务器信息
  server-header: ""
  # 启用压缩
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain
  # Tomcat配置
  tomcat:
    # 连接超时时间
    connection-timeout: 20000
    # 最大连接数
    max-connections: 8192
    # 最大线程数
    threads:
      max: 200
      min-spare: 10
  
# 监控配置（需要spring-boot-starter-actuator依赖）
# management:
#   endpoints:
#     web:
#       exposure:
#         # 只暴露健康检查端点
#         include: health
#   endpoint:
#     health:
#       show-details: never
