<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vw_loan_info.mapper.vw_loan_infoMapper">

    <resultMap type="vw_loan_info" id="vw_loan_infoResult">
        <result property="id"    column="id"    />
        <result property="urgeName"    column="urge_name"    />
        <result property="applyId"    column="apply_id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="partnerId"    column="partner_id"    />
        <result property="dhUser"    column="dh_user"    />
        <result property="followUp"    column="follow_up"    />
        <result property="urgeUser"    column="urge_user"    />
        <result property="petitionUser"    column="petition_user"    />
        <result property="lawUser"    column="law_user"    />
        <result property="period"    column="period"    />
        <result property="realReturnMoney"    column="real_return_money"    />
        <result property="reminderDate"    column="reminder_date"    />
        <result property="isPetition"    column="is_petition"    />
        <result property="badDebt"    column="bad_debt"    />
        <result property="status"    column="status"    />
        <result property="billStatus"    column="bill_status"    />
        <result property="slippageStatus"    column="slippage_status"    />
        <result property="followStatus"    column="follow_status"    />
        <result property="isExtension"    column="is_extension"    />
        <result property="extensionDate"    column="extension_date"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="contractAmt"    column="contract_amt"    />
        <result property="fOverdueAmount"    column="F_overdue_amount"    />
        <result property="bOverdueDays"    column="B_overdue_days"    />
        <result property="bOverdueAmount"    column="B_overdue_amount"    />
        <result property="dOverdueDays"    column="D_overdue_days"    />
        <result property="dOverdueAmount"    column="D_overdue_amount"    />
        <result property="bRepaymentDate"    column="B_repayment_date"    />
        <result property="dRepaymentDate"    column="D_repayment_date"    />
        <result property="bPeriods"    column="B_periods"    />
        <result property="dPeriods"    column="D_periods"    />
        <result property="bRemainingAmounts"    column="B_remaining_amounts"    />
        <result property="dRemainingAmounts"    column="D_remaining_amounts"    />
        <result property="bCurrentPeriods"    column="B_current_periods"    />
        <result property="dCurrentPeriods"    column="D_current_periods"    />
        <result property="bRepaymentAmounts"    column="B_repayment_amounts"    />
        <result property="dRepaymentAmounts"    column="D_repayment_amounts"    />
        <result property="bNowMoney"    column="B_now_money"    />
        <result property="dNowMoney"    column="D_now_money"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="plateNo"    column="plate_no"    />
        <result property="orgName"    column="org_name"    />
        <result property="officeName"    column="office_name"    />
        <result property="repaymentStatus"    column="repayment_status"    />
    </resultMap>

    <sql id="selectvw_loan_infoVo">
        select * from vw_loan_info
    </sql>

    <select id="selectvw_loan_infoList" parameterType="vw_loan_info" resultMap="vw_loan_infoResult">
        <include refid="selectvw_loan_infoVo"/>
        <where>
            <if test="urgeName != null  and urgeName != ''"> and urge_name like concat('%', #{urgeName}, '%')</if>
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="customerId != null  and customerId != ''"> and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="partnerId != null  and partnerId != ''"> and partner_id = #{partnerId}</if>
            <if test="dhUser != null  and dhUser != ''"> and dh_user = #{dhUser}</if>
            <if test="followUp != null  and followUp != ''"> and follow_up = #{followUp}</if>
            <if test="urgeUser != null  and urgeUser != ''"> and urge_user = #{urgeUser}</if>
            <if test="petitionUser != null  and petitionUser != ''"> and petition_user = #{petitionUser}</if>
            <if test="lawUser != null  and lawUser != ''"> and law_user = #{lawUser}</if>
            <if test="period != null  and period != ''"> and period = #{period}</if>
            <if test="realReturnMoney != null "> and real_return_money = #{realReturnMoney}</if>
            <if test="reminderDate != null "> and reminder_date = #{reminderDate}</if>
            <if test="isPetition != null  and isPetition != ''"> and is_petition = #{isPetition}</if>
            <if test="badDebt != null  and badDebt != ''"> and bad_debt = #{badDebt}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="billStatus != null  and billStatus != ''"> and bill_status = #{billStatus}</if>
            <if test="slippageStatus != null  and slippageStatus != ''"> and slippage_status = #{slippageStatus}</if>
            <if test="followStatus != null  and followStatus != ''"> and follow_status = #{followStatus}</if>
            <if test="isExtension != null  and isExtension != ''"> and is_extension = #{isExtension}</if>
            <if test="extensionDate != null "> and extension_date = #{extensionDate}</if>
            <if test="contractAmt != null "> and contract_amt = #{contractAmt}</if>
            <if test="fOverdueAmount != null "> and F_overdue_amount = #{fOverdueAmount}</if>
            <if test="bOverdueDays != null "> and B_overdue_days = #{bOverdueDays}</if>
            <if test="bOverdueAmount != null "> and B_overdue_amount = #{bOverdueAmount}</if>
            <if test="dOverdueDays != null "> and D_overdue_days = #{dOverdueDays}</if>
            <if test="dOverdueAmount != null "> and D_overdue_amount = #{dOverdueAmount}</if>
            <if test="bRepaymentDate != null "> and B_repayment_date = #{bRepaymentDate}</if>
            <if test="dRepaymentDate != null "> and D_repayment_date = #{dRepaymentDate}</if>
            <if test="bPeriods != null  and bPeriods != ''"> and B_periods = #{bPeriods}</if>
            <if test="dPeriods != null  and dPeriods != ''"> and D_periods = #{dPeriods}</if>
            <if test="bRemainingAmounts != null "> and B_remaining_amounts = #{bRemainingAmounts}</if>
            <if test="dRemainingAmounts != null "> and D_remaining_amounts = #{dRemainingAmounts}</if>
            <if test="bCurrentPeriods != null  and bCurrentPeriods != ''"> and B_current_periods = #{bCurrentPeriods}</if>
            <if test="dCurrentPeriods != null  and dCurrentPeriods != ''"> and D_current_periods = #{dCurrentPeriods}</if>
            <if test="bRepaymentAmounts != null "> and B_repayment_amounts = #{bRepaymentAmounts}</if>
            <if test="dRepaymentAmounts != null "> and D_repayment_amounts = #{dRepaymentAmounts}</if>
            <if test="bNowMoney != null "> and B_now_money = #{bNowMoney}</if>
            <if test="dNowMoney != null "> and D_now_money = #{dNowMoney}</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone = #{mobilePhone}</if>
            <if test="plateNo != null  and plateNo != ''"> and plate_no = #{plateNo}</if>
            <if test="orgName != null  and orgName != ''"> and org_name = #{orgName}</if>
            <if test="officeName != null  and officeName != ''"> and office_name = #{officeName}</if>
            <if test="repaymentStatus != null  and repaymentStatus != ''"> and FIND_IN_SET( repayment_status, #{repaymentStatus})</if>
        </where>
    </select>

    <select id="selectvw_loan_infoById" parameterType="String" resultMap="vw_loan_infoResult">
        <include refid="selectvw_loan_infoVo"/>
        where id = #{id}
    </select>

    <insert id="insertvw_loan_info" parameterType="vw_loan_info">
        insert into vw_loan_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="urgeName != null">urge_name,</if>
            <if test="applyId != null">apply_id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="partnerId != null">partner_id,</if>
            <if test="dhUser != null">dh_user,</if>
            <if test="followUp != null">follow_up,</if>
            <if test="urgeUser != null">urge_user,</if>
            <if test="petitionUser != null">petition_user,</if>
            <if test="lawUser != null">law_user,</if>
            <if test="period != null">period,</if>
            <if test="realReturnMoney != null">real_return_money,</if>
            <if test="reminderDate != null">reminder_date,</if>
            <if test="isPetition != null">is_petition,</if>
            <if test="badDebt != null">bad_debt,</if>
            <if test="status != null">status,</if>
            <if test="billStatus != null">bill_status,</if>
            <if test="slippageStatus != null">slippage_status,</if>
            <if test="followStatus != null">follow_status,</if>
            <if test="isExtension != null">is_extension,</if>
            <if test="extensionDate != null">extension_date,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="contractAmt != null">contract_amt,</if>
            <if test="fOverdueAmount != null">F_overdue_amount,</if>
            <if test="bOverdueDays != null">B_overdue_days,</if>
            <if test="bOverdueAmount != null">B_overdue_amount,</if>
            <if test="dOverdueDays != null">D_overdue_days,</if>
            <if test="dOverdueAmount != null">D_overdue_amount,</if>
            <if test="bRepaymentDate != null">B_repayment_date,</if>
            <if test="dRepaymentDate != null">D_repayment_date,</if>
            <if test="bPeriods != null">B_periods,</if>
            <if test="dPeriods != null">D_periods,</if>
            <if test="bRemainingAmounts != null">B_remaining_amounts,</if>
            <if test="dRemainingAmounts != null">D_remaining_amounts,</if>
            <if test="bCurrentPeriods != null">B_current_periods,</if>
            <if test="dCurrentPeriods != null">D_current_periods,</if>
            <if test="bRepaymentAmounts != null">B_repayment_amounts,</if>
            <if test="dRepaymentAmounts != null">D_repayment_amounts,</if>
            <if test="bNowMoney != null">B_now_money,</if>
            <if test="dNowMoney != null">D_now_money,</if>
            <if test="mobilePhone != null">mobile_phone,</if>
            <if test="plateNo != null">plate_no,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="urgeName != null">#{urgeName},</if>
            <if test="applyId != null">#{applyId},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="partnerId != null">#{partnerId},</if>
            <if test="dhUser != null">#{dhUser},</if>
            <if test="followUp != null">#{followUp},</if>
            <if test="urgeUser != null">#{urgeUser},</if>
            <if test="petitionUser != null">#{petitionUser},</if>
            <if test="lawUser != null">#{lawUser},</if>
            <if test="period != null">#{period},</if>
            <if test="realReturnMoney != null">#{realReturnMoney},</if>
            <if test="reminderDate != null">#{reminderDate},</if>
            <if test="isPetition != null">#{isPetition},</if>
            <if test="badDebt != null">#{badDebt},</if>
            <if test="status != null">#{status},</if>
            <if test="billStatus != null">#{billStatus},</if>
            <if test="slippageStatus != null">#{slippageStatus},</if>
            <if test="followStatus != null">#{followStatus},</if>
            <if test="isExtension != null">#{isExtension},</if>
            <if test="extensionDate != null">#{extensionDate},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="contractAmt != null">#{contractAmt},</if>
            <if test="fOverdueAmount != null">#{fOverdueAmount},</if>
            <if test="bOverdueDays != null">#{bOverdueDays},</if>
            <if test="bOverdueAmount != null">#{bOverdueAmount},</if>
            <if test="dOverdueDays != null">#{dOverdueDays},</if>
            <if test="dOverdueAmount != null">#{dOverdueAmount},</if>
            <if test="bRepaymentDate != null">#{bRepaymentDate},</if>
            <if test="dRepaymentDate != null">#{dRepaymentDate},</if>
            <if test="bPeriods != null">#{bPeriods},</if>
            <if test="dPeriods != null">#{dPeriods},</if>
            <if test="bRemainingAmounts != null">#{bRemainingAmounts},</if>
            <if test="dRemainingAmounts != null">#{dRemainingAmounts},</if>
            <if test="bCurrentPeriods != null">#{bCurrentPeriods},</if>
            <if test="dCurrentPeriods != null">#{dCurrentPeriods},</if>
            <if test="bRepaymentAmounts != null">#{bRepaymentAmounts},</if>
            <if test="dRepaymentAmounts != null">#{dRepaymentAmounts},</if>
            <if test="bNowMoney != null">#{bNowMoney},</if>
            <if test="dNowMoney != null">#{dNowMoney},</if>
            <if test="mobilePhone != null">#{mobilePhone},</if>
            <if test="plateNo != null">#{plateNo},</if>
        </trim>
    </insert>

    <update id="updatevw_loan_info" parameterType="vw_loan_info">
        update vw_loan_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="urgeName != null">urge_name = #{urgeName},</if>
            <if test="applyId != null">apply_id = #{applyId},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="partnerId != null">partner_id = #{partnerId},</if>
            <if test="dhUser != null">dh_user = #{dhUser},</if>
            <if test="followUp != null">follow_up = #{followUp},</if>
            <if test="urgeUser != null">urge_user = #{urgeUser},</if>
            <if test="petitionUser != null">petition_user = #{petitionUser},</if>
            <if test="lawUser != null">law_user = #{lawUser},</if>
            <if test="period != null">period = #{period},</if>
            <if test="realReturnMoney != null">real_return_money = #{realReturnMoney},</if>
            <if test="reminderDate != null">reminder_date = #{reminderDate},</if>
            <if test="isPetition != null">is_petition = #{isPetition},</if>
            <if test="badDebt != null">bad_debt = #{badDebt},</if>
            <if test="status != null">status = #{status},</if>
            <if test="billStatus != null">bill_status = #{billStatus},</if>
            <if test="slippageStatus != null">slippage_status = #{slippageStatus},</if>
            <if test="followStatus != null">follow_status = #{followStatus},</if>
            <if test="isExtension != null">is_extension = #{isExtension},</if>
            <if test="extensionDate != null">extension_date = #{extensionDate},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="fOverdueAmount != null">F_overdue_amount = #{fOverdueAmount},</if>
            <if test="bOverdueDays != null">B_overdue_days = #{bOverdueDays},</if>
            <if test="bOverdueAmount != null">B_overdue_amount = #{bOverdueAmount},</if>
            <if test="dOverdueDays != null">D_overdue_days = #{dOverdueDays},</if>
            <if test="dOverdueAmount != null">D_overdue_amount = #{dOverdueAmount},</if>
            <if test="bRepaymentDate != null">B_repayment_date = #{bRepaymentDate},</if>
            <if test="dRepaymentDate != null">D_repayment_date = #{dRepaymentDate},</if>
            <if test="bPeriods != null">B_periods = #{bPeriods},</if>
            <if test="dPeriods != null">D_periods = #{dPeriods},</if>
            <if test="bRemainingAmounts != null">B_remaining_amounts = #{bRemainingAmounts},</if>
            <if test="dRemainingAmounts != null">D_remaining_amounts = #{dRemainingAmounts},</if>
            <if test="bCurrentPeriods != null">B_current_periods = #{bCurrentPeriods},</if>
            <if test="dCurrentPeriods != null">D_current_periods = #{dCurrentPeriods},</if>
            <if test="bRepaymentAmounts != null">B_repayment_amounts = #{bRepaymentAmounts},</if>
            <if test="dRepaymentAmounts != null">D_repayment_amounts = #{dRepaymentAmounts},</if>
            <if test="bNowMoney != null">B_now_money = #{bNowMoney},</if>
            <if test="dNowMoney != null">D_now_money = #{dNowMoney},</if>
            <if test="mobilePhone != null">mobile_phone = #{mobilePhone},</if>
            <if test="plateNo != null">plate_no = #{plateNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletevw_loan_infoById" parameterType="String">
        delete from vw_loan_info where id = #{id}
    </delete>

    <delete id="deletevw_loan_infoByIds" parameterType="String">
        delete from vw_loan_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>