package com.ruoyi.car_order.service;

import java.util.List;
import com.ruoyi.car_order.domain.CarOrder;

/**
 * 找车订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface ICarOrderService 
{
    /**
     * 查询找车订单
     * 
     * @param id 找车订单主键
     * @return 找车订单
     */
    public CarOrder selectCarOrderById(String id);

    /**
     * 查询找车订单列表
     * 
     * @param carOrder 找车订单
     * @return 找车订单集合
     */
    public List<CarOrder> selectCarOrderList(CarOrder carOrder);

    /**
     * 新增找车订单
     * 
     * @param carOrder 找车订单
     * @return 结果
     */
    public int insertCarOrder(CarOrder carOrder);

    /**
     * 修改找车订单
     * 
     * @param carOrder 找车订单
     * @return 结果
     */
    public int updateCarOrder(CarOrder carOrder);

    /**
     * 批量删除找车订单
     * 
     * @param ids 需要删除的找车订单主键集合
     * @return 结果
     */
    public int deleteCarOrderByIds(String[] ids);

    /**
     * 删除找车订单信息
     * 
     * @param id 找车订单主键
     * @return 结果
     */
    public int deleteCarOrderById(String id);

    /**
     * 根据loanId更新dispatcher
     * @param loanId 流程id
     * @param dispatcher 派单员类型
     */
    void updateDispatcherByLoanId(Long loanId, Integer dispatcher);
}
