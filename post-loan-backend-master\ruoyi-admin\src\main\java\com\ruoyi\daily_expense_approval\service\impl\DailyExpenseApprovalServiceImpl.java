package com.ruoyi.daily_expense_approval.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.daily_expense_approval.mapper.DailyExpenseApprovalMapper;
import com.ruoyi.daily_expense_approval.domain.DailyExpenseApproval;
import com.ruoyi.daily_expense_approval.service.IDailyExpenseApprovalService;
import com.ruoyi.system.service.ISysUserService;

/**
 * 日常花费审批Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class DailyExpenseApprovalServiceImpl implements IDailyExpenseApprovalService 
{
    @Autowired
    private DailyExpenseApprovalMapper dailyExpenseApprovalMapper;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 查询日常花费审批
     * 
     * @param id 日常花费审批主键
     * @return 日常花费审批
     */
    @Override
    public DailyExpenseApproval selectDailyExpenseApprovalById(Long id)
    {
        return dailyExpenseApprovalMapper.selectDailyExpenseApprovalById(id);
    }

    /**
     * 查询日常花费审批列表
     * 
     * @param dailyExpenseApproval 日常花费审批
     * @return 日常花费审批
     */
    @Override
    public List<DailyExpenseApproval> selectDailyExpenseApprovalList(DailyExpenseApproval dailyExpenseApproval)
    {
        return dailyExpenseApprovalMapper.selectDailyExpenseApprovalList(dailyExpenseApproval);
    }

    /**
     * 新增日常花费审批
     *
     * @param dailyExpenseApproval 日常花费审批
     * @return 结果
     */
    @Override
    public int insertDailyExpenseApproval(DailyExpenseApproval dailyExpenseApproval)
    {
        // 自动填充申请人信息
        String username = SecurityUtils.getUsername();
        if (dailyExpenseApproval.getApplicantId() == null || dailyExpenseApproval.getApplicantId().isEmpty()) {
            dailyExpenseApproval.setApplicantId(username);
        }
        if (dailyExpenseApproval.getApplicantName() == null || dailyExpenseApproval.getApplicantName().isEmpty()) {
            String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
            dailyExpenseApproval.setApplicantName(nickName);
        }

        // 设置默认审批状态为待审批
        if (dailyExpenseApproval.getApprovalStatus() == null) {
            dailyExpenseApproval.setApprovalStatus("0"); // 0-待审批
        }

        // 设置申请时间
        if (dailyExpenseApproval.getApplicationTime() == null) {
            dailyExpenseApproval.setApplicationTime(DateUtils.getNowDate());
        }

        dailyExpenseApproval.setCreateTime(DateUtils.getNowDate());
        dailyExpenseApproval.setCreateBy(username);
        return dailyExpenseApprovalMapper.insertDailyExpenseApproval(dailyExpenseApproval);
    }

    /**
     * 修改日常花费审批
     * 
     * @param dailyExpenseApproval 日常花费审批
     * @return 结果
     */
    @Override
    public int updateDailyExpenseApproval(DailyExpenseApproval dailyExpenseApproval)
    {
        dailyExpenseApproval.setUpdateTime(DateUtils.getNowDate());
        return dailyExpenseApprovalMapper.updateDailyExpenseApproval(dailyExpenseApproval);
    }

    /**
     * 批量删除日常花费审批
     * 
     * @param ids 需要删除的日常花费审批主键
     * @return 结果
     */
    @Override
    public int deleteDailyExpenseApprovalByIds(Long[] ids)
    {
        return dailyExpenseApprovalMapper.deleteDailyExpenseApprovalByIds(ids);
    }

    /**
     * 删除日常花费审批信息
     *
     * @param id 日常花费审批主键
     * @return 结果
     */
    @Override
    public int deleteDailyExpenseApprovalById(Long id)
    {
        return dailyExpenseApprovalMapper.deleteDailyExpenseApprovalById(id);
    }

    /**
     * 审批日常花费申请
     *
     * @param id 日常花费审批主键
     * @param approvalStatus 审批状态（1-通过，2-拒绝）
     * @param approvalRemark 审批备注
     * @return 结果
     */
    @Override
    public int approveDailyExpense(Long id, String approvalStatus, String approvalRemark)
    {
        DailyExpenseApproval approval = new DailyExpenseApproval();
        approval.setId(id);
        approval.setApprovalStatus(approvalStatus);
        approval.setApprovalRemark(approvalRemark);
        approval.setApprovalTime(DateUtils.getNowDate());

        // 设置审批人信息
        String username = SecurityUtils.getUsername();
        approval.setApproverId(username);
        approval.setApproverName(SecurityUtils.getLoginUser().getUser().getNickName());

        approval.setUpdateTime(DateUtils.getNowDate());
        approval.setUpdateBy(username);

        return dailyExpenseApprovalMapper.updateDailyExpenseApproval(approval);
    }
}
