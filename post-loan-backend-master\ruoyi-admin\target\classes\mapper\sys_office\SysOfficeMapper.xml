<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.sys_office.mapper.SysOfficeMapper">
    
    <resultMap type="SysOffice" id="SysOfficeResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="parentIds"    column="parent_ids"    />
        <result property="name"    column="name"    />
        <result property="loanUser"    column="loan_user"    />
        <result property="legalUser"    column="legal_user"    />
        <result property="loanTime"    column="loan_time"    />
        <result property="legalTime"    column="legal_time"    />
        <result property="status"    column="status"    />
        <result property="sort"    column="sort"    />
        <result property="areaId"    column="area_id"    />
        <result property="code"    column="code"    />
        <result property="type"    column="type"    />
        <result property="grade"    column="grade"    />
        <result property="address"    column="address"    />
        <result property="zipCode"    column="zip_code"    />
        <result property="master"    column="master"    />
        <result property="phone"    column="phone"    />
        <result property="fax"    column="fax"    />
        <result property="email"    column="email"    />
        <result property="useable"    column="useable"    />
        <result property="primaryPerson"    column="primary_person"    />
        <result property="deputyPerson"    column="deputy_person"    />
        <result property="remarks"    column="remarks"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="fullName"    column="full_name"    />
        <result property="courtName"    column="court_name"    />
        <result property="areaName"    column="area_name"    />
        <result property="signFlag"    column="sign_flag"    />
        <result property="gpsScheme"    column="gps_scheme"    />
    </resultMap>

    <sql id="selectSysOfficeVo">
        select * from sys_office
    </sql>

    <select id="selectSysOfficeList" parameterType="SysOffice" resultMap="SysOfficeResult">
        <include refid="selectSysOfficeVo"/>
        <where>  
            <if test="parentId != null  and parentId != ''"> and parent_id = #{parentId}</if>
            <if test="parentIds != null  and parentIds != ''"> and parent_ids = #{parentIds}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
             <if test="loanUser != null  and loanUser != ''"> and loan_user = #{loanUser}</if>
              <if test="legalUser != null  and legalUser != ''"> and legal_user = #{legalUser}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="areaId != null  and areaId != ''"> and area_id = #{areaId}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="grade != null  and grade != ''"> and grade = #{grade}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="zipCode != null  and zipCode != ''"> and zip_code = #{zipCode}</if>
            <if test="master != null  and master != ''"> and master = #{master}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="fax != null  and fax != ''"> and fax = #{fax}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="useable != null  and useable != ''"> and useable = #{useable}</if>
            <if test="primaryPerson != null  and primaryPerson != ''"> and primary_person = #{primaryPerson}</if>
            <if test="deputyPerson != null  and deputyPerson != ''"> and deputy_person = #{deputyPerson}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="fullName != null  and fullName != ''"> and full_name like concat('%', #{fullName}, '%')</if>
            <if test="courtName != null  and courtName != ''"> and court_name like concat('%', #{courtName}, '%')</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="signFlag != null  and signFlag != ''"> and sign_flag = #{signFlag}</if>
            <if test="gpsScheme != null  and gpsScheme != ''"> and gps_scheme = #{gpsScheme}</if>
        </where>
    </select>
    
    <select id="selectSysOfficeById" parameterType="String" resultMap="SysOfficeResult">
        <include refid="selectSysOfficeVo"/>
        where id = #{id}
    </select>

    <select id="selectIdAndNameList" resultType="java.util.HashMap">
        SELECT id, name FROM sys_office WHERE del_flag = 0
    </select>

    <insert id="insertSysOffice" parameterType="SysOffice">
        insert into sys_office
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="parentId != null and parentId != ''">parent_id,</if>
            <if test="parentIds != null and parentIds != ''">parent_ids,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="loanUser != null">loan_user,</if>
            <if test="legalUser != null">legal_user,</if>
            <if test="status != null">status,</if>
            <if test="sort != null">sort,</if>
            <if test="areaId != null and areaId != ''">area_id,</if>
            <if test="code != null">code,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="grade != null and grade != ''">grade,</if>
            <if test="address != null">address,</if>
            <if test="zipCode != null">zip_code,</if>
            <if test="master != null">master,</if>
            <if test="phone != null">phone,</if>
            <if test="fax != null">fax,</if>
            <if test="email != null">email,</if>
            <if test="useable != null">useable,</if>
            <if test="primaryPerson != null">primary_person,</if>
            <if test="deputyPerson != null">deputy_person,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="fullName != null">full_name,</if>
            <if test="courtName != null">court_name,</if>
            <if test="areaName != null">area_name,</if>
            <if test="signFlag != null">sign_flag,</if>
            <if test="gpsScheme != null">gps_scheme,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="parentId != null and parentId != ''">#{parentId},</if>
            <if test="parentIds != null and parentIds != ''">#{parentIds},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="loanUser != null">#{loanUser},</if>
            <if test="legalUser != null">#{legalUser},</if>
            <if test="status != null">#{status},</if>
            <if test="sort != null">#{sort},</if>
            <if test="areaId != null and areaId != ''">#{areaId},</if>
            <if test="code != null">#{code},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="grade != null and grade != ''">#{grade},</if>
            <if test="address != null">#{address},</if>
            <if test="zipCode != null">#{zipCode},</if>
            <if test="master != null">#{master},</if>
            <if test="phone != null">#{phone},</if>
            <if test="fax != null">#{fax},</if>
            <if test="email != null">#{email},</if>
            <if test="useable != null">#{useable},</if>
            <if test="primaryPerson != null">#{primaryPerson},</if>
            <if test="deputyPerson != null">#{deputyPerson},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="fullName != null">#{fullName},</if>
            <if test="courtName != null">#{courtName},</if>
            <if test="areaName != null">#{areaName},</if>
            <if test="signFlag != null">#{signFlag},</if>
            <if test="gpsScheme != null">#{gpsScheme},</if>
         </trim>
    </insert>

    <update id="updateSysOffice" parameterType="SysOffice">
        update sys_office
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null and parentId != ''">parent_id = #{parentId},</if>
            <if test="parentIds != null and parentIds != ''">parent_ids = #{parentIds},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="loanUser != null">loan_user = #{loanUser},</if>
            <if test="legalUser != null">legal_user = #{legalUser},</if>
            <if test="loanTime != null">loan_time = #{loanTime},</if>
            <if test="legalTime != null">legal_time = #{legalTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="areaId != null and areaId != ''">area_id = #{areaId},</if>
            <if test="code != null">code = #{code},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="grade != null and grade != ''">grade = #{grade},</if>
            <if test="address != null">address = #{address},</if>
            <if test="zipCode != null">zip_code = #{zipCode},</if>
            <if test="master != null">master = #{master},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="fax != null">fax = #{fax},</if>
            <if test="email != null">email = #{email},</if>
            <if test="useable != null">useable = #{useable},</if>
            <if test="primaryPerson != null">primary_person = #{primaryPerson},</if>
            <if test="deputyPerson != null">deputy_person = #{deputyPerson},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="fullName != null">full_name = #{fullName},</if>
            <if test="courtName != null">court_name = #{courtName},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="signFlag != null">sign_flag = #{signFlag},</if>
            <if test="gpsScheme != null">gps_scheme = #{gpsScheme},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysOfficeById" parameterType="String">
        delete from sys_office where id = #{id}
    </delete>

    <delete id="deleteSysOfficeByIds" parameterType="String">
        delete from sys_office where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>