package com.ruoyi.bank_account.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.bank_account.mapper.BankAccountMapper;
import com.ruoyi.bank_account.domain.BankAccount;
import com.ruoyi.bank_account.service.IBankAccountService;

/**
 * 银行账户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Service
public class BankAccountServiceImpl implements IBankAccountService 
{
    @Autowired
    private BankAccountMapper bankAccountMapper;

    /**
     * 查询银行账户
     * 
     * @param id 银行账户主键
     * @return 银行账户
     */
    @Override
    public BankAccount selectBankAccountById(String id)
    {
        return bankAccountMapper.selectBankAccountById(id);
    }

    /**
     * 查询银行账户列表
     * 
     * @param bankAccount 银行账户
     * @return 银行账户
     */
    @Override
    public List<BankAccount> selectBankAccountList(BankAccount bankAccount)
    {
        return bankAccountMapper.selectBankAccountList(bankAccount);
    }

    /**
     * 新增银行账户
     * 
     * @param bankAccount 银行账户
     * @return 结果
     */
    @Override
    public int insertBankAccount(BankAccount bankAccount)
    {
        return bankAccountMapper.insertBankAccount(bankAccount);
    }

    /**
     * 修改银行账户
     * 
     * @param bankAccount 银行账户
     * @return 结果
     */
    @Override
    public int updateBankAccount(BankAccount bankAccount)
    {
        return bankAccountMapper.updateBankAccount(bankAccount);
    }

    /**
     * 批量删除银行账户
     * 
     * @param ids 需要删除的银行账户主键
     * @return 结果
     */
    @Override
    public int deleteBankAccountByIds(String[] ids)
    {
        return bankAccountMapper.deleteBankAccountByIds(ids);
    }

    /**
     * 删除银行账户信息
     * 
     * @param id 银行账户主键
     * @return 结果
     */
    @Override
    public int deleteBankAccountById(String id)
    {
        return bankAccountMapper.deleteBankAccountById(id);
    }
}
