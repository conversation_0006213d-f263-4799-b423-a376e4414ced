package com.ruoyi.coborrower_info.service;

import java.util.List;
import com.ruoyi.coborrower_info.domain.CoborrowerInfo;

/**
 * 担保人/共借人Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface ICoborrowerInfoService 
{
    /**
     * 查询担保人/共借人
     * 
     * @param id 担保人/共借人主键
     * @return 担保人/共借人
     */
    public CoborrowerInfo selectCoborrowerInfoById(String id);

    /**
     * 查询担保人/共借人列表
     * 
     * @param coborrowerInfo 担保人/共借人
     * @return 担保人/共借人集合
     */
    public List<CoborrowerInfo> selectCoborrowerInfoList(CoborrowerInfo coborrowerInfo);

    /**
     * 新增担保人/共借人
     * 
     * @param coborrowerInfo 担保人/共借人
     * @return 结果
     */
    public int insertCoborrowerInfo(CoborrowerInfo coborrowerInfo);

    /**
     * 修改担保人/共借人
     * 
     * @param coborrowerInfo 担保人/共借人
     * @return 结果
     */
    public int updateCoborrowerInfo(CoborrowerInfo coborrowerInfo);

    /**
     * 批量删除担保人/共借人
     * 
     * @param ids 需要删除的担保人/共借人主键集合
     * @return 结果
     */
    public int deleteCoborrowerInfoByIds(String[] ids);

    /**
     * 删除担保人/共借人信息
     * 
     * @param id 担保人/共借人主键
     * @return 结果
     */
    public int deleteCoborrowerInfoById(String id);
}
