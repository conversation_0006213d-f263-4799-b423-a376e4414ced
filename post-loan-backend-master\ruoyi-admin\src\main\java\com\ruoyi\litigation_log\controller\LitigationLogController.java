package com.ruoyi.litigation_log.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.litigation_log.domain.LitigationLog;
import com.ruoyi.litigation_log.service.ILitigationLogService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 法诉日志Controller
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@RestController
@RequestMapping("/litigation_log/litigation_log")
public class LitigationLogController extends BaseController
{
    @Autowired
    private ILitigationLogService litigationLogService;

    /**
     * 查询法诉日志列表
     */
    @PreAuthorize("@ss.hasPermi('litigation_log:litigation_log:list')")
    @GetMapping("/list")
    public TableDataInfo list(LitigationLog litigationLog)
    {
        startPage();
        List<LitigationLog> list = litigationLogService.selectLitigationLogList(litigationLog);
        return getDataTable(list);
    }

    /**
     * 导出法诉日志列表
     */
    @PreAuthorize("@ss.hasPermi('litigation_log:litigation_log:export')")
    @Log(title = "法诉日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LitigationLog litigationLog)
    {
        List<LitigationLog> list = litigationLogService.selectLitigationLogList(litigationLog);
        ExcelUtil<LitigationLog> util = new ExcelUtil<LitigationLog>(LitigationLog.class);
        util.exportExcel(response, list, "法诉日志数据");
    }

    /**
     * 获取法诉日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('litigation_log:litigation_log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(litigationLogService.selectLitigationLogById(id));
    }

    /**
     * 新增法诉日志
     */
    @PreAuthorize("@ss.hasPermi('litigation_log:litigation_log:add')")
    @Log(title = "法诉日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LitigationLog litigationLog)
    {
        return toAjax(litigationLogService.insertLitigationLog(litigationLog));
    }

    /**
     * 修改法诉日志
     */
    @PreAuthorize("@ss.hasPermi('litigation_log:litigation_log:edit')")
    @Log(title = "法诉日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LitigationLog litigationLog)
    {
        return toAjax(litigationLogService.updateLitigationLog(litigationLog));
    }

    /**
     * 删除法诉日志
     */
    @PreAuthorize("@ss.hasPermi('litigation_log:litigation_log:remove')")
    @Log(title = "法诉日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(litigationLogService.deleteLitigationLogByIds(ids));
    }
}
