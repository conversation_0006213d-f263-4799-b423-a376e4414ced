package com.ruoyi.system.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.LhTrialRepayMapper;
import com.ruoyi.system.domain.LhTrialRepay;
import com.ruoyi.system.service.ILhTrialRepayService;

/**
 * 蓝海还款试算Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class LhTrialRepayServiceImpl implements ILhTrialRepayService 
{
    @Autowired
    private LhTrialRepayMapper lhTrialRepayMapper;

    /**
     * 查询蓝海还款试算
     * 
     * @param id 蓝海还款试算主键
     * @return 蓝海还款试算
     */
    @Override
    public LhTrialRepay selectLhTrialRepayById(String id)
    {
        return lhTrialRepayMapper.selectLhTrialRepayById(id);
    }

    /**
     * 查询蓝海还款试算列表
     * 
     * @param lhTrialRepay 蓝海还款试算
     * @return 蓝海还款试算
     */
    @Override
    public List<LhTrialRepay> selectLhTrialRepayList(LhTrialRepay lhTrialRepay)
    {
        return lhTrialRepayMapper.selectLhTrialRepayList(lhTrialRepay);
    }

    @Override
    public LhTrialRepay selectLhTrialRepayLists(LhTrialRepay lhTrialRepay)
    {
        return lhTrialRepayMapper.selectLhTrialRepayLists(lhTrialRepay);
    }

    /**
     * 新增蓝海还款试算
     * 
     * @param lhTrialRepay 蓝海还款试算
     * @return 结果
     */
    @Override
    public int insertLhTrialRepay(LhTrialRepay lhTrialRepay)
    {
        return lhTrialRepayMapper.insertLhTrialRepay(lhTrialRepay);
    }

    /**
     * 修改蓝海还款试算
     * 
     * @param lhTrialRepay 蓝海还款试算
     * @return 结果
     */
    @Override
    public int updateLhTrialRepay(LhTrialRepay lhTrialRepay)
    {
        return lhTrialRepayMapper.updateLhTrialRepay(lhTrialRepay);
    }

    /**
     * 批量删除蓝海还款试算
     * 
     * @param ids 需要删除的蓝海还款试算主键
     * @return 结果
     */
    @Override
    public int deleteLhTrialRepayByIds(String[] ids)
    {
        return lhTrialRepayMapper.deleteLhTrialRepayByIds(ids);
    }

    /**
     * 删除蓝海还款试算信息
     * 
     * @param id 蓝海还款试算主键
     * @return 结果
     */
    @Override
    public int deleteLhTrialRepayById(String id)
    {
        return lhTrialRepayMapper.deleteLhTrialRepayById(id);
    }
}
