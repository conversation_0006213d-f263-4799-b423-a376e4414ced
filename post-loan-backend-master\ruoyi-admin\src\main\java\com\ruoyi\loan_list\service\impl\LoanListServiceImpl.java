package com.ruoyi.loan_list.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.loan_list.mapper.LoanListMapper;
import com.ruoyi.loan_list.domain.LoanList;
import com.ruoyi.loan_list.service.ILoanListService;

/**
 * 贷后逾期记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
public class LoanListServiceImpl implements ILoanListService {
    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(LoanListServiceImpl.class);
    @Autowired
    private LoanListMapper loanListMapper;

    /**
     * 查询贷后逾期记录
     * 
     * @param id 贷后逾期记录主键
     * @return 贷后逾期记录
     */
    @Override
    public LoanList selectLoanListById(String id) {
        return loanListMapper.selectLoanListById(id);
    }

    /**
     * 查询贷后逾期记录列表
     * 
     * @param loanList 贷后逾期记录
     * @return 贷后逾期记录
     */
    @Override
    public List<LoanList> selectLoanListList(LoanList loanList) {
        return loanListMapper.selectLoanListList(loanList);
    }

    /**
     * 新增贷后逾期记录
     * 
     * @param loanList 贷后逾期记录
     * @return 结果
     */
    @Override
    public int insertLoanList(LoanList loanList) {
        loanList.setCreateTime(DateUtils.getNowDate());
        return loanListMapper.insertLoanList(loanList);
    }

    /**
     * 修改贷后逾期记录
     * 
     * @param loanList 贷后逾期记录
     * @return 结果
     */
    @Override
    public int updateLoanList(LoanList loanList) {
        loanList.setUpdateTime(DateUtils.getNowDate());
        return loanListMapper.updateLoanList(loanList);
    }

    /**
     * 批量删除贷后逾期记录
     * 
     * @param ids 需要删除的贷后逾期记录主键
     * @return 结果
     */
    @Override
    public int deleteLoanListByIds(String[] ids) {
        return loanListMapper.deleteLoanListByIds(ids);
    }

    /**
     * 删除贷后逾期记录信息
     * 
     * @param id 贷后逾期记录主键
     * @return 结果
     */
    @Override
    public int deleteLoanListById(String id) {
        return loanListMapper.deleteLoanListById(id);
    }

    @Override
    public int batchUpdateUrgeUser(java.util.List<String> ids, String urgeUser) {
        return loanListMapper.batchUpdateUrgeUser(ids, urgeUser);
    }

    @Override
    public int batchAssignPetitionUser(java.util.List<String> ids, String petitionUser) {
        // 查询所有id的petitionAssignmentType
        List<LoanList> list = loanListMapper.selectPetitionAssignmentTypesByIds(ids);
        logger.info("list: {}", list);
        // 检查是否全部为1或2
        boolean allAssignable = list.stream().allMatch(l -> l.getPetitionAssignmentType() != null && (l.getPetitionAssignmentType() == 1 || l.getPetitionAssignmentType() == 2));
        if (!allAssignable) {
            // 返回0表示未执行任何指派
            return 0;
        }
        return loanListMapper.batchAssignPetitionUser(ids, petitionUser, new java.util.Date(), 3); // 3=已指派
    }

    @Override
    public int revokePetitionUser(String id) {
        return loanListMapper.revokePetitionUser(id, null, null, 2); // 2=已撤销
    }
}
