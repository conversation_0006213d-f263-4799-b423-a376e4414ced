package com.ruoyi.partner_info.service;

import java.util.List;
import com.ruoyi.partner_info.domain.PartnerInfo;

/**
 * 资金方管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface IPartnerInfoService 
{
    /**
     * 查询资金方管理
     * 
     * @param id 资金方管理主键
     * @return 资金方管理
     */
    public PartnerInfo selectPartnerInfoById(String id);

    /**
     * 查询资金方管理列表
     * 
     * @param partnerInfo 资金方管理
     * @return 资金方管理集合
     */
    public List<PartnerInfo> selectPartnerInfoList(PartnerInfo partnerInfo);

    /**
     * 新增资金方管理
     * 
     * @param partnerInfo 资金方管理
     * @return 结果
     */
    public int insertPartnerInfo(PartnerInfo partnerInfo);

    /**
     * 修改资金方管理
     * 
     * @param partnerInfo 资金方管理
     * @return 结果
     */
    public int updatePartnerInfo(PartnerInfo partnerInfo);

    /**
     * 批量删除资金方管理
     * 
     * @param ids 需要删除的资金方管理主键集合
     * @return 结果
     */
    public int deletePartnerInfoByIds(String[] ids);

    /**
     * 删除资金方管理信息
     * 
     * @param id 资金方管理主键
     * @return 结果
     */
    public int deletePartnerInfoById(String id);

    /**
     * 查询所有资金方的id和orgName
     * @return id和orgName列表
     */
    List<java.util.Map<String, Object>> selectPartnerInfoIdAndNameList();
}
