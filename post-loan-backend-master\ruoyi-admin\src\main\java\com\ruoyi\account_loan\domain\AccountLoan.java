package com.ruoyi.account_loan.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 贷款-贷款信息对象 account_loan
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
public class AccountLoan extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 流水号 */
    private String id;

    //// B_overdue_days 银行逾期天数
    // private Long B_overdue_days;
    //
    //// B_overdue_amount 银行逾期金额
    // private BigDecimal B_overdue_amount;
    //
    // // D_overdue_days 代扣逾期天数
    // private Long D_overdue_days;
    //
    // // D_overdue_amount 代扣逾期金额
    // private BigDecimal D_overdue_amount;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyId;

    /** 出账编号 */
    @Excel(name = "出账编号")
    private String putoutId;

    /** 合同编号 */
    @Excel(name = "合同编号")
    private String contractId;

    /** 客户编号 */
    @Excel(name = "客户编号")
    private String customerId;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 贷款状态（Code:LoanStatus） */
    @Excel(name = "贷款状态", readConverterExp = "C=ode:LoanStatus")
    private String loanStatus;

    /**
     * 贷后还款状态
     * 1-还款中，2-已完结，3-提前结清，4-逾期催回结清，5-逾期减免结清，6-逾期未还款，7-逾期还款中，8-代偿未还款，9-代偿还款中，10-代偿减免结清，11-代偿全额结清
     */
    @Excel(name = "贷后还款状态", readConverterExp = "1=还款中,2=已完结,3=提前结清,4=逾期催回结清,5=逾期减免结清,6=逾期未还款,7=逾期还款中,8=代偿未还款,9=代偿还款中,10=代偿减免结清,11=代偿全额结清")
    private String repaymentStatus;

    /** 发生类型（Code:OccurType） */
    @Excel(name = "发生类型", readConverterExp = "C=ode:OccurType")
    private String occurType;

    /** 产品编号 */
    @Excel(name = "产品编号")
    private String productId;

    /** 资金方Id */
    @Excel(name = "资金方Id")
    private String partnerId;

    /** 币种 */
    @Excel(name = "币种")
    private String currency;

    /** 开卡金额 */
    @Excel(name = "开卡金额")
    private BigDecimal businessSum;

    /** 放款金额 */
    @Excel(name = "放款金额")
    private BigDecimal contractAmt;

    /** 放款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "放款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date putoutDate;

    /** 账单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "账单日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date billDate;

    /** 首次还款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "首次还款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstRepayDate;

    /** 上期还款日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上期还款日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastDueDate;

    /** 本期还款日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "本期还款日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date nextDueDate;

    /** 到期日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到期日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date maturityDate;

    /** 期限 */
    @Excel(name = "期限")
    private Long term;

    /** 贷款期限单位 */
    @Excel(name = "贷款期限单位")
    private String termUnit;

    /** 还清本息日期（不含罚复息部分） */
    @Excel(name = "还清本息日期", readConverterExp = "不=含罚复息部分")
    private Date settleDate;

    /** 结清日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结清日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date finishDate;

    /** 贷款原始到期日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "贷款原始到期日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date originalMaturityDate;

    /** 贷款利率功能组件编号（Term:RAT） */
    @Excel(name = "贷款利率功能组件编号", readConverterExp = "T=erm:RAT")
    private String rateTermId;

    /** 主还款方式功能组件编号（Term:RPT） */
    @Excel(name = "主还款方式功能组件编号", readConverterExp = "T=erm:RPT")
    private String rptTermId;

    /** 罚息利率组件 */
    @Excel(name = "罚息利率组件")
    private String finTermId;

    /** 费用组件 */
    @Excel(name = "费用组件")
    private String feeTermId;

    /** 本金余额 */
    @Excel(name = "本金余额")
    private BigDecimal normalBalance;

    /** 月供 */
    @Excel(name = "月供")
    private BigDecimal nextInstalmentAmt;

    /** 当前期数 */
    @Excel(name = "当前期数")
    private Long currentPeriod;

    /** 当期本金 */
    @Excel(name = "当期本金")
    private BigDecimal currentBalance;

    /** 当期利息 */
    @Excel(name = "当期利息")
    private BigDecimal currenctinteBalance;

    /** 逾期本金 */
    @Excel(name = "逾期本金")
    private BigDecimal overdueBalance;

    /** 逾期利息 */
    @Excel(name = "逾期利息")
    private BigDecimal odinteBalance;

    /** 罚息 */
    @Excel(name = "罚息")
    private BigDecimal fineinteBalance;

    /** 复利 */
    @Excel(name = "复利")
    private BigDecimal compdinteBalance;

    /** 逾期金额 */
    @Excel(name = "逾期金额")
    private BigDecimal overdueAmt;

    /** 逾期天数 */
    @Excel(name = "逾期天数")
    private Long overdueDays;

    /** 已还期数 */
    @Excel(name = "已还期数")
    private Long lcaTimes;

    /** 剩余还款期数 */
    @Excel(name = "剩余还款期数")
    private Long totalPeriod;

    /** 宽限期利息余额 */
    @Excel(name = "宽限期利息余额")
    private BigDecimal graceinteBalance;

    /** 正常计提利息 */
    @Excel(name = "正常计提利息")
    private BigDecimal accrueinteBalance;

    /** 利率调整方式（Code:RepriceType） */
    @Excel(name = "利率调整方式", readConverterExp = "C=ode:RepriceType")
    private String repriceType;

    /** 利率调整周期单位(Code:TermUnit) */
    @Excel(name = "利率调整周期单位(Code:TermUnit)")
    private String repriceFlag;

    /** 利率调整周期(自定义重定价日期用) */
    @Excel(name = "利率调整周期(自定义重定价日期用)")
    private Long repriceCycle;

    /** 首次利率调整日期(自定义重定价日期用) */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "首次利率调整日期(自定义重定价日期用)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date repriceDate;

    /** 上次利率调整日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上次利率调整日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastRepriceDate;

    /** 下次利率调整日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "下次利率调整日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date nextRepriceDate;

    /** 逾期宽限期天数 */
    @Excel(name = "逾期宽限期天数")
    private Long graceDays;

    /** 贷款逾期起点标识 */
    @Excel(name = "贷款逾期起点标识")
    private String loanOverDateFlag;

    /** 节假日是否顺延（Code:CanlendarType，可多选，例如A||B标识满足之一即顺延） */
    @Excel(name = "节假日是否顺延", readConverterExp = "C=ode:CanlendarType，可多选，例如A||B标识满足之一即顺延")
    private String holidayPaymentFlag;

    /** 批扣标识 */
    @Excel(name = "批扣标识")
    private String autoPayFlag;

    /** 计提类型范围 */
    @Excel(name = "计提类型范围")
    private String interestTypeFlag;

    /** 五级分类结果 */
    @Excel(name = "五级分类结果")
    private String classifyResult;

    /** 扣款批次号 */
    @Excel(name = "扣款批次号")
    private String batchNo;

    /** 锁定标示（1 日终锁定 2 未锁定 3 日初锁定 4 批扣锁定） */
    @Excel(name = "锁定标示", readConverterExp = "1=,日=终锁定,2=,未=锁定,3=,日=初锁定,4=,批=扣锁定")
    private String lockFlag;

    /** 贷款处理日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "贷款处理日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date businessDate;

    /** 出账利率（放款固化） */
    @Excel(name = "出账利率", readConverterExp = "放=款固化")
    private BigDecimal putoutRate;

    /** 出账还款方式（放款固化） */
    @Excel(name = "出账还款方式", readConverterExp = "放=款固化")
    private String putoutPaymentMethod;

    /** 执行利率 */
    @Excel(name = "执行利率")
    private BigDecimal loanRate;

    /** 执行费率 */
    @Excel(name = "执行费率")
    private BigDecimal managementFeeRate;

    /** 生成还款计划标识 */
    @Excel(name = "生成还款计划标识")
    private String paymentFlag;

    /** 绩效状态:0未执行1已执行 */
    @Excel(name = "绩效状态:0未执行1已执行")
    private String performanceStatus;

    /** 贷款机构 */
    @Excel(name = "贷款机构")
    private String orgId;

    /** 经办部门 */
    @Excel(name = "经办部门")
    private String officeId;

    /** 用户Id */
    @Excel(name = "用户Id")
    private String userId;

    /** 业务员Id */
    @Excel(name = "业务员Id")
    private String mangerId;

    /**
     * 逾期分配状态(Allot_Status:10:逾期分配;20:电催;25:电催审核分配;30:电催审核;35:上访分配;40:上访;45:法务援助分配;50:法务援助;60:法律诉讼;99:跟踪结束)
     */
    @Excel(name = "逾期分配状态(Allot_Status:10:逾期分配;20:电催;25:电催审核分配;30:电催审核;35:上访分配;40:上访;45:法务援助分配;50:法务援助;60:法律诉讼;99:跟踪结束)")
    private String allotStatus;

    /** 逾期分配用户 */
    @Excel(name = "逾期分配用户")
    private String allotAssignee;

    /** 诉讼状态(litigationStatus) */
    @Excel(name = "诉讼状态(litigationStatus)")
    private String litigationStatus;

    /** 提交方式(Sub_Type) */
    @Excel(name = "提交方式(Sub_Type)")
    private String subType;

    /** 号码状态(Number_Status) */
    @Excel(name = "号码状态(Number_Status)")
    private String numberStatus;

    /** 是否滞后 */
    @Excel(name = "是否滞后")
    private String lagFlag;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 首逾金额 */
    @Excel(name = "首逾金额")
    private BigDecimal fOverdueAmount;

    /** 银行逾期天数 */
    @Excel(name = "银行逾期天数")
    private Long bOverdueDays;

    /** 银行逾期金额 */
    @Excel(name = "银行逾期金额")
    private BigDecimal bOverdueAmount;

    /** 代扣逾期天数 */
    @Excel(name = "代扣逾期天数")
    private Long dOverdueDays;

    /** 代扣逾期金额 */
    @Excel(name = "代扣逾期金额")
    private BigDecimal dOverdueAmount;

    /** 银行还款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "银行还款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date bRepaymentDate;

    /** 代扣还款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "代扣还款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dRepaymentDate;

    /** 银行逾期期数 */
    @Excel(name = "银行逾期期数")
    private String bPeriods;

    /** 代扣逾期期数 */
    @Excel(name = "代扣逾期期数")
    private String dPeriods;

    /** 银行剩余金额 */
    @Excel(name = "银行剩余金额")
    private BigDecimal bRemainingAmounts;

    /** 代扣剩余金额 */
    @Excel(name = "代扣剩余金额")
    private BigDecimal dRemainingAmounts;

    /** 银行本期期数 */
    @Excel(name = "银行本期期数")
    private String bCurrentPeriods;

    /** 代扣本期期数 */
    @Excel(name = "代扣本期期数")
    private String dCurrentPeriods;

    /** 银行已还金额 */
    @Excel(name = "银行已还金额")
    private BigDecimal bRepaymentAmounts;

    /** 代扣已还金额 */
    @Excel(name = "代扣已还金额")
    private BigDecimal dRepaymentAmounts;

    /** 银行本期金额 */
    @Excel(name = "银行本期金额")
    private BigDecimal bNowMoney;

    /** 代扣本期金额 */
    @Excel(name = "代扣本期金额")
    private BigDecimal dNowMoney;

    /** 银行实还时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "银行实还时间", width = 30, dateFormat = "yyyy-MM-dd")
    private String bReturnTime;

    /** 代扣实还时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "代扣实还时间", width = 30, dateFormat = "yyyy-MM-dd")
    private String dReturnTime;

}
