<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.partner_info.mapper.PartnerInfoMapper">
    
    <resultMap type="PartnerInfo" id="PartnerInfoResult">
        <result property="id"    column="id"    />
        <result property="parentOrg"    column="parent_org"    />
        <result property="orgName"    column="org_name"    />
        <result property="orgType"    column="org_type"    />
        <result property="fullDays"    column="full_days"    />
        <result property="orgContact"    column="org_contact"    />
        <result property="contactNumber"    column="contact_number"    />
        <result property="contactCertId"    column="contact_cert_id"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="legalRepresentName"    column="legal_represent_name"    />
        <result property="legalRepresentNumber"    column="legal_represent_number"    />
        <result property="address"    column="address"    />
        <result property="orgStatus"    column="org_status"    />
        <result property="orgCorpsta"    column="org_corpsta"    />
        <result property="businessLicenseNo"    column="business_license_no"    />
        <result property="registerAmt"    column="register_amt"    />
        <result property="businessLicenseRegistno"    column="business_license_registno"    />
        <result property="registerAddress"    column="register_address"    />
        <result property="organizationCertid"    column="organization_certid"    />
        <result property="beginDate"    column="begin_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="limitAmt"    column="limit_amt"    />
        <result property="limitStatus"    column="limit_status"    />
        <result property="limitPeriod"    column="limit_period"    />
        <result property="supportLimitFlag"    column="support_limit_flag"    />
        <result property="supportLimitAmt"    column="support_limit_amt"    />
        <result property="supportLimitPeriod"    column="support_limit_period"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="remarks"    column="remarks"    />
    </resultMap>

    <sql id="selectPartnerInfoVo">
        select id, parent_org, org_name, org_type, full_days, org_contact, contact_number, contact_cert_id, contact_phone, legal_represent_name, legal_represent_number, address, org_status, org_corpsta, business_license_no, register_amt, business_license_registno, register_address, organization_certid, begin_date, end_date, limit_amt, limit_status, limit_period, support_limit_flag, support_limit_amt, support_limit_period, create_by, create_date, update_by, update_date, remarks from partner_info
    </sql>

    <select id="selectPartnerInfoList" parameterType="PartnerInfo" resultMap="PartnerInfoResult">
        <include refid="selectPartnerInfoVo"/>
        <where>  
            <if test="parentOrg != null  and parentOrg != ''"> and parent_org = #{parentOrg}</if>
            <if test="orgName != null  and orgName != ''"> and org_name like concat('%', #{orgName}, '%')</if>
            <if test="orgType != null  and orgType != ''"> and org_type = #{orgType}</if>
            <if test="fullDays != null "> and full_days = #{fullDays}</if>
            <if test="orgContact != null  and orgContact != ''"> and org_contact like concat('%', #{orgContact}, '%')</if>
            <if test="contactNumber != null  and contactNumber != ''"> and contact_number like concat('%', #{contactNumber}, '%')</if>
            <if test="contactCertId != null  and contactCertId != ''"> and contact_cert_id like concat('%', #{contactCertId}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone like concat('%', #{contactPhone}, '%')</if>
            <if test="legalRepresentName != null  and legalRepresentName != ''"> and legal_represent_name like concat('%', #{legalRepresentName}, '%')</if>
            <if test="legalRepresentNumber != null  and legalRepresentNumber != ''"> and legal_represent_number like concat('%', #{legalRepresentNumber}, '%')</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="orgStatus != null  and orgStatus != ''"> and org_status = #{orgStatus}</if>
            <if test="orgCorpsta != null  and orgCorpsta != ''"> and org_corpsta = #{orgCorpsta}</if>
            <if test="businessLicenseNo != null  and businessLicenseNo != ''"> and business_license_no = #{businessLicenseNo}</if>
            <if test="registerAmt != null "> and register_amt = #{registerAmt}</if>
            <if test="businessLicenseRegistno != null  and businessLicenseRegistno != ''"> and business_license_registno = #{businessLicenseRegistno}</if>
            <if test="registerAddress != null  and registerAddress != ''"> and register_address = #{registerAddress}</if>
            <if test="organizationCertid != null  and organizationCertid != ''"> and organization_certid = #{organizationCertid}</if>
            <if test="beginDate != null  and beginDate != ''"> and begin_date = #{beginDate}</if>
            <if test="endDate != null  and endDate != ''"> and end_date = #{endDate}</if>
            <if test="limitAmt != null "> and limit_amt = #{limitAmt}</if>
            <if test="limitStatus != null  and limitStatus != ''"> and limit_status = #{limitStatus}</if>
            <if test="limitPeriod != null  and limitPeriod != ''"> and limit_period = #{limitPeriod}</if>
            <if test="supportLimitFlag != null  and supportLimitFlag != ''"> and support_limit_flag = #{supportLimitFlag}</if>
            <if test="supportLimitAmt != null "> and support_limit_amt = #{supportLimitAmt}</if>
            <if test="supportLimitPeriod != null  and supportLimitPeriod != ''"> and support_limit_period = #{supportLimitPeriod}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
        </where>
    </select>
    
    <select id="selectPartnerInfoById" parameterType="String" resultMap="PartnerInfoResult">
        <include refid="selectPartnerInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertPartnerInfo" parameterType="PartnerInfo">
        insert into partner_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="parentOrg != null">parent_org,</if>
            <if test="orgName != null and orgName != ''">org_name,</if>
            <if test="orgType != null and orgType != ''">org_type,</if>
            <if test="fullDays != null">full_days,</if>
            <if test="orgContact != null">org_contact,</if>
            <if test="contactNumber != null">contact_number,</if>
            <if test="contactCertId != null">contact_cert_id,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="legalRepresentName != null">legal_represent_name,</if>
            <if test="legalRepresentNumber != null">legal_represent_number,</if>
            <if test="address != null">address,</if>
            <if test="orgStatus != null">org_status,</if>
            <if test="orgCorpsta != null">org_corpsta,</if>
            <if test="businessLicenseNo != null">business_license_no,</if>
            <if test="registerAmt != null">register_amt,</if>
            <if test="businessLicenseRegistno != null">business_license_registno,</if>
            <if test="registerAddress != null">register_address,</if>
            <if test="organizationCertid != null">organization_certid,</if>
            <if test="beginDate != null">begin_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="limitAmt != null">limit_amt,</if>
            <if test="limitStatus != null and limitStatus != ''">limit_status,</if>
            <if test="limitPeriod != null">limit_period,</if>
            <if test="supportLimitFlag != null">support_limit_flag,</if>
            <if test="supportLimitAmt != null">support_limit_amt,</if>
            <if test="supportLimitPeriod != null">support_limit_period,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="remarks != null">remarks,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="parentOrg != null">#{parentOrg},</if>
            <if test="orgName != null and orgName != ''">#{orgName},</if>
            <if test="orgType != null and orgType != ''">#{orgType},</if>
            <if test="fullDays != null">#{fullDays},</if>
            <if test="orgContact != null">#{orgContact},</if>
            <if test="contactNumber != null">#{contactNumber},</if>
            <if test="contactCertId != null">#{contactCertId},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="legalRepresentName != null">#{legalRepresentName},</if>
            <if test="legalRepresentNumber != null">#{legalRepresentNumber},</if>
            <if test="address != null">#{address},</if>
            <if test="orgStatus != null">#{orgStatus},</if>
            <if test="orgCorpsta != null">#{orgCorpsta},</if>
            <if test="businessLicenseNo != null">#{businessLicenseNo},</if>
            <if test="registerAmt != null">#{registerAmt},</if>
            <if test="businessLicenseRegistno != null">#{businessLicenseRegistno},</if>
            <if test="registerAddress != null">#{registerAddress},</if>
            <if test="organizationCertid != null">#{organizationCertid},</if>
            <if test="beginDate != null">#{beginDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="limitAmt != null">#{limitAmt},</if>
            <if test="limitStatus != null and limitStatus != ''">#{limitStatus},</if>
            <if test="limitPeriod != null">#{limitPeriod},</if>
            <if test="supportLimitFlag != null">#{supportLimitFlag},</if>
            <if test="supportLimitAmt != null">#{supportLimitAmt},</if>
            <if test="supportLimitPeriod != null">#{supportLimitPeriod},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remarks != null">#{remarks},</if>
         </trim>
    </insert>

    <update id="updatePartnerInfo" parameterType="PartnerInfo">
        update partner_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentOrg != null">parent_org = #{parentOrg},</if>
            <if test="orgName != null and orgName != ''">org_name = #{orgName},</if>
            <if test="orgType != null and orgType != ''">org_type = #{orgType},</if>
            <if test="fullDays != null">full_days = #{fullDays},</if>
            <if test="orgContact != null">org_contact = #{orgContact},</if>
            <if test="contactNumber != null">contact_number = #{contactNumber},</if>
            <if test="contactCertId != null">contact_cert_id = #{contactCertId},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="legalRepresentName != null">legal_represent_name = #{legalRepresentName},</if>
            <if test="legalRepresentNumber != null">legal_represent_number = #{legalRepresentNumber},</if>
            <if test="address != null">address = #{address},</if>
            <if test="orgStatus != null">org_status = #{orgStatus},</if>
            <if test="orgCorpsta != null">org_corpsta = #{orgCorpsta},</if>
            <if test="businessLicenseNo != null">business_license_no = #{businessLicenseNo},</if>
            <if test="registerAmt != null">register_amt = #{registerAmt},</if>
            <if test="businessLicenseRegistno != null">business_license_registno = #{businessLicenseRegistno},</if>
            <if test="registerAddress != null">register_address = #{registerAddress},</if>
            <if test="organizationCertid != null">organization_certid = #{organizationCertid},</if>
            <if test="beginDate != null">begin_date = #{beginDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="limitAmt != null">limit_amt = #{limitAmt},</if>
            <if test="limitStatus != null and limitStatus != ''">limit_status = #{limitStatus},</if>
            <if test="limitPeriod != null">limit_period = #{limitPeriod},</if>
            <if test="supportLimitFlag != null">support_limit_flag = #{supportLimitFlag},</if>
            <if test="supportLimitAmt != null">support_limit_amt = #{supportLimitAmt},</if>
            <if test="supportLimitPeriod != null">support_limit_period = #{supportLimitPeriod},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePartnerInfoById" parameterType="String">
        delete from partner_info where id = #{id}
    </delete>

    <delete id="deletePartnerInfoByIds" parameterType="String">
        delete from partner_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>