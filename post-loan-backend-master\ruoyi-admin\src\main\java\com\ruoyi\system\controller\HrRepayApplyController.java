package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HrRepayApply;
import com.ruoyi.system.service.IHrRepayApplyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 华瑞提前还款Controller
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@RestController
@RequestMapping("/system/hr")
public class HrRepayApplyController extends BaseController
{
    @Autowired
    private IHrRepayApplyService hrRepayApplyService;

    /**
     * 查询华瑞提前还款列表
     */
//    @PreAuthorize("@ss.hasPermi('system:hr:list')")
    @GetMapping("/list")
    public TableDataInfo list(HrRepayApply hrRepayApply)
    {
        startPage();
        List<HrRepayApply> list = hrRepayApplyService.selectHrRepayApplyList(hrRepayApply);
        return getDataTable(list);
    }

    /**
     * 导出华瑞提前还款列表
     */
    @PreAuthorize("@ss.hasPermi('system:hr:export')")
    @Log(title = "华瑞提前还款", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HrRepayApply hrRepayApply)
    {
        List<HrRepayApply> list = hrRepayApplyService.selectHrRepayApplyList(hrRepayApply);
        ExcelUtil<HrRepayApply> util = new ExcelUtil<HrRepayApply>(HrRepayApply.class);
        util.exportExcel(response, list, "华瑞提前还款数据");
    }

    /**
     * 获取华瑞提前还款详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:hr:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(hrRepayApplyService.selectHrRepayApplyById(id));
    }

    /**
     * 新增华瑞提前还款
     */
    @PreAuthorize("@ss.hasPermi('system:hr:add')")
    @Log(title = "华瑞提前还款", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HrRepayApply hrRepayApply)
    {
        return toAjax(hrRepayApplyService.insertHrRepayApply(hrRepayApply));
    }

    /**
     * 修改华瑞提前还款
     */
    @PreAuthorize("@ss.hasPermi('system:hr:edit')")
    @Log(title = "华瑞提前还款", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HrRepayApply hrRepayApply)
    {
        return toAjax(hrRepayApplyService.updateHrRepayApply(hrRepayApply));
    }

    /**
     * 删除华瑞提前还款
     */
    @PreAuthorize("@ss.hasPermi('system:hr:remove')")
    @Log(title = "华瑞提前还款", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(hrRepayApplyService.deleteHrRepayApplyByIds(ids));
    }
}
