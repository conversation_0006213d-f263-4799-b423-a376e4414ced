package com.ruoyi.customer_relative.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.customer_relative.domain.CustomerRelative;
import com.ruoyi.customer_relative.service.ICustomerRelativeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 客户联系人信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/customer_relative/customer_relative")
public class CustomerRelativeController extends BaseController
{
    @Autowired
    private ICustomerRelativeService customerRelativeService;

    /**
     * 查询客户联系人信息列表
     */
    @PreAuthorize("@ss.hasPermi('customer_relative:customer_relative:list')")
    @GetMapping("/list")
    public TableDataInfo list(CustomerRelative customerRelative)
    {
        startPage();
        List<CustomerRelative> list = customerRelativeService.selectCustomerRelativeList(customerRelative);
        return getDataTable(list);
    }

    /**
     * 导出客户联系人信息列表
     */
    @PreAuthorize("@ss.hasPermi('customer_relative:customer_relative:export')")
    @Log(title = "客户联系人信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CustomerRelative customerRelative)
    {
        List<CustomerRelative> list = customerRelativeService.selectCustomerRelativeList(customerRelative);
        ExcelUtil<CustomerRelative> util = new ExcelUtil<CustomerRelative>(CustomerRelative.class);
        util.exportExcel(response, list, "客户联系人信息数据");
    }

    /**
     * 获取客户联系人信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('customer_relative:customer_relative:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(customerRelativeService.selectCustomerRelativeById(id));
    }

    /**
     * 新增客户联系人信息
     */
    @PreAuthorize("@ss.hasPermi('customer_relative:customer_relative:add')")
    @Log(title = "客户联系人信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CustomerRelative customerRelative)
    {
        return toAjax(customerRelativeService.insertCustomerRelative(customerRelative));
    }

    /**
     * 修改客户联系人信息
     */
    @PreAuthorize("@ss.hasPermi('customer_relative:customer_relative:edit')")
    @Log(title = "客户联系人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CustomerRelative customerRelative)
    {
        return toAjax(customerRelativeService.updateCustomerRelative(customerRelative));
    }

    /**
     * 删除客户联系人信息
     */
    @PreAuthorize("@ss.hasPermi('customer_relative:customer_relative:remove')")
    @Log(title = "客户联系人信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(customerRelativeService.deleteCustomerRelativeByIds(ids));
    }
}
