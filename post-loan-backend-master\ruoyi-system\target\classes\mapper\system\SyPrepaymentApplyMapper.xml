<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SyPrepaymentApplyMapper">
    
    <resultMap type="SyPrepaymentApply" id="SyPrepaymentApplyResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="syApplyId"    column="sy_apply_id"    />
        <result property="prepaymentType"    column="prepayment_type"    />
        <result property="type"    column="type"    />
        <result property="repayDate"    column="repay_date"    />
        <result property="capitalArrears"    column="capital_arrears"    />
        <result property="interestArrears"    column="interest_arrears"    />
        <result property="costArrears"    column="cost_arrears"    />
        <result property="lastCapital"    column="last_capital"    />
        <result property="liquidateDamages"    column="liquidate_damages"    />
        <result property="currentInterest"    column="current_interest"    />
        <result property="bondAmt"    column="bond_amt"    />
        <result property="leaseAmt"    column="lease_amt"    />
        <result property="refundAmt"    column="refund_amt"    />
        <result property="repaymentAmount"    column="repayment_amount"    />
        <result property="createDate"    column="create_date"    />
    </resultMap>

    <sql id="selectSyPrepaymentApplyVo">
        select id, apply_id, sy_apply_id, prepayment_type, type, repay_date, capital_arrears, interest_arrears, cost_arrears, last_capital, liquidate_damages, current_interest, bond_amt, lease_amt, refund_amt, repayment_amount, create_date from sy_prepayment_apply
    </sql>

    <select id="selectSyPrepaymentApplyList" parameterType="SyPrepaymentApply" resultMap="SyPrepaymentApplyResult">
        <include refid="selectSyPrepaymentApplyVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="syApplyId != null  and syApplyId != ''"> and sy_apply_id = #{syApplyId}</if>
            <if test="prepaymentType != null  and prepaymentType != ''"> and prepayment_type = #{prepaymentType}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="repayDate != null  and repayDate != ''"> and repay_date = #{repayDate}</if>
        </where>
    </select>
    
    <select id="selectSyPrepaymentApplyLists" parameterType="SyPrepaymentApply" resultMap="SyPrepaymentApplyResult">
        <include refid="selectSyPrepaymentApplyVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="syApplyId != null  and syApplyId != ''"> and sy_apply_id = #{syApplyId}</if>
            <if test="prepaymentType != null  and prepaymentType != ''"> and prepayment_type = #{prepaymentType}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="repayDate != null  and repayDate != ''"> and repay_date = #{repayDate}</if>
        </where>
        limit 1
    </select>
    
    <select id="selectSyPrepaymentApplyById" parameterType="String" resultMap="SyPrepaymentApplyResult">
        <include refid="selectSyPrepaymentApplyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSyPrepaymentApply" parameterType="SyPrepaymentApply" useGeneratedKeys="true" keyProperty="id">
        insert into sy_prepayment_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyId != null">apply_id,</if>
            <if test="syApplyId != null">sy_apply_id,</if>
            <if test="prepaymentType != null">prepayment_type,</if>
            <if test="type != null">type,</if>
            <if test="repayDate != null">repay_date,</if>
            <if test="capitalArrears != null">capital_arrears,</if>
            <if test="interestArrears != null">interest_arrears,</if>
            <if test="costArrears != null">cost_arrears,</if>
            <if test="lastCapital != null">last_capital,</if>
            <if test="liquidateDamages != null">liquidate_damages,</if>
            <if test="currentInterest != null">current_interest,</if>
            <if test="bondAmt != null">bond_amt,</if>
            <if test="leaseAmt != null">lease_amt,</if>
            <if test="refundAmt != null">refund_amt,</if>
            <if test="repaymentAmount != null">repayment_amount,</if>
            <if test="createDate != null">create_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyId != null">#{applyId},</if>
            <if test="syApplyId != null">#{syApplyId},</if>
            <if test="prepaymentType != null">#{prepaymentType},</if>
            <if test="type != null">#{type},</if>
            <if test="repayDate != null">#{repayDate},</if>
            <if test="capitalArrears != null">#{capitalArrears},</if>
            <if test="interestArrears != null">#{interestArrears},</if>
            <if test="costArrears != null">#{costArrears},</if>
            <if test="lastCapital != null">#{lastCapital},</if>
            <if test="liquidateDamages != null">#{liquidateDamages},</if>
            <if test="currentInterest != null">#{currentInterest},</if>
            <if test="bondAmt != null">#{bondAmt},</if>
            <if test="leaseAmt != null">#{leaseAmt},</if>
            <if test="refundAmt != null">#{refundAmt},</if>
            <if test="repaymentAmount != null">#{repaymentAmount},</if>
            <if test="createDate != null">#{createDate},</if>
         </trim>
    </insert>

    <update id="updateSyPrepaymentApply" parameterType="SyPrepaymentApply">
        update sy_prepayment_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyId != null">apply_id = #{applyId},</if>
            <if test="syApplyId != null">sy_apply_id = #{syApplyId},</if>
            <if test="prepaymentType != null">prepayment_type = #{prepaymentType},</if>
            <if test="type != null">type = #{type},</if>
            <if test="repayDate != null">repay_date = #{repayDate},</if>
            <if test="capitalArrears != null">capital_arrears = #{capitalArrears},</if>
            <if test="interestArrears != null">interest_arrears = #{interestArrears},</if>
            <if test="costArrears != null">cost_arrears = #{costArrears},</if>
            <if test="lastCapital != null">last_capital = #{lastCapital},</if>
            <if test="liquidateDamages != null">liquidate_damages = #{liquidateDamages},</if>
            <if test="currentInterest != null">current_interest = #{currentInterest},</if>
            <if test="bondAmt != null">bond_amt = #{bondAmt},</if>
            <if test="leaseAmt != null">lease_amt = #{leaseAmt},</if>
            <if test="refundAmt != null">refund_amt = #{refundAmt},</if>
            <if test="repaymentAmount != null">repayment_amount = #{repaymentAmount},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSyPrepaymentApplyById" parameterType="String">
        delete from sy_prepayment_apply where id = #{id}
    </delete>

    <delete id="deleteSyPrepaymentApplyByIds" parameterType="String">
        delete from sy_prepayment_apply where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 