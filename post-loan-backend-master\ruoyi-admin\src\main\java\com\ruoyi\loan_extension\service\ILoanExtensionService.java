package com.ruoyi.loan_extension.service;

import java.util.List;
import com.ruoyi.loan_extension.domain.LoanExtension;

/**
 * 流程延期申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface ILoanExtensionService {
    /**
     * 查询流程延期申请
     * 
     * @param id 流程延期申请主键
     * @return 流程延期申请
     */
    public LoanExtension selectLoanExtensionById(Integer id);

    /**
     * 查询流程延期申请列表
     * 
     * @param loanExtension 流程延期申请
     * @return 流程延期申请集合
     */
    public List<LoanExtension> selectLoanExtensionList(LoanExtension loanExtension);

    /**
     * 新增流程延期申请
     * 
     * @param loanExtension 流程延期申请
     * @return 结果
     */
    public int insertLoanExtension(LoanExtension loanExtension);

    /**
     * 修改流程延期申请
     * 
     * @param loanExtension 流程延期申请
     * @return 结果
     */
    public int updateLoanExtension(LoanExtension loanExtension);

    /**
     * 批量删除流程延期申请
     * 
     * @param ids 需要删除的流程延期申请主键集合
     * 
     * @return 结果
     */
    public int deleteLoanExtensionByIds(Integer[] ids);

    /**
     * 删除流程延期申请信息
     * 
     * @param id 流程延期申请主键
     * @return 结果
     */
    public int deleteLoanExtensionById(Integer id);
}
