package com.ruoyi.car_order.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.car_order.mapper.CarOrderMapper;
import com.ruoyi.car_order.domain.CarOrder;
import com.ruoyi.car_order.service.ICarOrderService;

/**
 * 找车订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
public class CarOrderServiceImpl implements ICarOrderService 
{
    @Autowired
    private CarOrderMapper carOrderMapper;

    /**
     * 查询找车订单
     * 
     * @param id 找车订单主键
     * @return 找车订单
     */
    @Override
    public CarOrder selectCarOrderById(String id)
    {
        return carOrderMapper.selectCarOrderById(id);
    }

    /**
     * 查询找车订单列表
     * 
     * @param carOrder 找车订单
     * @return 找车订单
     */
    @Override
    public List<CarOrder> selectCarOrderList(CarOrder carOrder)
    {
        return carOrderMapper.selectCarOrderList(carOrder);
    }

    /**
     * 新增找车订单
     * 
     * @param carOrder 找车订单
     * @return 结果
     */
    @Override
    public int insertCarOrder(CarOrder carOrder)
    {
        return carOrderMapper.insertCarOrder(carOrder);
    }

    /**
     * 修改找车订单
     * 
     * @param carOrder 找车订单
     * @return 结果
     */
    @Override
    public int updateCarOrder(CarOrder carOrder)
    {
        return carOrderMapper.updateCarOrder(carOrder);
    }

    /**
     * 批量删除找车订单
     * 
     * @param ids 需要删除的找车订单主键
     * @return 结果
     */
    @Override
    public int deleteCarOrderByIds(String[] ids)
    {
        return carOrderMapper.deleteCarOrderByIds(ids);
    }

    /**
     * 删除找车订单信息
     * 
     * @param id 找车订单主键
     * @return 结果
     */
    @Override
    public int deleteCarOrderById(String id)
    {
        return carOrderMapper.deleteCarOrderById(id);
    }

    @Override
    public void updateDispatcherByLoanId(Long loanId, Integer dispatcher) {
        carOrderMapper.updateDispatcherByLoanId(loanId, dispatcher);
    }
}
