package com.ruoyi.car_order.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 找车订单对象 car_order
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
public class CarOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 创建时间 */
    private Date createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyNo;

    /** 流程id */
    @Excel(name = "流程id")
    private Long LoanId;

    /** 找车团队 */
    @Excel(name = "找车团队")
    private Long teamId;

    /** 车库id */
    @Excel(name = "车库id")
    private Long garageId;

    /** 1-入库，2-出库 */
    @Excel(name = "1-入库，2-出库")
    private Integer libraryStatus;

    /** 入库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入库时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date inboundTime;

    /** 出库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出库时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date outboundTime;

    /** 找车佣金 */
    @Excel(name = "找车佣金")
    private BigDecimal locatingCommission;

    /** GPS状态 */
    @Excel(name = "GPS状态")
    private Integer GPS;

    /** 钥匙状态：1-已邮寄，2-已收回，3-未归还 */
    @Excel(name = "钥匙状态：1-已邮寄，2-已收回，3-未归还")
    private Long keyStatus;

    /** 钥匙邮寄时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "钥匙邮寄时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date keyTime;

    /** 收车方式：1-主动交车，2-钥匙开车，3-板车拖车 */
    @Excel(name = "收车方式：1-主动交车，2-钥匙开车，3-板车拖车")
    private Integer collectionMethod;

    /** 订单状态，0-发起，1-已分配，2-已完成，3-未完成，4-已撤销 */
    @Excel(name = "订单状态，0-发起，1-已分配，2-已完成，3-未完成，4-已撤销")
    private Integer status;

    /** 省 */
    @Excel(name = "省")
    private String keyProvince;
    /** 市 */
    @Excel(name = "市")
    private String keyCity;
    /** 区 */
    @Excel(name = "区")
    private String keyBorough;
    /** 地址 */
    @Excel(name = "地址")
    private String keyAddress;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String keyDetailAddress;

    /** 分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "分配时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date allocationTime;

    /** 派单员 1-贷后文员 2-法诉文员 */
    private Integer dispatcher;

    public void setKeyProvince(String keyProvince)  { this.keyProvince = keyProvince;}
    public String getKeyProvince()
    {
        return keyProvince;
    }

    public void setKeyCity(String keyCity)  { this.keyCity = keyCity;}
    public String getKeyCity()
    {
        return keyCity;
    }

    public void setKeyBorough(String keyBorough)  { this.keyBorough = keyBorough;}
    public String getKeyBorough()
    {
        return keyBorough;
    }

    public void setKeyAddress(String keyAddress)  { this.keyAddress = keyAddress;}
    public String getKeyAddress()  { return keyAddress;}

    public void setKeyDetailAddress(String keyDetailAddress)  { this.keyDetailAddress = keyDetailAddress;}
    public String getKeyDetailAddress()  { return keyDetailAddress;}

    public void setDispatcher(Integer dispatcher) { this.dispatcher = dispatcher; }
    public Integer getDispatcher() { return dispatcher; }


    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }

    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    public void setApplyNo(String applyNo)
    {
        this.applyNo = applyNo;
    }

    public String getApplyNo()
    {
        return applyNo;
    }

    public void setLoanId(Long LoanId) {this.LoanId = LoanId;}

    public Long getLoanId(){return LoanId;}

    public void setTeamId(Long teamId) 
    {
        this.teamId = teamId;
    }

    public Long getTeamId() 
    {
        return teamId;
    }

    public void setGarageId(Long garageId) 
    {
        this.garageId = garageId;
    }

    public Long getGarageId() 
    {
        return garageId;
    }

    public void setLibraryStatus(Integer libraryStatus) 
    {
        this.libraryStatus = libraryStatus;
    }

    public Integer getLibraryStatus() 
    {
        return libraryStatus;
    }

    public void setInboundTime(Date inboundTime) 
    {
        this.inboundTime = inboundTime;
    }

    public Date getInboundTime() 
    {
        return inboundTime;
    }

    public void setOutboundTime(Date outboundTime) 
    {
        this.outboundTime = outboundTime;
    }

    public Date getOutboundTime() 
    {
        return outboundTime;
    }

    public void setLocatingCommission(BigDecimal locatingCommission) 
    {
        this.locatingCommission = locatingCommission;
    }

    public BigDecimal getLocatingCommission() 
    {
        return locatingCommission;
    }

    public void setGPS(Integer GPS) 
    {
        this.GPS = GPS;
    }

    public Integer getGPS() 
    {
        return GPS;
    }

    public void setKeyStatus(Long keyStatus) 
    {
        this.keyStatus = keyStatus;
    }

    public Long getKeyStatus() 
    {
        return keyStatus;
    }

    public void setKeyTime(Date keyTime) 
    {
        this.keyTime = keyTime;
    }

    public Date getKeyTime() 
    {
        return keyTime;
    }

    public void setCollectionMethod(Integer collectionMethod) 
    {
        this.collectionMethod = collectionMethod;
    }

    public Integer getCollectionMethod() 
    {
        return collectionMethod;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setAllocationTime(Date allocationTime) 
    {
        this.allocationTime = allocationTime;
    }

    public Date getAllocationTime() 
    {
        return allocationTime;
    }



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("createBy", getCreateBy())
            .append("createDate", getCreateDate())
            .append("updateBy", getUpdateBy())
            .append("updateDate", getUpdateDate())
            .append("applyNo", getApplyNo())
            .append("teamId", getTeamId())
            .append("garageId", getGarageId())
            .append("libraryStatus", getLibraryStatus())
            .append("inboundTime", getInboundTime())
            .append("outboundTime", getOutboundTime())
            .append("locatingCommission", getLocatingCommission())
            .append("GPS", getGPS())
            .append("keyStatus", getKeyStatus())
            .append("keyTime", getKeyTime())
            .append("collectionMethod", getCollectionMethod())
            .append("status", getStatus())
            .append("allocationTime", getAllocationTime())
            .append("dispatcher", getDispatcher())
                .append("keyProvince", getKeyProvince())
                .append("keyCity", getKeyCity())
                .append("keyBorough", getKeyBorough())
                .append("keyAddress", getKeyAddress())
                .append("keyDetailAddress", getKeyDetailAddress())
            .toString();
    }
}
