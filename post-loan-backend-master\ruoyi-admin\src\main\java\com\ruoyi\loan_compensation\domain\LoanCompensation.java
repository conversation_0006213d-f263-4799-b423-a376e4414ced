package com.ruoyi.loan_compensation.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.trial_balance.domain.TrialBalance;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.EqualsAndHashCode;

/**
 * 代偿对象 loan_compensation
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LoanCompensation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;
    private TrialBalance trialBalance;
    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applyId;

    /** 流程id */
    @Excel(name = "流程id")
    private Long loanId;

    /** 客户ID */
    @Excel(name = "贷款人ID")
    private String customerId;

    /** 贷款人 */
    @Excel(name = "贷款人")
    private String customerName;

    /** 业务员 */
    @Excel(name = "业务员")
    private String salesman;

    /** 出单渠道 */
    @Excel(name = "出单渠道")
    private String orgName;

    /** 银行id */
    @Excel(name = "银行id")
    private String partnerId;

    /** 放款银行 */
    @Excel(name = "放款银行")
    private String bank;

    /** 放款金额 */
    @Excel(name = "放款金额")
    private BigDecimal loanAmount;

    /** 其他欠款 */
    @Excel(name = "其他欠款")
    private BigDecimal otherDebt;

    /** 总结清金额 */
    @Excel(name = "总结清金额")
    private BigDecimal totalMoney;

    /** 0-跟催员发起，1-贷后试算，2-跟催员提交凭据，3-同意，4-拒绝，5-核对完成 */
    @Excel(name = "0-跟催员发起，1-贷后试算，2-跟催员提交凭据，3-同意，4-拒绝，5-核对完成")
    private Integer examineStatus;

    /** 拒绝理由 */
    @Excel(name = "拒绝理由")
    private String reason;

    /** 创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 修改日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 风险金比例 */
    @Excel(name = "风险金比例")
    private Long fxjProportion;

    /** 渠道比例 */
    @Excel(name = "渠道比例")
    private Long qdProportion;

    /** 广明借比例 */
    @Excel(name = "广明借比例")
    private Long gmjProportion;

    /** 科技借比例 */
    @Excel(name = "科技借比例")
    private Long kjjProportion;

    /** 科技出资比例 */
    @Excel(name = "科技出资比例")
    private Long kjczProportion;

    /** 首邦出资比例 */
    @Excel(name = "首邦出资比例")
    private Long sbczProportion;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal fxjMoney;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal qdMoney;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal gmjMoney;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal kjjMoney;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal kjczMoney;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal sbczMoney;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fxjAccount;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String qdAccount;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String gmjAccount;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String kjjAccount;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String kjczAccount;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String sbczAccount;

    /** 图片字段 */
    @Excel(name = "图片")
    private String image;

    /** 打款金额 */
    @Excel(name = "打款金额")
    private BigDecimal payouts;

    /** 打款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "打款时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date payoutsTime;

    /** 审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审批时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

}
