package com.ruoyi.litigation_cost_submission.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.litigation_cost_submission.domain.LitigationCostSubmissionLimit;
import com.ruoyi.litigation_cost_submission.service.ILitigationCostSubmissionLimitService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 法诉费用提交限制记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/litigation_cost_submission/litigation_cost_submission")
public class LitigationCostSubmissionLimitController extends BaseController
{
    @Autowired
    private ILitigationCostSubmissionLimitService litigationCostSubmissionLimitService;

    /**
     * 查询法诉费用提交限制记录列表
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_submission:litigation_cost_submission:list')")
    @GetMapping("/list")
    public TableDataInfo list(LitigationCostSubmissionLimit litigationCostSubmissionLimit)
    {
        startPage();
        List<LitigationCostSubmissionLimit> list = litigationCostSubmissionLimitService.selectLitigationCostSubmissionLimitList(litigationCostSubmissionLimit);
        return getDataTable(list);
    }

    /**
     * 导出法诉费用提交限制记录列表
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_submission:litigation_cost_submission:export')")
    @Log(title = "法诉费用提交限制记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LitigationCostSubmissionLimit litigationCostSubmissionLimit)
    {
        List<LitigationCostSubmissionLimit> list = litigationCostSubmissionLimitService.selectLitigationCostSubmissionLimitList(litigationCostSubmissionLimit);
        ExcelUtil<LitigationCostSubmissionLimit> util = new ExcelUtil<LitigationCostSubmissionLimit>(LitigationCostSubmissionLimit.class);
        util.exportExcel(response, list, "法诉费用提交限制记录数据");
    }

    /**
     * 获取法诉费用提交限制记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_submission:litigation_cost_submission:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(litigationCostSubmissionLimitService.selectLitigationCostSubmissionLimitById(id));
    }

    /**
     * 新增法诉费用提交限制记录
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_submission:litigation_cost_submission:add')")
    @Log(title = "法诉费用提交限制记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LitigationCostSubmissionLimit litigationCostSubmissionLimit)
    {
        return toAjax(litigationCostSubmissionLimitService.insertLitigationCostSubmissionLimit(litigationCostSubmissionLimit));
    }

    /**
     * 修改法诉费用提交限制记录
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_submission:litigation_cost_submission:edit')")
    @Log(title = "法诉费用提交限制记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LitigationCostSubmissionLimit litigationCostSubmissionLimit)
    {
        return toAjax(litigationCostSubmissionLimitService.updateLitigationCostSubmissionLimit(litigationCostSubmissionLimit));
    }

    /**
     * 删除法诉费用提交限制记录
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_submission:litigation_cost_submission:remove')")
    @Log(title = "法诉费用提交限制记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(litigationCostSubmissionLimitService.deleteLitigationCostSubmissionLimitByIds(ids));
    }
}
