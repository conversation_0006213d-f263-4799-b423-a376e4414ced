package com.ruoyi.litigation_cost_approval.service;

import java.util.List;
import com.ruoyi.litigation_cost_approval.domain.LitigationCostApproval;

/**
 * 法诉费用审批Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface ILitigationCostApprovalService 
{
    /**
     * 查询法诉费用审批列表（显示法诉记录，每个费用取最新的展示）
     * 
     * @param litigationCostApproval 法诉费用审批
     * @return 法诉费用审批集合
     */
    public List<LitigationCostApproval> selectLitigationCostApprovalList(LitigationCostApproval litigationCostApproval);

    /**
     * 查询法诉费用审批
     * 
     * @param id 法诉费用审批主键
     * @return 法诉费用审批
     */
    public LitigationCostApproval selectLitigationCostApprovalById(Long id);

    /**
     * 根据法诉案件ID查询费用提交记录详情
     * 
     * @param litigationCaseId 法诉案件ID
     * @return 费用提交记录列表
     */
    public List<LitigationCostApproval> selectSubmissionRecordsByLitigationCaseId(Long litigationCaseId);

    /**
     * 新增法诉费用审批
     * 
     * @param litigationCostApproval 法诉费用审批
     * @return 结果
     */
    public int insertLitigationCostApproval(LitigationCostApproval litigationCostApproval);

    /**
     * 修改法诉费用审批
     * 
     * @param litigationCostApproval 法诉费用审批
     * @return 结果
     */
    public int updateLitigationCostApproval(LitigationCostApproval litigationCostApproval);

    /**
     * 批量删除法诉费用审批
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLitigationCostApprovalByIds(Long[] ids);

    /**
     * 删除法诉费用审批信息
     * 
     * @param id 法诉费用审批主键
     * @return 结果
     */
    public int deleteLitigationCostApprovalById(Long id);

    /**
     * 单个审批费用记录
     * 
     * @param id 记录ID
     * @param approvalStatus 审批状态
     * @param reasons 拒绝原因
     * @return 结果
     */
    public int approveLitigationCostRecord(Long id, String approvalStatus, String reasons);

    /**
     * 批量审批费用记录
     * 
     * @param ids 记录ID列表
     * @param approvalStatus 审批状态
     * @param reasons 拒绝原因
     * @return 结果
     */
    public int batchApproveLitigationCostRecords(List<Long> ids, String approvalStatus, String reasons);

    /**
     * 获取审批状态统计
     * 
     * @return 统计结果
     */
    public List<java.util.Map<String, Object>> getApprovalStatistics();

    /**
     * 查询待审批记录数量
     * 
     * @return 待审批记录数量
     */
    public int countPendingApproval();
}
