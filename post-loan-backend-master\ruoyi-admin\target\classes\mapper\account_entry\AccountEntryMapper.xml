<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.account_entry.mapper.AccountEntryMapper">
    
    <resultMap type="AccountEntry" id="AccountEntryResult">
        <result property="id"    column="id"    />
        <result property="loanId"    column="loan_id"    />
        <result property="loanCompensationId"    column="loan_compensation_id"    />
        <result property="amountType"    column="amount_type"    />
        <result property="entryTime"    column="entry_time"    />
        <result property="actualAmount"    column="actual_amount"    />
        <result property="transferAccount"    column="transfer_account"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="amountDue"    column="amount_due"    />
        <result property="difference"    column="difference"    />
    </resultMap>

    <sql id="selectAccountEntryVo">
        select id, loan_id, loan_compensation_id, amount_type, entry_time, actual_amount, transfer_account, create_date, update_date, create_by, update_by, amount_due, difference from account_entry
    </sql>

    <select id="selectAccountEntryList" parameterType="AccountEntry" resultMap="AccountEntryResult">
        <include refid="selectAccountEntryVo"/>
        <where>  
            <if test="loanId != null "> and loan_id = #{loanId}</if>
            <if test="loanCompensationId != null "> and loan_compensation_id = #{loanCompensationId}</if>
            <if test="amountType != null  and amountType != ''"> and amount_type = #{amountType}</if>
            <if test="entryTime != null "> and entry_time = #{entryTime}</if>
            <if test="actualAmount != null "> and actual_amount = #{actualAmount}</if>
            <if test="transferAccount != null  and transferAccount != ''"> and transfer_account = #{transferAccount}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="amountDue != null "> and amount_due = #{amountDue}</if>
            <if test="difference != null "> and difference = #{difference}</if>
        </where>
    </select>
    
    <select id="selectAccountEntryById" parameterType="Long" resultMap="AccountEntryResult">
        <include refid="selectAccountEntryVo"/>
        where id = #{id}
    </select>

    <insert id="insertAccountEntry" parameterType="AccountEntry" useGeneratedKeys="true" keyProperty="id">
        insert into account_entry
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="loanId != null">loan_id,</if>
            <if test="loanCompensationId != null">loan_compensation_id,</if>
            <if test="amountType != null and amountType != ''">amount_type,</if>
            <if test="entryTime != null">entry_time,</if>
            <if test="actualAmount != null">actual_amount,</if>
            <if test="transferAccount != null and transferAccount != ''">transfer_account,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="amountDue != null">amount_due,</if>
            <if test="difference != null">difference,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="loanId != null">#{loanId},</if>
            <if test="loanCompensationId != null">#{loanCompensationId},</if>
            <if test="amountType != null and amountType != ''">#{amountType},</if>
            <if test="entryTime != null">#{entryTime},</if>
            <if test="actualAmount != null">#{actualAmount},</if>
            <if test="transferAccount != null and transferAccount != ''">#{transferAccount},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="amountDue != null">#{amountDue},</if>
            <if test="difference != null">#{difference},</if>
         </trim>
    </insert>

    <insert id="batchInsertAccountEntry">
        insert into account_entry (
            loan_id, loan_compensation_id, amount_type, entry_time, actual_amount, transfer_account, create_date, update_date, create_by, update_by, amount_due, difference
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.loanId},
                #{item.loanCompensationId},
                #{item.amountType},
                #{item.entryTime},
                #{item.actualAmount},
                #{item.transferAccount},
                #{item.createDate},
                #{item.updateDate},
                #{item.createBy},
                #{item.updateBy},
                #{item.amountDue},
                #{item.difference}
            )
        </foreach>
    </insert>

    <update id="updateAccountEntry" parameterType="AccountEntry">
        update account_entry
        <trim prefix="SET" suffixOverrides=",">
            <if test="loanId != null">loan_id = #{loanId},</if>
            <if test="loanCompensationId != null">loan_compensation_id = #{loanCompensationId},</if>
            <if test="amountType != null and amountType != ''">amount_type = #{amountType},</if>
            <if test="entryTime != null">entry_time = #{entryTime},</if>
            <if test="actualAmount != null">actual_amount = #{actualAmount},</if>
            <if test="transferAccount != null and transferAccount != ''">transfer_account = #{transferAccount},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="amountDue != null">amount_due = #{amountDue},</if>
            <if test="difference != null">difference = #{difference},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAccountEntryById" parameterType="Long">
        delete from account_entry where id = #{id}
    </delete>

    <delete id="deleteAccountEntryByIds" parameterType="String">
        delete from account_entry where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countByLoanCompensationIdAndAmountType" resultType="int">
        select count(1) from account_entry 
        where loan_compensation_id = #{loanCompensationId} 
        and amount_type = #{amountType}
    </select>
</mapper>