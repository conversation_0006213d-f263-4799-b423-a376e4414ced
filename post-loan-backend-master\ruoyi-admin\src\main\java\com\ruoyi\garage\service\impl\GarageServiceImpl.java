package com.ruoyi.garage.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.garage.mapper.GarageMapper;
import com.ruoyi.garage.domain.Garage;
import com.ruoyi.garage.service.IGarageService;

/**
 * 车库Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
public class GarageServiceImpl implements IGarageService 
{
    @Autowired
    private GarageMapper garageMapper;

    /**
     * 查询车库
     * 
     * @param id 车库主键
     * @return 车库
     */
    @Override
    public Garage selectGarageById(Integer id)
    {
        return garageMapper.selectGarageById(id);
    }

    /**
     * 查询车库列表
     * 
     * @param garage 车库
     * @return 车库
     */
    @Override
    public List<Garage> selectGarageList(Garage garage)
    {
        return garageMapper.selectGarageList(garage);
    }

    /**
     * 新增车库
     * 
     * @param garage 车库
     * @return 结果
     */
    @Override
    public int insertGarage(Garage garage)
    {
        return garageMapper.insertGarage(garage);
    }

    /**
     * 修改车库
     * 
     * @param garage 车库
     * @return 结果
     */
    @Override
    public int updateGarage(Garage garage)
    {
        return garageMapper.updateGarage(garage);
    }

    /**
     * 批量删除车库
     * 
     * @param ids 需要删除的车库主键
     * @return 结果
     */
    @Override
    public int deleteGarageByIds(Integer[] ids)
    {
        return garageMapper.deleteGarageByIds(ids);
    }

    /**
     * 删除车库信息
     * 
     * @param id 车库主键
     * @return 结果
     */
    @Override
    public int deleteGarageById(Integer id)
    {
        return garageMapper.deleteGarageById(id);
    }
}
