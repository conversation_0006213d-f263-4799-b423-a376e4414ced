<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.JJWxRepayPlanMapper">
    
    <resultMap type="JJWxRepayPlan" id="WxRepayPlanResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="period"    column="period"    />
        <result property="repayDate"    column="repay_date"    />
        <result property="actualRepayDate"    column="actual_repay_date"    />
        <result property="repayAmount"    column="repay_amount"    />
        <result property="actualRepayAmount"    column="actual_repay_amount"    />
        <result property="capital"    column="capital"    />
        <result property="actualCapital"    column="actual_capital"    />
        <result property="interest"    column="interest"    />
        <result property="actualInterest"    column="actual_interest"    />
        <result property="defInterest"    column="def_interest"    />
        <result property="actualDefInterest"    column="actual_def_interest"    />
        <result property="balance"    column="balance"    />
        <result property="status"    column="status"    />
        <result property="buyBackStatus"    column="buy_back_status"    />
        <result property="buyBackCapital"    column="buy_back_capital"    />
        <result property="buyBackInterest"    column="buy_back_interest"    />
        <result property="flag"    column="flag"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
    </resultMap>

    <sql id="selectWxRepayPlanVo">
        select * from wx_repay_plan
    </sql>

    <select id="selectWxRepayPlanList" parameterType="JJWxRepayPlan" resultMap="WxRepayPlanResult">
        <include refid="selectWxRepayPlanVo"/>
        <where>  
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="repayDate != null  and repayDate != ''"> and repay_date = #{repayDate}</if>
            <if test="actualRepayDate != null  and actualRepayDate != ''"> and actual_repay_date = #{actualRepayDate}</if>
            <if test="repayAmount != null "> and repay_amount = #{repayAmount}</if>
            <if test="actualRepayAmount != null "> and actual_repay_amount = #{actualRepayAmount}</if>
            <if test="capital != null "> and capital = #{capital}</if>
            <if test="actualCapital != null "> and actual_capital = #{actualCapital}</if>
            <if test="interest != null "> and interest = #{interest}</if>
            <if test="actualInterest != null "> and actual_interest = #{actualInterest}</if>
            <if test="defInterest != null "> and def_interest = #{defInterest}</if>
            <if test="actualDefInterest != null "> and actual_def_interest = #{actualDefInterest}</if>
            <if test="balance != null "> and balance = #{balance}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="buyBackStatus != null  and buyBackStatus != ''"> and buy_back_status = #{buyBackStatus}</if>
            <if test="buyBackCapital != null "> and buy_back_capital = #{buyBackCapital}</if>
            <if test="buyBackInterest != null "> and buy_back_interest = #{buyBackInterest}</if>
            <if test="flag != null  and flag != ''"> and flag = #{flag}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
    </select>

    <select id="selectWxRepayPlanListGroup" parameterType="JJWxRepayPlan" resultMap="WxRepayPlanResult">
        <include refid="selectWxRepayPlanVo"/>
        <where>
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="repayDate != null  and repayDate != ''"> and repay_date = #{repayDate}</if>
            <if test="actualRepayDate != null  and actualRepayDate != ''"> and actual_repay_date = #{actualRepayDate}</if>
            <if test="repayAmount != null "> and repay_amount = #{repayAmount}</if>
            <if test="actualRepayAmount != null "> and actual_repay_amount = #{actualRepayAmount}</if>
            <if test="capital != null "> and capital = #{capital}</if>
            <if test="actualCapital != null "> and actual_capital = #{actualCapital}</if>
            <if test="interest != null "> and interest = #{interest}</if>
            <if test="actualInterest != null "> and actual_interest = #{actualInterest}</if>
            <if test="defInterest != null "> and def_interest = #{defInterest}</if>
            <if test="actualDefInterest != null "> and actual_def_interest = #{actualDefInterest}</if>
            <if test="balance != null "> and balance = #{balance}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="buyBackStatus != null  and buyBackStatus != ''"> and buy_back_status = #{buyBackStatus}</if>
            <if test="buyBackCapital != null "> and buy_back_capital = #{buyBackCapital}</if>
            <if test="buyBackInterest != null "> and buy_back_interest = #{buyBackInterest}</if>
            <if test="flag != null  and flag != ''"> and flag = #{flag}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
        group by apply_id
    </select>

    <select id="selectWxRepayPlanListNow" parameterType="String" resultMap="WxRepayPlanResult">
        <include refid="selectWxRepayPlanVo"/>
        where apply_id = #{applyId} and  YEAR(repay_date) = YEAR(CURDATE())  and MONTH(repay_date) = MONTH(CURDATE()) limit 1
    </select>

</mapper>